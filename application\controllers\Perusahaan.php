<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class <PERSON><PERSON>haan extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if($this->session->userdata('logged_in') == FALSE ){
      redirect('login');  
    }
    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('ModelPerusahaan'));
  }

  public function index()
  {
    $data=array(      
      'listkat'        => $this->ModelPerusahaan->tampilKategori(),
      'datakat'        => $this->ModelPerusahaan->dataKategori(),
      'isi'            => 'Perusahaan/Index'
    );
    $this->load->view('Layout/Wrapper',$data, FALSE);
  }

  public function modalTambahPerusahaan()
  {
    $this->load->view('Perusahaan/modal_tambah_perusahaan');
  }

  public function getKategori()
  {
    $result = $this->ModelPerusahaan->listKategori()->result_array();
    $data = array();
    foreach ($result as $row) 
    {
      $sub_array = array();
      $sub_array['id'] = $row['ID_KATEGORI'];
      $sub_array['text'] = $row['KATEGORI'];
      $data[] = $sub_array;
    }

    echo json_encode($data);
  }

  public function getlistPerusahaan()
  {
    $draw   = intval($this->input->POST("draw"));
    $start  = intval($this->input->POST("start"));
    $length = intval($this->input->POST("length"));

    $dataperusahaan = $this->ModelPerusahaan->listPerusahaan();
    $data=array();
    $no =1;
    foreach ($dataperusahaan->result() as $field) {

      // $edit = '<button type="button" id="btn-edit" data-toggle="modal" data-target="#modal_edit_perusahaan" class="btn btn-success btn-sm" data-id="'.$field->ID_PERUSAHAAN.'"><i class="fa fa-edit"></i> Edit</button>';

      // $hapus = '<button type="button" class="btn btn-danger btn-sm" title="hapus data" data-id="'.$field->ID_PERUSAHAAN.'"><i class="fa fa-trash"></i> Hapus</button>';

      $edit = '<button type="button" class="btn btn-success btn-sm" title="edit data" onclick="editperusahaan('.$field->ID_PERUSAHAAN.')" style="width:72px"><i class="fa fa-edit"></i> Edit</button>';

      $hapus = '<button type="button" class="btn btn-danger btn-sm hapusperusahaan" href="javaScript:;" id="hapusperusahaan" title="hapus data" data="'.$field->ID_PERUSAHAAN.'" style="width:72px"><i class="fa fa-trash"></i> Hapus</button>';

      // $aksi = '<a class="btn btn-warning uploadSurat" href="javascript:;" data-id="'.$field->ID_PERUSAHAAN.'" data-bs-toggle="modal" data-bs-target="#modal_upload_sirs"> Upload</a>';

      $data[] = array(
        $no,
        $field->PERUSAHAAN,
        $edit.' '.$hapus
      );
      $no++;
    }

    $output = array(
      "draw"            => $draw,
      "recordsTotal"    => $dataperusahaan->num_rows(),
      "recordsFiltered" => $dataperusahaan->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }

  public function simpan($param)
  {
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'tambah' || $param == 'ubah') {
        $post = $this->input->post();
        // echo "<pre>";print_r($post);exit();
        $this->db->trans_begin();

        $tglcreate = date('Y-m-d H:i:s');
        // $now = time();

        $data_insert = array (
          // 'ID_KATEGORI'       => $this->input->post('KATEGORI'), 
          'PERUSAHAAN'        => $this->input->post('PERUSAHAAN'),
          'CREATED_BY'        => $this->session->userdata('id'),
          'CREATED_DATE'      => $tglcreate,
        );
   // echo "<pre>";print_r($data_insert);exit();

        $this->db->insert('db_pks.tb_perusahaan', $data_insert);

        if ($this->db->trans_status() === false) {
          $this->db->trans_rollback();
          $result = array('status' => 'failed');
        } else {
          $this->db->trans_commit();
          $result = array('status' => 'success');
        }
        echo json_encode($result);
      }
    }
  }  

  public function modalEditperusahaan()
  {
    if($this->input->is_ajax_request()==true){
      $id = $this->input->post('id');
      // $listkategori = $this->ModelPerusahaan->dataKategori();
      $ambildata = $this->ModelPerusahaan->editlistPerusahaan($id);
      if($ambildata->num_rows()>0){
        $row=$ambildata->row_array();
        $data=[
          'ID_PERUSAHAAN'     => $row['ID_PERUSAHAAN'],
          'ID_KATEGORI'       => $row['ID_KATEGORI'],
          'PERUSAHAAN'        => $row['PERUSAHAAN'],
          'KATEGORI'          => $row['KATEGORI'],
          'listkat'           => $this->ModelPerusahaan->tampilKategori(),
        ];
      }
      $msg=[
        'sukses'=>$this->load->view('Perusahaan/modal_edit_perusahaan', $data, true)
      ];
      echo json_encode($msg);
    }
  }

  public function UpdatePerusahaan($param)
  {
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'tambah' || $param == 'ubah') {
        $post = $this->input->post();
        // echo "<pre>";print_r($post);exit();
        $this->db->trans_begin();

        $tglupdate = date('Y-m-d H:i:s');

        $id = $this->input->post('ID');

        $data_insert = array (
          // 'ID_KATEGORI'      => $this->input->post('KATEGORI'), 
          'PERUSAHAAN'       => $this->input->post('PERUSAHAAN'),
          'UPDATED_BY'        => $this->session->userdata('id'),
          'UPDATED_DATE'      => $tglupdate,
        );
   // echo "<pre>";print_r($data_insert);exit();
        $this->db->where('db_pks.tb_perusahaan.ID_PERUSAHAAN',$id);
        $this->db->update('db_pks.tb_perusahaan',$data_insert);
        
        if ($this->db->trans_status() === false) {
          $this->db->trans_rollback();
          $result = array('status' => 'failed');
        } else {
          $this->db->trans_commit();
          $result = array('status' => 'success');
        }
        echo json_encode($result);
      }
    }
  }  

public function HapusPerusahaanX($param)
  {
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'tambah' || $param == 'ubah') {
        $post = $this->input->post();
        // echo "<pre>";print_r($post);exit();
        $this->db->trans_begin();

        $tglupdate = date('Y-m-d H:i:s');

        $id = $this->input->post('ID');

        $data_insert = array (
          'ID_KATEGORI'      => 0, 
          // 'UPDATE_BY'        => $this->session->userdata('id'),
          // 'UPDATE_DATE'      => $tglupdate,
        );
   // echo "<pre>";print_r($data_insert);exit();
        $this->db->where('db_pks.tb_perusahaan.ID_PERUSAHAAN',$id);
        $this->db->update('db_pks.tb_perusahaan',$data_insert);
        
        if ($this->db->trans_status() === false) {
          $this->db->trans_rollback();
          $result = array('status' => 'failed');
        } else {
          $this->db->trans_commit();
          $result = array('status' => 'success');
        }
        echo json_encode($result);
      }
    }
  }

    public function HapusPerusahaan()
    {
    $this->db->trans_begin();
    $post = $this->input->post();
    $id =$post['ID'];

    switch ($post['PAR']) {
      case 'nextOpen':
        $dataUpdate = array (
          'STATUS_TASK'                          => 2,
        );
        break;
      case 'prevProgress':
        $dataUpdate = array (
          'STATUS_TASK'                          => 1,
        );
        break;
      case 'nextProgress':
        $dataUpdate = array (
          'STATUS_TASK'                          => 3,
        );
        break;
      case 'prevReview':
        $dataUpdate = array (
          'STATUS_TASK'                          => 2,
        );
        break;
      case 'nextReview':
        $dataUpdate = array (
          'STATUS_TASK'                          => 4,
        );
        break;
      case 'prevClosed':
        $dataUpdate = array (
          'STATUS_TASK'                          => 3,
        );
        break;
      case 'deactivated':
        $dataUpdate = array (
          'STATUS'                           => 0,
        );
        break;
    }
    // echo "<pre>";print_r($id);exit();
        $this->db->where('db_pks.tb_perusahaan.ID_PERUSAHAAN', $id);
        $this->db->update('db_pks.tb_perusahaan', $dataUpdate);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
    }

}
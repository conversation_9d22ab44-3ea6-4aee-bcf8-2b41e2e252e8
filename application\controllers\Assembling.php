<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Assembling extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if($this->session->userdata('logged_in') == FALSE ){
      redirect('login');  
    }
    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('ModelAssembling'));
  }

  public function index()
  {
    $data=array(      
      // 'listkat'        => $this->ModelInput->tampilKategori(),
      // 'listperusahaan' => $this->ModelInput->listPerusahaan(),
      // 'listno'         => $this->ModelInput->listnoPks(),
      'isi'            => 'Assembling/Index'
    );
    $this->load->view('Layout/Wrapper',$data);
  }

  public function index_assri()
  {
    $data=array(      
      // 'listkat'        => $this->ModelInput->tampilKategori(),
      // 'listperusahaan' => $this->ModelInput->listPerusahaan(),
      // 'listno'         => $this->ModelInput->listnoPks(),
      'isi'            => 'Assembling_ri/Index'
    );
    $this->load->view('Layout/Wrapper',$data);
  }

  public function index_assrj()
  {
    $data=array(      
      // 'listkat'        => $this->ModelInput->tampilKategori(),
      // 'listperusahaan' => $this->ModelInput->listPerusahaan(),
      // 'listno'         => $this->ModelInput->listnoPks(),
      'isi'            => 'Assembling_rj/Index'
    );
    $this->load->view('Layout/Wrapper',$data);
  }

  public function CekNomr1()
{
    $nomr = $this->input->post('NOMR');

    $cekmr = $this->ModelAssembling->cek_nomr($nomr);
    
    if ($cekmr->num_rows() > 0) {
        $result = $cekmr->row_array();
        $row = array(
            'nomr' => $result['NORM'],
            'nama' => $result['NAMA']
        );
    } else {
        $row = array(
            'nomr' => '',
            'nama' => 'Data tidak ditemukan'
        );
    }

    echo json_encode($row);
}

public function CekNomr()
{
  $nomr = $this->input->post('NOMR');
  $cekmr = $this->ModelAssembling->cek_nomr($nomr);

  if ($cekmr->num_rows() > 0) {
    $result = $cekmr->result_array();
    $data = array(
            'nomr' => $result[0]['NORM'], // Asumsi NORM sama untuk semua kunjungan
            'nama' => $result[0]['NAMA'], // Asumsi NAMA sama untuk semua kunjungan
            'kunjungan' => array(),
            'ruangan' => array(),
            'id_ruangan' => array()
          );

    foreach ($result as $row) {
      $data['kunjungan'][] = $row['TANGGAL'];
      $data['ruangan'][] = $row['DESKRIPSI'];
      $data['id_ruangan'][] = $row['ID_RUANGAN'];
    }
  } else {
    $data = array(
      'nomr' => '',
      'nama' => 'Data tidak ditemukan',
      'kunjungan' => array(),
      'ruangan' => array(),
      'id_ruangan' => array()
    );
  }

  echo json_encode($data);
}

public function CekNomrRi()
{
  $nomr = $this->input->post('NOMR');
  $cekmr = $this->ModelAssembling->cek_nomr_ri($nomr);

  if ($cekmr->num_rows() > 0) {
    $result = $cekmr->result_array();
    $data = array(
            'nomr' => $result[0]['NORM'], // Asumsi NORM sama untuk semua kunjungan
            'nama' => $result[0]['NAMA'], // Asumsi NAMA sama untuk semua kunjungan
            'kunjungan' => array(),
            'ruangan' => array(),
            'id_ruangan' => array()
          );

    foreach ($result as $row) {
      $data['kunjungan'][] = $row['TANGGAL'];
      $data['ruangan'][] = $row['DESKRIPSI'];
      $data['id_ruangan'][] = $row['ID_RUANGAN'];
      $data['tgl_masuk'][] = $row['MASUK'];
      $data['tgl_keluar'][] = $row['KELUAR'];
    }
  } else {
    $data = array(
      'nomr' => '',
      'nama' => 'Data tidak ditemukan',
      'kunjungan' => array(),
      'ruangan' => array(),
      'id_ruangan' => array()
    );
  }

  echo json_encode($data);
}

  public function list_ruangan()
    {
        $result = $this->ModelAssembling->list_Ruangan();
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row['ID_SIMPEL'];
            $sub_array['text'] = $row['DESKRIPSI'];
            $data[] = $sub_array;
        }
        echo json_encode($data);
    }

  public function list_jumlah()
    {
        $result = $this->ModelAssembling->list_Jumlah();
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row['ID'];
            $sub_array['text'] = $row['DESKRIPSI'];
            $data[] = $sub_array;
        }
        echo json_encode($data);
    }

  public function list_jumlah1()
    {
        $result = $this->ModelAssembling->list_Jumlah();
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row['ID'];
            $sub_array['text'] = $row['DESKRIPSI'];
            $data[] = $sub_array;
        }
        echo json_encode($data);
    }

    public function list_volume()
    {
        $result = $this->ModelAssembling->list_Volume();
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row['ID'];
            $sub_array['text'] = $row['DESKRIPSI'];
            $data[] = $sub_array;
        }
        echo json_encode($data);
    }

    public function list_petugas()
    {
        $result = $this->ModelAssembling->list_Petugas();
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row['id'];
            $sub_array['text'] = $row['nama_lengkap'];
            $data[] = $sub_array;
        }
        echo json_encode($data);
    }

    public function list_lokasi()
    {
        $result = $this->ModelAssembling->list_Lokasi();
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row['ID'];
            $sub_array['text'] = $row['DESKRIPSI'];
            $data[] = $sub_array;
        }
        echo json_encode($data);
    }

    public function list_jenis()
    {
        $result = $this->ModelAssembling->list_Jenis();
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row['ID'];
            $sub_array['text'] = $row['DESKRIPSI'];
            $data[] = $sub_array;
        }
        echo json_encode($data);
    }

    public function getdataAssrj()
    {
      $start  = intval($this->input->post("start"));
      $length = intval($this->input->post("length"));
      $search = $this->input->post("search")['value'];
      $tugas = $this->session->userdata('tugas');
      $user = $this->session->userdata('id');
  
      $listdata = $this->ModelAssembling->dataListAssrj($start, $length, $search, $tugas, $user);
  
      $data = array();
      $no = $start + 1;
      foreach ($listdata['data'] as $field) {
          $edit = '<button type="button" class="btn btn-primary btn-rounded btn-sm" title="edit data" onclick="editAss(' . $field->ID . ')" style="width:72px"><i class="fa fa-edit"></i></button>';
          $hapus = '<button type="button" class="btn btn-danger btn-rounded btn-sm hapus_assrj" data="' . $field->ID . '" style="width:72px"><i class="fa fa-trash"></i></button>';
  
          $data[] = array(
              $no,
              $field->NOMR,
              $field->NAMA_PASIEN,
              $field->JUMLAH_RM,
              $field->VOLUME_RM,
              $field->TANGGAL_KUNJUNGAN,
              $field->PETUGAS,
              $field->TANGGAL_ASSEMBLING,
              $edit . ' ' . $hapus
          );
          $no++;
        }
    
        $output = array(
            "draw" => $this->input->post("draw"),
            "recordsTotal" => $listdata['recordsTotal'],
            "recordsFiltered" => $listdata['recordsFiltered'],
            "data" => $data
        );
        echo json_encode($output);
    }
    

  public function hapusAssri()
    {
    $this->db->trans_begin();
    $post = $this->input->post();
    $id =$post['ID'];

    
      $dataUpdate = array (
          'STATUS'                           => 0,
        );
    
    // echo "<pre>";print_r($id);exit();
        $this->db->where('db_rekammedis.tb_assembling.ID', $id);
        $this->db->update('db_rekammedis.tb_assembling', $dataUpdate);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
    }

    public function hapusAssrj()
    {
    $this->db->trans_begin();
    $post = $this->input->post();
    $id =$post['ID'];

    
      $dataUpdate = array (
          'STATUS'                           => 0,
        );
    
    // echo "<pre>";print_r($id);exit();
        $this->db->where('db_rekammedis.tb_assembling.ID', $id);
        $this->db->update('db_rekammedis.tb_assembling', $dataUpdate);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
    }

  public function simpanAssrj()
  {
    $this->db->trans_begin();

    $post = $this->input->post();
    // echo "<pre>";print_r($post);exit();
    $tglcreate = date('Y-m-d H:i:s');
                 
    $data_insert = array (
      'NOMR'       => $this->input->post('NOMR'),
      'NAMA_PASIEN'         => $this->input->post('NAMA'),
      'JUMLAH_RM'       => $this->input->post('JUMLAH_RM'),
      'VOLUME_RM'       => $this->input->post('VOLUME_RM'),
      'TANGGAL_KUNJUNGAN'       => $this->input->post('TANGGAL_KUNJUNGAN'),
      'ID_PETUGAS'       =>  $this->input->post('PETUGAS'),
      'TANGGAL_ASSEMBLING'       => $this->input->post('TANGGAL_ASS'),
      'JENIS_ASSEMBLING'          => 1,
      'TANGGAL_INPUT'            => $tglcreate,
      'OLEH'          => $this->session->userdata('id'),
    );
   // echo "<pre>";print_r($data_insert);exit();
    $this->db->insert('db_rekammedis.tb_assembling', $data_insert);

    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
      } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
      }
 
    echo json_encode($result);
    
  }

  public function simpanAssri()
  { 
    $this->db->trans_begin();

    $post = $this->input->post();
    // echo "<pre>";print_r($post);exit();
    $tglcreate = date('Y-m-d H:i:s');
                 
    $data_insert = array (
      'NOMR'       => $this->input->post('NOMR_RI'),
      'NAMA_PASIEN'         => $this->input->post('NAMA_RI'),
      'TANGGAL_MASUK'       => $this->input->post('TANGGAL_MASUK_RI'),
      'TANGGAL_KELUAR'       => $this->input->post('TANGGAL_KELUAR_RI'),
      'JUMLAH_RM'       => $this->input->post('JUMLAH_RM_RI'),
      'VOLUME_RM'       => $this->input->post('VOLUME_RM_RI'),
      'EPISODE'       => $this->input->post('EPISODE_RAWAT_RI'),
      'TANGGAL_ASSEMBLING'       => $this->input->post('TANGGAL_ASS_RI'),
      'JENIS_ASSEMBLING'          => 2,
      'ID_PETUGAS'       => $this->input->post('PETUGAS_RI'),
      // 'TANGGAL_KUNJUNGAN'       => $this->input->post('TANGGAL_KUNJUNGAN'),
      'TANGGAL_INPUT'            => $tglcreate,
      'OLEH'          => $this->session->userdata('id'),
    );
   // echo "<pre>";print_r($data_insert);exit();
    $this->db->insert('db_rekammedis.tb_assembling', $data_insert);

    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
      } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
      }
 
    echo json_encode($result);
    
  }

  public function modalEditAss()
  {
    if($this->input->is_ajax_request()==true){
      $id = $this->input->post('id');
      $ambildata = $this->ModelAssembling->editAss($id);
      if($ambildata->num_rows()>0){
        $row=$ambildata->row_array();
        $data=[
          
          // 'listkat'           => $this->ModelPerusahaan->tampilKategori(),
          'ID'                  => $row['ID'],
          'NOMR'                => $row['NOMR'],
          'NAMA_PASIEN'         => $row['NAMA_PASIEN'],
          // 'ID_JUM'                => $row['ID_JUM'],
          'JUMLAH_RM'                => $row['JUMLAH_RM'],
          'ID_VOL'                => $row['ID_VOL'],
          'VOLUME_RM'                => $row['VOLUME_RM'],
          'TANGGAL_KUNJUNGAN'                => $tgl_indo=date_format(date_create($row['TANGGAL_KUNJUNGAN']),'d-m-Y'),
          'PETUGAS'                => $row['PETUGAS'],
          'TANGGAL_ASSEMBLING'                => $tgl_indo=date_format(date_create($row['TANGGAL_ASSEMBLING']),'d-m-Y'),
        ];
      }
      $msg=[
        'sukses'=>$this->load->view('Assembling_rj/modal_edit', $data, true)
      ];
      echo json_encode($msg);
    }
  }

  public function modalEditAssri()
  {
    if($this->input->is_ajax_request()==true){
      $id = $this->input->post('id');
      $ambildata = $this->ModelAssembling->editAss($id);
      if($ambildata->num_rows()>0){
        $row=$ambildata->row_array();
        $data=[
          
          // 'listkat'           => $this->ModelPerusahaan->tampilKategori(),
          'ID'                  => $row['ID'],
          'NOMR'                => $row['NOMR'],
          'NAMA_PASIEN'         => $row['NAMA_PASIEN'],
          'JUMLAH_RM'                => $row['JUMLAH_RM'],
          'ID_VOL'                => $row['ID_VOL'],
          'VOLUME_RM'                => $row['VOLUME_RM'],
          // 'TANGGAL_KUNJUNGAN'                => $tgl_indo=date_format(date_create($row['TANGGAL_KUNJUNGAN']),'d-m-Y'),
          'PETUGAS'                => $row['PETUGAS'],
          'TANGGAL_ASSEMBLING'                => $tgl_indo=date_format(date_create($row['TANGGAL_ASSEMBLING']),'d-m-Y'),
          'TANGGAL_MASUK'                => $row['TANGGAL_MASUK'],
          'TANGGAL_KELUAR'                => $row['TANGGAL_KELUAR'],
          'EPISODE'                => $row['EPISODE'],
        ];
      }
      $msg=[
        'sukses'=>$this->load->view('Assembling_ri/modal_edit_ri', $data, true)
      ];
      echo json_encode($msg);
    }
  }

public function simpanEdit()
  {
    $post = $this->input->post();
    
    $this->db->trans_begin();

    $id = $this->input->post('ID');

    $tgledit = date('Y-m-d H:i:s');
                 
    $data_insert = array (
      // 'NOMR'       => $this->input->post('NOMR'),
      // 'NAMA_PASIEN'         => $this->input->post('NAMA'),
      'JUMLAH_RM'       => $this->input->post('JUMLAH_RM_EDIT'),
      'VOLUME_RM'       => $this->input->post('VOLUME_RM_EDIT'),
      // 'TANGGAL_KUNJUNGAN'       => $this->input->post('TANGGAL_KUNJUNGAN'),
      // 'ID_PETUGAS'       => $this->input->post('PETUGAS'),
      // 'TANGGAL_ASSEMBLING'       => $this->input->post('TANGGAL_ASS'),
      // 'TANGGAL_INPUT'            => $tglcreate,
      // 'OLEH'          => $this->session->userdata('id'),
    );
   // echo "<pre>";print_r($data_insert);exit();
    $this->db->where('ID', $id);
    $this->db->update('db_rekammedis.tb_assembling', $data_insert);

    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
      } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
      }
 
    echo json_encode($result);
    
  }

  public function simpanEditri()
  {
    $post = $this->input->post();
    
    $this->db->trans_begin();

    $id = $this->input->post('ID');

    $tgledit = date('Y-m-d H:i:s');
                 
    $data_insert = array (
      // 'NOMR'       => $this->input->post('NOMR'),
      // 'NAMA_PASIEN'         => $this->input->post('NAMA'),
      'JUMLAH_RM'       => $this->input->post('JUMLAH_RM_EDIT'),
      'VOLUME_RM'       => $this->input->post('VOLUME_RM_EDIT'),
      'EPISODE'       => $this->input->post('EPISODE_EDIT'),
      // 'TANGGAL_KUNJUNGAN'       => $this->input->post('TANGGAL_KUNJUNGAN'),
      // 'ID_PETUGAS'       => $this->input->post('PETUGAS'),
      // 'TANGGAL_ASSEMBLING'       => $this->input->post('TANGGAL_ASS'),
      // 'TANGGAL_INPUT'            => $tglcreate,
      // 'OLEH'          => $this->session->userdata('id'),
    );
   // echo "<pre>";print_r($data_insert);exit();
    $this->db->where('ID', $id);
    $this->db->update('db_rekammedis.tb_assembling', $data_insert);

    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
      } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
      }
 
    echo json_encode($result);
    
  }

  public function getdataAssri()
  {
    $tugas = $this->session->userdata('tugas');
    $user = $this->session->userdata('id');

    $draw = intval($this->input->post("draw"));
    $start = intval($this->input->post("start"));
    $length = intval($this->input->post("length"));
    $search = $this->input->post("search")['value'];

    $listdata = $this->ModelAssembling->dataListAssri($start, $length, $search, $tugas, $user);

    $data = array();
    $no = $start + 1;

    foreach ($listdata['data'] as $field) {
        $edit = '<button type="button" class="btn btn-primary btn-rounded btn-sm" title="edit data" onclick="editAssri(' . $field->ID . ')" style="width:72px"><i class="fa fa-edit"></i></button>';
        $hapus = '<button type="button" class="btn btn-danger btn-rounded btn-sm hapus_assri" href="javaScript:;" id="hapus_assri" title="hapus_assri" data="' . $field->ID . '" style="width:72px"><i class="fa fa-trash"></i></button>';

        $data[] = array(
            $no,
            $field->NOMR,
            $field->NAMA_PASIEN,
            $field->TANGGAL_MASUK,
            $field->TANGGAL_KELUAR,
            $field->JUMLAH_RM,
            $field->VOLUME_RM,
            $field->EPISODE,
            $field->TANGGAL_ASSEMBLING,
            $field->PETUGAS,
            $edit . ' ' . $hapus
        );
        $no++;
    }

    $output = array(
        "draw" => $draw,
        "recordsTotal" => $listdata['recordsTotal'],
        "recordsFiltered" => $listdata['recordsFiltered'],
        "data" => $data
    );

    echo json_encode($output);
  }






  public function simpanAssrjXXX()
  {
    $post = $this->input->post();
    // echo "<pre>";print_r($post);exit();
    $this->db->trans_begin();

    $tglcreate = date('Y-m-d H:i:s');
                 
    $data_insert = array (
      'NOMR'       => $this->input->post('NOMR'),
      'NAMA_PASIEN'         => $this->input->post('NAMA'), 
      'TANGGAL_KIRIM'         => $this->input->post('TANGGAL_KIRIM'),
      'TANGGAL_KUNJUNGAN'        => $this->input->post('TANGGAL_KUNJUNGAN'),
      'ID_RUANGAN'          => $this->input->post('RUANGAN'),
      'JENIS_FORM'          => $this->input->post('JENIS'),
      'JENIS_ASSEMBLING'       => 1,
      'TANGGAL_INPUT'            => $tglcreate,
      'OLEH'          => $this->session->userdata('id'),
    );
   // echo "<pre>";print_r($data_insert);exit();

    $this->db->insert('db_rekammedis.tb_assembling', $data_insert);

    $id_detail = $this->db->insert_id();

    $jenis = $this->input->post('JENIS');

    $getData = $this->ModelAssembling->formCeklis($jenis)->result_array();

    $data_insert_detail = array();

    foreach($getData as $row){
      $data_insert_detail[] = array(
      // 'ID'       => $id_detail,
      'ID_ASSEMBLING' => $id_detail, 
      'ID_FORM'       => $row['ID'],
      'CEKLIS'        => 1,
      );
    }
   // echo "<pre>";print_r($data_insert);exit();

    if (!empty($data_insert_detail)) {
        $this->db->insert_batch('db_rekammedis.tb_assembling_detail', $data_insert_detail);
    }

    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
      } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
      }
 
    echo json_encode($result);
    
  }

  

  public function modalCeklisX()
  {
    $id       = $this->input->post('id');
    // $did = $this->input->post('did');
    $head =  $this->ModelAssembling->inputCeklis($id)->result_array();
    // $body = $this->ModelAssembling->dataCeklis($did)->result_array();
    $data     = array(

      'head' => $head,
      // 'body' => $body,
    );
    $this->load->view('Assembling_rj/modal_ceklis', $data);
  }

  public function getdataCeklis()
  {
    // $draw   = intval($this->input->POST("draw"));
    // $start  = intval($this->input->POST("start"));
    // $length = intval($this->input->POST("length"));

    // $id = '6';
    
       // echo "<pre>";print_r($id);exit();

    $id       = $this->input->post('ID');

    $listdata = $this->ModelAssembling->dataCeklis($id);
    
    $data=array();
    $no =1;
    foreach ($listdata->result() as $field) {

      $ceklis=$field->CEKLIS;

      if($ceklis == '2'){
        $cek = '<input type="checkbox" data-id="'.$field->ID_DET.'"  class="cek_done" checked>';
      } else {
        $cek = '<input type="checkbox" data-id="'.$field->ID_DET.'"  class="cek_done">';
      }
    
      $edit = '<button type="button" class="btn btn-primary btn-rounded btn-sm" title="edit data" onclick="ceklis('.$field->ID.')" style="width:72px"><i class="fa fa-edit"></i> </button>';

      $data[] = array(
        $no,
        $field->NAMA_FORM,
        $cek
        );
        
      $no++;

    }

    $output = array(
      // "draw"            => $draw,
      // "recordsTotal"    => $listdata->num_rows(),
      // "recordsFiltered" => $listdata->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }

public function simpanCeklis()
    {
    $this->db->trans_begin();
    $post = $this->input->post();
    $id =$post['id'];
    $value =$post['value'];

    if($value==1){
      $dataUpdate = array (
          'CEKLIS'                           => 2,
        );
    } else if($value==2){
      $dataUpdate = array (
          'CEKLIS'                           => 1,
        );
    }
      
    // echo "<pre>";print_r($id);exit();
        $this->db->where('db_rekammedis.tb_assembling_detail.ID', $id);
        $this->db->update('db_rekammedis.tb_assembling_detail', $dataUpdate);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
    }
   



    



    

}
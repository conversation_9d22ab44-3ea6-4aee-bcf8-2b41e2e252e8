<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Login extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
		//Do your magic here
		$this->load->model('ModelLogin');
	}

	public function index()
	{
		$data = array(
			'title' 		=> 'Login' ,
		);
		$this->load->view('Login', $data, FALSE);		
	}

	public function signin(){
    $result    = $this->ModelLogin->login();

    if(isset($result['data'])){
      $data = $result['data'];

      $session = array(
        'id'        	  	=> $data['id'],
        // 'username'  	  	=> $data['username'],
        'nama'      	  	=> $data['nama'],
        // 'nama_pegawai'  	=> $data['nama_pegawai'],
        'id_coder'     	=> $data['id_coder'],
        // 'jabatan'  		  	=> $data['jabatan'],
        // 'idunit'  		  	=> $data['idunit'],
        'tugas'      => $data['tugas'],
        // 'unit'  		      => $data['unit'],
        'link'      	  	=> $data['link'],
        // 'id_dir'        	=> $data['id_dir'],
        'logged_in' 	  	=> TRUE,

      );
      $this->session->set_userdata($session);
    }
    echo json_encode($result);
  }

}

/* End of file Login.php */
/* Location: ./application/controllers/Login.php */
<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class <PERSON>jadwalanOperasi extends CI_Controller {

    public function __construct()
    {
        parent::__construct();
        if($this->session->userdata('logged_in') == FALSE ){
            redirect('login');  
        }
        
        // Check specific permission
        if($this->session->userdata('admision') != '1' || $this->session->userdata('stat_admision') != '2') {
            show_404();
        }
        
        date_default_timezone_set("Asia/Bangkok");
        $this->load->model(array('ModelPenjadwalanOperasi'));
    }

    public function index(){
        $data = array(
            'isi' => 'PenjadwalanOperasi/Index'
        );
        $this->load->view('Layout/Wrapper', $data);
    }

    public function get_data()
    {
        $draw = intval($this->input->post("draw"));
        $start = $this->input->post('start');  
        $length = $this->input->post('length'); 
        $searchValue = $this->input->post('search')['value'];
        $mulai = $this->input->post('mulai');
        $akhir = $this->input->post('akhir');
        $hari = $this->input->post('hari');
        $tujuan_rs = $this->input->post('tujuan_rs');
        
        $tab = $this->input->post('tab');
        $listdata = $this->ModelPenjadwalanOperasi->getDataPerjanjian($start, $length, $searchValue, $mulai, $akhir, $hari, $tab);
        
        $data = array();
        $no = $start + 1;
        foreach ($listdata['data'] as $row) {
            $sub_array = array();
            $sub_array[] = $no++;
            $sub_array[] = date('d/m/Y', strtotime($row->tgl_operasi));
            $sub_array[] = $row->kamar_operasi ?: '-';
            $sub_array[] = $row->tujuan_rs;
            $sub_array[] = date('d/m/Y H:i', strtotime($row->tgl_dibuat));
            $sub_array[] = $row->nama . '<br><small class="text-muted">' . $row->norm . '</small>';
            $sub_array[] = $row->norm;
            $sub_array[] = $row->tgl_lahir_umur;
            $sub_array[] = $row->ruang_rawat ?: '-';
            $sub_array[] = $row->diagnosis ?: '-';
            $sub_array[] = $row->tindakan ?: '-';
            $sub_array[] = $row->dokter_operator ?: '-';
            $sub_array[] = $row->dokter_anestesi ?: '-';
            $sub_array[] = $row->catatan_khusus ?: '-';
            
            // Tombol aksi
            $aksi = '';
            if ($row->id_penjadwalan) {
                $aksi .= '<button type="button" class="btn btn-sm btn-primary mr-1" onclick="editPerjanjian(' . $row->ID . ')" title="Edit">
                            <i class="fe-edit"></i>
                          </button>';
                $aksi .= '<button type="button" class="btn btn-sm btn-danger" onclick="hapusPerjanjian(' . $row->ID . ')" title="Hapus">
                            <i class="fe-trash-2"></i>
                          </button>';
            } else {
                $aksi .= '<button type="button" class="btn btn-sm btn-success" onclick="buatJadwal(' . $row->ID . ')" title="Buat Jadwal">
                            <i class="fe-plus"></i> Jadwal
                          </button>';
            }
            
            $sub_array[] = $aksi;
            $data[] = $sub_array;
        }

        $output = array(
            "draw" => $draw,
            "recordsTotal" => $listdata['recordsTotal'],
            "recordsFiltered" => $listdata['recordsFiltered'],
            "data" => $data
        );

        echo json_encode($output);
    }

    public function get_detail($id)
    {
        $data = $this->ModelPenjadwalanOperasi->getDetailPerjanjian($id);
        echo json_encode($data);
    }

    public function simpan()
    {
        $this->form_validation->set_rules($this->_validation_rules());
        
        if ($this->form_validation->run() == FALSE) {
            $response = array(
                'status' => 'error',
                'message' => validation_errors()
            );
        } else {
            $data = $this->input->post();
            
            if ($this->ModelPenjadwalanOperasi->simpanPerjanjian($data)) {
                $response = array(
                    'status' => 'success',
                    'message' => 'Data berhasil disimpan'
                );
            } else {
                $response = array(
                    'status' => 'error',
                    'message' => 'Gagal menyimpan data'
                );
            }
        }
        
        echo json_encode($response);
    }

    // public function hapus()
    // {
    //     $id = $this->input->post('id');
    //     $alasan = $this->input->post('alasan');
        
    //     if (empty($alasan)) {
    //         $response = array(
    //             'status' => 'error',
    //             'message' => 'Alasan penghapusan wajib diisi'
    //         );
    //     } else {
    //         if ($this->ModelPenjadwalanOperasi->hapusPerjanjian($id, $alasan)) {
    //             $response = array(
    //                 'status' => 'success',
    //                 'message' => 'Data berhasil dihapus'
    //             );
    //         } else {
    //             $response = array(
    //                 'status' => 'error',
    //                 'message' => 'Gagal menghapus data'
    //             );
    //         }
    //     }
        
    //     echo json_encode($response);
    // }

    public function get_pasien()
    {
        $search = $this->input->get('q');
        $data = $this->ModelPenjadwalanOperasi->getPasien($search);
        
        $results = array();
        foreach ($data as $row) {
            $results[] = array(
                'id' => $row['norm'],
                'text' => $row['norm'] . ' - ' . $row['nama']
            );
        }
        
        echo json_encode(array('results' => $results));
    }

    public function get_pasien_detail($norm)
    {
        $data = $this->ModelPenjadwalanOperasi->getPasienDetail($norm);
        echo json_encode($data);
    }

    public function get_dokter()
    {
        $data = $this->ModelPenjadwalanOperasi->getDokter();
        echo json_encode($data);
    }

    public function get_kamar()
    {
        $data = $this->ModelPenjadwalanOperasi->getKamarOperasi();
        echo json_encode($data);
    }

    public function get_kamar_for_calendar()
    {
        $data = $this->ModelPenjadwalanOperasi->getKamarOperasiForCalendar();
        echo json_encode($data);
    }

    public function get_dokter_anestesi()
    {
        $data = $this->ModelPenjadwalanOperasi->getDokterAnestesi();
        echo json_encode($data);
    }

    public function get_jenis_anestesi()
    {
        $data = $this->ModelPenjadwalanOperasi->getJenisAnestesi();
        echo json_encode($data);
    }

    public function get_ruang_rawat()
    {
        $data = $this->ModelPenjadwalanOperasi->getRuangRawat();
        echo json_encode($data);
    }

    // Data untuk tab Daftar Perjanjian Operasi
    public function get_daftar_perjanjian()
    {
        $start = $this->input->post('start');
        $length = $this->input->post('length');
        $searchValue = $this->input->post('search')['value'];
        $tanggal_mulai = $this->input->post('tanggal_mulai');
        $tanggal_akhir = $this->input->post('tanggal_akhir');
        $hari = $this->input->post('hari') ?: 'All';
        $status_filter = $this->input->post('status_filter') ?: 'penjadwalan,perjanjian';

        // Sorting
        $orderColumn = $this->input->post('order')[0]['column'];
        $orderDir = $this->input->post('order')[0]['dir'];
        $columns = ['no', 'tgl_operasi', 'kamar_operasi', 'tgl_dibuat', 'nama', 'norm', 'tgl_lahir_umur', 'ruang_rawat', 'diagnosis', 'tindakan', 'dokter_operator', 'dokter_anestesi', 'catatan_khusus', 'tujuan_rs'];
        $orderBy = isset($columns[$orderColumn]) && $columns[$orderColumn] != 'no' ? $columns[$orderColumn] : 'tgl_operasi';

        $result = $this->ModelPenjadwalanOperasi->getDaftarPerjanjian($start, $length, $searchValue, $tanggal_mulai, $tanggal_akhir, $hari, $orderBy, $orderDir, $status_filter);

        $data = array();
        $no = $start + 1;

        // Build table data with conflict information
        foreach ($result['data'] as $field) {
            $sub_array = array();
            $sub_array[] = $no;

            // Check konflik slot dari query database
            $hasConflict = isset($field->konflik_slot) && $field->konflik_slot > 0;

            // Format tanggal dengan tanda merah jika ada konflik slot
            if ($hasConflict) {
                $sub_array[] = '<span style="color:#dc3545;font-weight:600;background-color:#fff5f5;padding:2px 6px;border-radius:4px;border:1px solid #dc3545;">' . date('d/m/Y', strtotime($field->tgl_operasi)) . ' <i class="mdi mdi-alert-circle" title="ruang rawat sudah terisi"></i></span>';
            } else {
                $sub_array[] = date('d/m/Y', strtotime($field->tgl_operasi));
            }
            
            $sub_array[] = $field->kamar_operasi ?: '-';
            $sub_array[] = $field->tgl_dibuat ? date('d/m/Y H:i', strtotime($field->tgl_dibuat)) : '-';
            $sub_array[] = $field->nama;
            $sub_array[] = $field->norm;
            $sub_array[] = $field->tgl_lahir_umur;
            $sub_array[] = $field->ruang_rawat ?: '-';
            $sub_array[] = $field->diagnosis ?: '-';
            $sub_array[] = $field->tindakan ?: '-';
            $sub_array[] = $field->dokter_operator ?: '-';
            $sub_array[] = $field->dokter_anestesi ?: '-';
            $sub_array[] = $field->catatan_khusus ?: '-';
            $sub_array[] = $field->tujuan_rs ?: '-';

            // Kolom Aksi - Logic berdasarkan kondisi
            $aksi = '<div class="btn-group" role="group">';
            if (!empty($field->id_penjadwalan)) {
                // Jika ada id_penjadwalan = UBAH
                $aksi .= '<button type="button" class="btn btn-sm btn-primary btn-edit" data-id="'.$field->id_penjadwalan.'" data-jenis="ubah" title="Ubah">';
                $aksi .= '<i class="fa fa-edit"></i>';
                $aksi .= '</button>';
                $aksi .= '<button type="button" class="btn btn-sm btn-danger btn-delete" data-id="'.$field->id_penjadwalan.'" title="Hapus">';
                $aksi .= '<i class="fa fa-times"></i>';
                $aksi .= '</button>';
            } else {
                // Jika tidak ada id_penjadwalan = BUAT dan HAPUS PERJANJIAN
                $aksi .= '<button type="button" class="btn btn-sm btn-success btn-edit" data-id="'.$field->id_tpo.'" data-jenis="buat" title="Buat">';
                $aksi .= '<i class="fa fa-plus"></i>';
                $aksi .= '</button>';
                $aksi .= '<button type="button" class="btn btn-sm btn-danger btn-delete-perjanjian" data-id="'.$field->ID.'" title="Hapus Perjanjian">';
                $aksi .= '<i class="fa fa-trash"></i>';
                $aksi .= '</button>';
            }
            $aksi .= '</div>';
            $sub_array[] = $aksi;

            // Add row attributes for background color
            if (!empty($field->id_penjadwalan)) {
                $sub_array['DT_RowAttr'] = array('style' => 'background-color: #ff0000ff !important; color: #ffffff !important;'); // Hijau
            } else {
                $sub_array['DT_RowAttr'] = array('style' => 'background-color: #c6ee14ff !important;'); // Kuning
            }

            $data[] = $sub_array;
            $no++;
        }

        $output = array(
            "draw" => intval($this->input->post('draw')),
            "recordsTotal" => $result['recordsTotal'],
            "recordsFiltered" => $result['recordsFiltered'],
            "data" => $data
        );

        echo json_encode($output);
    }

    // Data untuk tab Operasi Hari Ini
    public function get_operasi_hari_ini()
    {
        $start = $this->input->post('start');
        $length = $this->input->post('length');
        $searchValue = $this->input->post('search')['value'];

        // Sorting
        $orderColumn = $this->input->post('order')[0]['column'];
        $orderDir = $this->input->post('order')[0]['dir'];
        $columns = ['no', 'kamar_operasi', 'tgl_dibuat', 'nama', 'norm', 'tgl_lahir_umur', 'ruang_rawat', 'diagnosis', 'tindakan', 'dokter_operator', 'dokter_anestesi', 'catatan_khusus', 'ruang_rawat_desc', 'tujuan_rs'];
        $orderBy = isset($columns[$orderColumn]) && $columns[$orderColumn] != 'no' ? $columns[$orderColumn] : 'kamar_operasi';

        $result = $this->ModelPenjadwalanOperasi->getOperasiHariIni($start, $length, $searchValue, $orderBy, $orderDir);

        $data = array();
        $no = $start + 1;

        foreach ($result['data'] as $field) {
            $sub_array = array();
            $sub_array[] = $no;
            $sub_array[] = $field->kamar_operasi ?: '-';
            $sub_array[] = $field->tgl_dibuat ? date('d/m/Y H:i', strtotime($field->tgl_dibuat)) : '-';
            $sub_array[] = $field->nama;
            $sub_array[] = $field->norm;
            $sub_array[] = $field->tgl_lahir_umur;
            $sub_array[] = $field->ruang_rawat ?: '-';
            $sub_array[] = $field->diagnosis ?: '-';
            $sub_array[] = $field->tindakan ?: '-';
            $sub_array[] = $field->dokter_operator ?: '-';
            $sub_array[] = $field->dokter_anestesi ?: '-';
            $sub_array[] = $field->catatan_khusus ?: '-';
            $sub_array[] = $field->ruang_rawat_desc ?: '-';
            $sub_array[] = $field->tujuan_rs ?: '-';

            // Kolom Aksi untuk Operasi Hari Ini - Selesai dan Hapus
            $aksi = '<div class="btn-group" role="group">';
            if (!empty($field->id_penjadwalan)) {
                // Button Selesai (status = 5)
                $aksi .= '<button type="button" class="btn btn-sm btn-success btn-selesai-operasi" data-id="'.$field->id_penjadwalan.'" title="Selesai">';
                $aksi .= '<i class="fa fa-check"></i>';
                $aksi .= '</button>';
                // Button Hapus
                $aksi .= '<button type="button" class="btn btn-sm btn-danger btn-hapus-operasi" data-id="'.$field->id_penjadwalan.'" data-perjanjian-id="'.$field->ID.'" title="Hapus">';
                $aksi .= '<i class="fa fa-trash"></i>';
                $aksi .= '</button>';
            } else {
                $aksi .= '<span class="text-muted">-</span>';
            }
            $aksi .= '</div>';
            $sub_array[] = $aksi;

            $data[] = $sub_array;
            $no++;
        }

        $output = array(
            "draw" => intval($this->input->post('draw')),
            "recordsTotal" => $result['recordsTotal'],
            "recordsFiltered" => $result['recordsFiltered'],
            "data" => $data
        );

        echo json_encode($output);
    }

    // Data untuk tab History/Selesai Operasi
    public function get_history_operasi()
    {
        $start = $this->input->post('start');
        $length = $this->input->post('length');
        $searchValue = $this->input->post('search')['value'];
        $tanggal_mulai = $this->input->post('tanggal_mulai');
        $tanggal_akhir = $this->input->post('tanggal_akhir');
        $hari = $this->input->post('hari') ?: 'All';

        // Sorting
        $orderColumn = $this->input->post('order')[0]['column'];
        $orderDir = $this->input->post('order')[0]['dir'];
        $columns = ['no', 'tgl_operasi', 'kamar_operasi', 'nama', 'norm', 'tgl_lahir_umur', 'ruang_rawat', 'diagnosis', 'tindakan', 'dokter_operator', 'dokter_anestesi', 'status'];
        $orderBy = isset($columns[$orderColumn]) && $columns[$orderColumn] != 'no' ? $columns[$orderColumn] : 'tgl_operasi';

        $result = $this->ModelPenjadwalanOperasi->getHistoryOperasi($start, $length, $searchValue, $tanggal_mulai, $tanggal_akhir, $hari, $orderBy, $orderDir);

        $data = array();
        $no = $start + 1;

        foreach ($result['data'] as $field) {
            // Status operasi
            $status = '';
            if ($field->status_penjadwalan == 0) {
                $status = '<span class="badge badge-danger">Batal Operasi</span>';
            } elseif ($field->status_penjadwalan == 5) {
                $status = '<span class="badge badge-success">Operasi Selesai</span>';
            }

            $sub_array = array();
            $sub_array[] = $no;
            $sub_array[] = date('d/m/Y', strtotime($field->tgl_operasi));
            $sub_array[] = $field->kamar_operasi ?: '-';
            $sub_array[] = $field->nama . ' (' . $field->norm . ')';
            $sub_array[] = $field->tgl_lahir_umur;
            $sub_array[] = $field->ruang_rawat ?: '-';
            $sub_array[] = $field->diagnosis ?: '-';
            $sub_array[] = $field->tindakan ?: '-';
            $sub_array[] = $field->dokter_operator ?: '-';
            $sub_array[] = $field->dokter_anestesi ?: '-';
            $sub_array[] = $status;

            $data[] = $sub_array;
            $no++;
        }

        $output = array(
            "draw" => intval($this->input->post('draw')),
            "recordsTotal" => $result['recordsTotal'],
            "recordsFiltered" => $result['recordsFiltered'],
            "data" => $data
        );

        echo json_encode($output);
    }

    // Get available days in date range
    public function get_available_days()
    {
        $tanggal_mulai = $this->input->post('tanggal_mulai');
        $tanggal_akhir = $this->input->post('tanggal_akhir');

        $available_days = $this->ModelPenjadwalanOperasi->getAvailableDays($tanggal_mulai, $tanggal_akhir);

        echo json_encode($available_days);
    }

    private function _validation_rules()
    {
        return array(
            array(
                'field' => 'norm',
                'label' => 'Nomor RM',
                'rules' => 'trim|required',
                'errors' => array(
                    'required' => '%s wajib diisi',
                ),
            ),
            array(
                'field' => 'id_dokter',
                'label' => 'Dokter',
                'rules' => 'trim|required',
                'errors' => array(
                    'required' => '%s wajib diisi',
                ),
            ),
            array(
                'field' => 'tgl_operasi',
                'label' => 'Tanggal operasi',
                'rules' => 'trim|required',
                'errors' => array(
                    'required' => '%s wajib diisi',
                ),
            ),
            array(
                'field' => 'kamar_operasi',
                'label' => 'Kamar operasi',
                'rules' => 'trim|required',
                'errors' => array(
                    'required' => '%s wajib diisi',
                ),
            ),
            array(
                'field' => 'waktu_operasi',
                'label' => 'Waktu operasi',
                'rules' => 'trim|required',
                'errors' => array(
                    'required' => '%s wajib diisi',
                ),
            ),
            array(
                'field' => 'durasi_operasi',
                'label' => 'Durasi operasi',
                'rules' => 'trim|required|numeric',
                'errors' => array(
                    'required' => '%s wajib diisi',
                    'numeric' => '%s harus berupa angka',
                ),
            ),
            array(
                'field' => 'tujuan_rs',
                'label' => 'Tujuan RS',
                'rules' => 'trim|required',
                'errors' => array(
                    'required' => '%s wajib diisi',
                ),
            )
        );
    }

    // Form Penjadwalan Operasi
    public function form_penjadwalan()
    {
        $id = $this->input->post('id');
        $jenis = $this->input->post('jenis');

        // Get form data
        $dataForm = $this->ModelPenjadwalanOperasi->isiForm($jenis, $id);

        // Get dropdown data
        $data = array(
            'detail' => $dataForm,
            'dokter' => $this->ModelPenjadwalanOperasi->dokter(),
            'kamar_operasi' => $this->ModelPenjadwalanOperasi->kamar(105090104),
            'dokter_anestesi' => $this->ModelPenjadwalanOperasi->dokter_anestesi(),
            'jenis_anestesi' => $this->ModelPenjadwalanOperasi->referensi(622),
            'list_kelas' => $this->ModelPenjadwalanOperasi->getKelas()
        );

        $this->load->view('PenjadwalanOperasi/FormPenjadwalan', $data);
    }

    // Delete Penjadwalan
    public function hapus_penjadwalan()
    {
        $id = $this->input->post('id');
        $alasan = $this->input->post('alasan');

        if (empty($alasan)) {
            echo json_encode(array('status' => 'error', 'message' => 'Alasan penghapusan wajib diisi'));
            return;
        }

        $result = $this->ModelPenjadwalanOperasi->hapusPenjadwalanOperasi($id, $alasan);

        if ($result) {
            echo json_encode(array('status' => 'success', 'message' => 'Data berhasil dihapus'));
        } else {
            echo json_encode(array('status' => 'error', 'message' => 'Gagal menghapus data'));
        }
    }

    // Hapus Perjanjian Operasi
    public function hapus_perjanjian()
    {
        $id = $this->input->post('id');

        $result = $this->ModelPenjadwalanOperasi->hapusPerjanjianOperasi($id);

        if ($result) {
            echo json_encode(array('status' => 'success', 'message' => 'Perjanjian berhasil dihapus'));
        } else {
            echo json_encode(array('status' => 'error', 'message' => 'Gagal menghapus perjanjian'));
        }
    }

    // Selesai Operasi - Update status = 5
    public function selesai_operasi()
    {
        $id = $this->input->post('id');

        $result = $this->ModelPenjadwalanOperasi->selesaiOperasi($id);

        if ($result) {
            echo json_encode(array('status' => 'success', 'message' => 'Operasi berhasil diselesaikan'));
        } else {
            echo json_encode(array('status' => 'error', 'message' => 'Gagal menyelesaikan operasi'));
        }
    }

    // Hapus Operasi Hari Ini
    public function hapus_operasi()
    {
        $id_penjadwalan = $this->input->post('id_penjadwalan');
        $id_perjanjian = $this->input->post('id_perjanjian');
        $alasan = $this->input->post('alasan');

        if (empty($alasan)) {
            echo json_encode(array('status' => 'error', 'message' => 'Alasan penghapusan wajib diisi'));
            return;
        }

        $result = $this->ModelPenjadwalanOperasi->hapusOperasiHariIni($id_penjadwalan, $id_perjanjian, $alasan);

        if ($result) {
            echo json_encode(array('status' => 'success', 'message' => 'Operasi berhasil dihapus'));
        } else {
            echo json_encode(array('status' => 'error', 'message' => 'Gagal menghapus operasi'));
        }
    }

    // Modal Pilih Tanggal Operasi
    public function modal_pilih_tanggal()
    {
        $this->load->view('PenjadwalanOperasi/ModalPilihTanggal');
    }

    // Get Calendar Data
    public function get_calendar_data()
    {
        $start_date = $this->input->post('start_date');
        $end_date = $this->input->post('end_date');

        $result = $this->ModelPenjadwalanOperasi->getCalendarData($start_date, $end_date);

        echo json_encode($result);
    }

    // Tambah Penjadwalan Baru
    public function tambah_penjadwalan()
    {
        $data = $this->input->post();

        // Update remun_medis.perjanjian
        if (!empty($data['id_perjanjian']) && !empty($data['tgl_operasi'])) {
            $this->db->where('ID', $data['id_perjanjian']);
            $this->db->update('remun_medis.perjanjian', array('TANGGAL' => $data['tgl_operasi']));
        }

        // Update medis.tb_pendaftaran_operasi
        if (!empty($data['id_tpo'])) {
            $update_pendaftaran = array();
            if (isset($data['slot_operasi'])) {
                $update_pendaftaran['slot_operasi'] = $data['slot_operasi'];
            }
            if (isset($data['kamar_operasi'])) {
                $update_pendaftaran['kamar'] = $data['kamar_operasi'];
            }
            if (isset($data['tgl_operasi'])) {
                $update_pendaftaran['tanggal_operasi'] = $data['tgl_operasi'];
            }
            if (!empty($update_pendaftaran)) {
                $this->db->where('ID', $data['id_tpo']);
                $this->db->update('medis.tb_pendaftaran_operasi', $update_pendaftaran);
            }
        }

        // Prepare data untuk tambah penjadwalan baru
        $penjadwalanData = array(
            'id_perjanjian' => $data['id_perjanjian'] ?? null,
            'id_reservasi' => $data['id_reservasi'] ?? null,
            'id_waiting_list_operasi' => $data['id_waiting_list_operasi'] ?? null,
            'kamar_operasi' => $data['kamar_operasi'] ?? $data['kamar_operasi_hidden'] ?? null,
            'slot_operasi' => $data['slot_operasi'] ?? null,
            'tujuan_rs' => 16,
            'tgl_rawat' => $data['tgl_rencanaMasuk'] ?? null,
            'tgl_operasi' => $data['tgl_operasi'] ?? null,
            'waktu_operasi' => $data['waktu_operasi'] ?? null,
            'dr_anestesi' => $data['dr_anestesi'] ?? null,
            'jenis_anestesi' => $data['jenis_anestesi'] ?? null,
            'durasi_operasi' => $data['durasi_operasi'] ?? null,
            'created_by' => $this->session->userdata('id_simpel'),
            'created_at' => date('Y-m-d H:i:s')
        );

        // Save penjadwalan operasi
        $result = $this->ModelPenjadwalanOperasi->tambahPenjadwalan($penjadwalanData);

        // Update reservasi jika ada id_reservasi
        if ($result && isset($data['id_reservasi']) && !empty($data['id_reservasi'])) {
            $reservasiData = array();
            if (isset($data['idCara_bayar']) && !empty($data['idCara_bayar'])) {
                $reservasiData['id_cara_bayar'] = $data['idCara_bayar'];
            }
            if (isset($data['kelasPasien']) && !empty($data['kelasPasien'])) {
                $reservasiData['id_kelas'] = $data['kelasPasien'];
            }
            if (isset($data['tgl_rencanaMasuk']) && !empty($data['tgl_rencanaMasuk'])) {
                $reservasiData['tgl_rencanaMasuk'] = $data['tgl_rencanaMasuk'];
            }

            if (!empty($reservasiData)) {
                $this->ModelPenjadwalanOperasi->updateReservasi($data['id_reservasi'], $reservasiData);
            }
        }

        if ($result) {
            echo json_encode(array('status' => 'success', 'message' => 'Penjadwalan berhasil ditambahkan'));
        } else {
            echo json_encode(array('status' => 'error', 'message' => 'Gagal menambahkan penjadwalan'));
        }
    }

    // Update Penjadwalan
    public function update_penjadwalan()
    {
        $data = $this->input->post();

        // Update remun_medis.perjanjian
        if (!empty($data['id_perjanjian']) && !empty($data['tgl_operasi'])) {
            $this->db->where('ID', $data['id_perjanjian']);
            $this->db->update('remun_medis.perjanjian', array('TANGGAL' => $data['tgl_operasi']));
        }

        // Update medis.tb_pendaftaran_operasi
        if (!empty($data['id_tpo'])) {
            $update_pendaftaran = array();
            if (isset($data['slot_operasi'])) {
                $update_pendaftaran['slot_operasi'] = $data['slot_operasi'];
            }
            if (isset($data['kamar_operasi'])) {
                $update_pendaftaran['kamar'] = $data['kamar_operasi'];
            }
            if (isset($data['tgl_operasi'])) {
                $update_pendaftaran['tanggal_operasi'] = $data['tgl_operasi'];
            }
            if (!empty($update_pendaftaran)) {
                $this->db->where('ID', $data['id_tpo']);
                $this->db->update('medis.tb_pendaftaran_operasi', $update_pendaftaran);
            }
        }

        // Prepare data untuk update penjadwalan
        $penjadwalanData = array(
            'id_penjadwalan' => $data['id_penjadwalan'],
            'id_perjanjian' => $data['id_perjanjian'] ?? null,
            'id_reservasi' => $data['id_reservasi'] ?? null,
            'id_waiting_list_operasi' => $data['id_waiting_list_operasi'] ?? null,
            'kamar_operasi' => $data['kamar_operasi'] ?? $data['kamar_operasi_hidden'] ?? null,
            'slot_operasi' => $data['slot_operasi'] ?? null,
            'tujuan_rs' => 16,
            'tgl_rawat' => $data['tgl_rencanaMasuk'] ?? null,
            'tgl_operasi' => $data['tgl_operasi'] ?? null,
            'waktu_operasi' => $data['waktu_operasi'] ?? null,
            'dr_anestesi' => $data['dr_anestesi'] ?? null,
            'jenis_anestesi' => $data['jenis_anestesi'] ?? null,
            'durasi_operasi' => $data['durasi_operasi'] ?? null,
            'created_by' => $this->session->userdata('id_simpel'),
            'updated_at' => date('Y-m-d H:i:s')
        );

        // Update penjadwalan operasi
        $result = $this->ModelPenjadwalanOperasi->updatePenjadwalan($penjadwalanData);

        // Update reservasi jika ada id_reservasi
        if ($result && isset($data['id_reservasi']) && !empty($data['id_reservasi'])) {
            $reservasiData = array();
            if (isset($data['idCara_bayar']) && !empty($data['idCara_bayar'])) {
                $reservasiData['id_cara_bayar'] = $data['idCara_bayar'];
            }
            if (isset($data['kelasPasien']) && !empty($data['kelasPasien'])) {
                $reservasiData['id_kelas'] = $data['kelasPasien'];
            }
            if (isset($data['tgl_rencanaMasuk']) && !empty($data['tgl_rencanaMasuk'])) {
                $reservasiData['tgl_rencanaMasuk'] = $data['tgl_rencanaMasuk'];
            }

            if (!empty($reservasiData)) {
                $this->ModelPenjadwalanOperasi->updateReservasi($data['id_reservasi'], $reservasiData);
            }
        }

        if ($result) {
            echo json_encode(array('status' => 'success', 'message' => 'Penjadwalan berhasil diupdate'));
        } else {
            echo json_encode(array('status' => 'error', 'message' => 'Gagal mengupdate penjadwalan'));
        }
    }

    // Get Week Calendar Data - Optimized endpoint
    public function get_week_calendar()
    {
        $start_date = $this->input->post('start_date');
        $end_date = $this->input->post('end_date');

        // Validate date format
        if (!$start_date || !$end_date) {
            echo json_encode(array('error' => 'Start date and end date are required'));
            return;
        }

        // Validate date format Y-m-d
        if (!DateTime::createFromFormat('Y-m-d', $start_date) || !DateTime::createFromFormat('Y-m-d', $end_date)) {
            echo json_encode(array('error' => 'Invalid date format. Use Y-m-d'));
            return;
        }

        // Limit date range to max 14 days for security
        $date1 = new DateTime($start_date);
        $date2 = new DateTime($end_date);
        $diff = $date2->diff($date1)->days;
        
        if ($diff > 14) {
            echo json_encode(array('error' => 'Date range too large. Maximum 14 days allowed'));
            return;
        }

        $rawData = $this->ModelPenjadwalanOperasi->getWeekCalendarData($start_date, $end_date);

        // Get list kamar for structuring response
        $kamarList = $this->ModelPenjadwalanOperasi->getKamarOperasiForCalendar();

        // Structure response for frontend
        $response = array(
            'kamar' => $kamarList,
            'days' => array()
        );

        // Generate 7 days from start_date (including weekends)
        $currentDate = new DateTime($start_date);
        $allDays = array();

        for ($i = 0; $i < 7; $i++) {
            $allDays[] = $currentDate->format('Y-m-d');
            $currentDate->add(new DateInterval('P1D'));
        }

        // Build days array
        foreach ($allDays as $date) {
            $dayData = array(
                'date' => $date,
                'slots' => array()
            );

            // Get all slots for this date from rawData
            if (isset($rawData[$date])) {
                foreach ($rawData[$date] as $slotKey => $slotData) {
                    if (strpos($slotKey, 'slot_') === 0) {
                        // Extract kamar_id and slot from key like 'slot_1_2'
                        $parts = explode('_', $slotKey);
                        if (count($parts) >= 3) {
                            $kamarId = $parts[1];
                            $slotNum = $parts[2];

                            $dayData['slots'][] = array(
                                'kamar_id' => $kamarId,
                                'slot' => intval($slotNum),
                                'jenis' => $slotData['jenis'], // 'scheduled' or 'appointment'
                                'pasien_info' => $slotData['pasien_info'],
                                'nama_dokter' => $slotData['nama_dokter'],
                                'rencana_tindakan_operasi' => $slotData['rencana_tindakan_operasi'],
                                'waktu_selesai' => $slotData['waktu_selesai'],
                                'waktu_operasi' => $slotData['waktu_operasi'],
                                'nama_kamar' => $slotData['nama_kamar']
                            );
                        }
                    }
                }
            }

            $response['days'][] = $dayData;
        }

        echo json_encode($response);
    }
}

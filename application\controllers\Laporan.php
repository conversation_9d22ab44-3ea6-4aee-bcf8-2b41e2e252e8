<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class <PERSON>poran extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
		if($this->session->userdata('logged_in') == FALSE ){
			redirect('login');  
		}
		date_default_timezone_set("Asia/Bangkok");
		$this->load->model(array('ModelLaporan'));

	}

	public function index()
	{
		$data=array(
			'isi'			=> 'Laporan/Laporan_rekap',
			// 'listfilling'   => $this->ModelLaporan->listTableFilling()->result_array(),
			// 'penjamin'		=> $this->MasterModel->penjamin(),
		);
		$this->load->view('Layout/Wrapper',$data, FALSE);
	}
	public function index_lap_analisis()
	{
		$data=array(
			'isi'			=> 'Laporan/Laporan_analisis',
		);
		$this->load->view('Layout/Wrapper',$data, FALSE);
	}
	public function indexAdmision()
	{
		$data=array(
			'isi'			=> 'Admision/laporan/Laporan',
		);
		$this->load->view('Layout/Wrapper',$data, FALSE);
	}
	public function getPetugas() {
		$jns = $this->input->get('jns');
		$result = $this->ModelLaporan->get_petugas($jns);
		
		$data = array();
		foreach ($result as $row) {
			$data[] = array(
				'id' => $row['DESKRIPSI'] . '|' . $row['KET'],
				'text' => $row['DESKRIPSI'] . ' - ' . $row['KET']
			);
		}
		
		echo json_encode($data);
	}
	
	
}

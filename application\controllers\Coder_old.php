<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Coder extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if($this->session->userdata('logged_in') == FALSE ){
      redirect('login');  
    }
    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('ModelCoder'));
  }

  public function index()
  {
    $data=array(      
      // 'listkat'        => $this->ModelInput->tampilKategori(),
      // 'listperusahaan' => $this->ModelInput->listPerusahaan(),
      // 'listno'         => $this->ModelInput->listnoPks(),
      'isi'            => 'Coder/Index'
    );
    $this->load->view('Layout/Wrapper',$data);
  }
  public function index_history()
  {
    $data=array(
      'isi'            => 'Coder/IndexHistory'
    );
    $this->load->view('Layout/Wrapper',$data);
  }
  public function index_tugas()
  {
    $data=array(
      'isi'            => 'Coder/IndexTugas'
    );
    $this->load->view('Layout/Wrapper',$data);
  }
  public function history_coder(){ 
    $start = $this->input->post('start');
    $length = $this->input->post('length');
    $searchValue = $this->input->post('search')['value']; 
    $userid = $this->session->userdata('id');
    
    $filters = false; 
    if ($this->input->post('norm') || $this->input->post('awalLayanan') || $this->input->post('akhirLayanan') || $this->input->post('awalGrouping') || $this->input->post('akhirGrouping')) {
        $filters = true;
    }

    // Memanggil model hanya jika ada filter
    if ($filters) {
        $result = $this->ModelCoder->getHistory($userid, $start, $length, $searchValue);
    } else {
      $result = [
          'recordsTotal' => 0,
          'recordsFiltered' => 0,
          'data' => []
      ];
    }

    $data = array();
    $no = $start + 1; 

    foreach ($result['data'] as $field) {
        if ($field->HASIL_RAD == '1') {
            $hasil_rad = '<span class="badge bg-warning text-dark" style="font-size: 16px;">Belum Ada Hasil</span>';
        } else if ($field->HASIL_RAD == '2') {
            $hasil_rad = '<span class="badge bg-success" style="font-size: 16px;">Sudah Ada Hasil</span>';
        } else if ($field->HASIL_RAD == '3') {
            $hasil_rad = '<span class="badge bg-success" style="font-size: 16px;">Dikerjakan Belum Ada Hasil</span>';
        } else if ($field->HASIL_RAD == '4') {
            $hasil_rad = '<span class="badge bg-danger" style="font-size: 16px;">Tidak Dikerjakan</span>';
        } else {
            $hasil_rad = '-';
        }

        $det = '<a href="#viewDet" data-toggle="modal" data-id="'.$field->ID.'" 
        data-deskripsi="'.$field->DESKRIPSI.'"
        data-nopen="'.$field->NOPEN.'"
        data-tanggal-pendaftaran="'.date_format(date_create($field->TANGGAL_PENDAFTARAN), 'd-m-Y H:i:s').'" 
        data-tanggal-tarik="'.date_format(date_create($field->TANGGAL_TARIK), 'd-m-Y').'" 
        data-backdrop="static" data-keyboard="false">
        <i class="fa fa-info-circle"></i>
        </a>';
        // $info = '<a href="#viewData" data-toggle="modal" data-id="'.$field->ID.'" data-backdrop="static" data-keyboard="false">
        // <i class="fas fa-pencil-alt"></i>
        // </a>';
        $buka = '<a href="#" class="btn btn-sm btn-primary" onclick="modalcek(\'' . $field->NORM . '\', \'' . $field->NAMA_PASIEN . '\', \'' . $field->NOPEN . '\')"><i class="fas fa-search"></i> Lihat</a>';

        $sub_array = array();
        $sub_array[] = $no++;
        $sub_array[] = date_format(date_create($field->TANGGAL_LAYANAN), 'd-m-Y H:i:s');
        $sub_array[] = $field->NAMA_PASIEN.'  ['. $field->NORM .']'. ' ' . $det;
        $sub_array[] = $hasil_rad;
        $sub_array[] = $field->SEP . (!is_null($field->statusSEP) ? ' [' . $field->statusSEP . ']' : '  [TIDAK DIISI]');  
        $sub_array[] = $buka ;  
        $sub_array[] = $field->DONE_DATE ? $field->DONE_DATE : '-';
        $sub_array[] = ($field->STATUS == 2) ? "Sudah coding" : "Batal coding";
        
        $data[] = $sub_array;
    }

    $output = array(
        "draw" => intval($this->input->post('draw')),
        "recordsTotal" => $result['recordsTotal'],
        "recordsFiltered" => $result['recordsFiltered'],
        "data" => $data
    );

    echo json_encode($output);
}




public function getListdata()
  {
    $tugas = $this->session->userdata('tugas');
    $userid = $this->session->userdata('id');
    // if($tugas=='9'){
    //   $listdata = $this->ModelCoder->dataListcosting_admin();
    // } else {
    //   $listdata = $this->ModelCoder->dataListcosting_user($userid);
    // }
     // Ambil parameter dari DataTables
     $start = $this->input->post("start");
     $length = $this->input->post("length");
     $search_value = $this->input->post("search")['value'];
 
     // Panggil model dengan parameter sesuai
     if ($tugas == '9') {
         $listdata = $this->ModelCoder->dataListcosting_admin($start, $length, $search_value);
     } else {
         $listdata = $this->ModelCoder->dataListcosting_user($userid, $start, $length, $search_value);
     }
    
    $data=array();
    $no = $start + 1;
    foreach ($listdata['data']->result() as $field) {
      $info = '<a href="#viewData" data-toggle="modal" data-id="'.$field->ID.'" data-backdrop="static" data-keyboard="false">
      <i class="fas fa-pencil-alt"></i>
      </a>';  
      
      // $cek = '<input type="checkbox" value="'.$field->ID.'"  class="cek_done">';

      if ($field->HASIL_RAD == '1') {
          $hasil_rad = '<span class="badge bg-warning text-dark" style="font-size: 16px;">Belum Ada Hasil</span>';
      } else if ($field->HASIL_RAD == '2') {
          $hasil_rad = '<span class="badge bg-success" style="font-size: 16px;">Sudah Ada Hasil</span>';
      } else if ($field->HASIL_RAD == '3') {
          $hasil_rad = '<span class="badge bg-success" style="font-size: 16px;">Dikerjakan Belum Ada Hasil</span>';
      } else if ($field->HASIL_RAD == '4') {
          $hasil_rad = '<span class="badge bg-danger" style="font-size: 16px;">Tidak Dikerjakan</span>';
      } else {

          $hasil_rad = '-';
      }
    

      if($field->STATUS=='1'){
        $status = 'Belum Dikerjakan';
        $cek = '<input type="checkbox" data-id="'.$field->ID.'" value="'.$field->STATUS.'"  class="cek_done">';
      } else {
        $status = 'Sudah Dikerjakan';
        $cek = '<input type="checkbox" data-id="'.$field->ID.'" value="'.$field->STATUS.'"  class="cek_done" checked>';
      }
      // $buka = '<a href="#" class="btn btn-sm btn-primary" onclick="modalcek(\''.$field->NORM.'\')"><i class="fas fa-search"></i> Lihat</a>';
     $buka = '<a href="#" class="btn btn-sm btn-primary" onclick="modalcek(\'' . $field->NORM . '\', \'' . $field->NAMA_PASIEN . '\', \'' . $field->NOPEN . '\')"><i class="fas fa-search"></i> Lihat</a>';
      


      if($tugas=='9'){
        // $pindah = '<a href="#viewPindah" 
        // data-toggle="modal" 
        // data-id="'.$field->ID.'" 
        // data-id-petugas="'.$field->id_petugas.'" 
        // data-nama="'.$field->nama.'" 
        // data-backdrop="static" 
        // data-keyboard="false" 
        // class="btn btn-sm btn-primary" 
        // style="background-color: yellow; padding: 2px 6px;"
        // title="Klik untuk membagi tugas code : '.$field->nama.'">
        // <i class="fas fa-share-alt" style="font-size: 14px; color: white;"></i>
        // </a>';
          $data[] = array(
          '<input type="checkbox" class="selectItem" data-id="'.$field->ID.'" onchange="updateCheckboxStatus(this)">', // Checkbox
          $no,
          $field->nama,
          $field->NAMA_PASIEN.' '.'['.$field->NORM.']'. '  ' .$pindah, 
          $field->SEP,
          $field->statusSEP,
          $buka,
          empty($field->DONE_DATE) ? "-" : $field->DONE_DATE,
          empty($field->COSTING) ? "-" : $field->COSTING,
          $status,
          );
          
      } else {
        // $buka = '<a href="#" class="btn btn-sm btn-primary" onclick="modalcek(\'' . $field->NORM . '\', \'' . $field->NAMA_PASIEN . '\', \'' . $field->NOPEN . '\')"><i class="fas fa-search"></i> Lihat</a>';
        $det = '<a href="#viewDet" data-toggle="modal" data-id="'.$field->ID.'" 
         data-deskripsi="'.$field->DESKRIPSI.'"
         data-nopen="'.$field->NOPEN.'"
         data-costing="'.$field->STATUS_COSTING.'"
         data-tanggal-pendaftaran="'.date_format(date_create($field->TANGGAL_PENDAFTARAN), 'd-m-Y H:i:s').'" 
         data-tanggal-tarik="'.date_format(date_create($field->TANGGAL_TARIK), 'd-m-Y').'" 
         data-backdrop="static" data-keyboard="false">
         <i class="fa fa-info-circle"></i>
         </a>';      

        if (empty($field->statusSEP)) {
            $statusSEP = $field->SEP; 
        } elseif ($field->statusSEP == 'LAYAK') {
            $statusSEP = '<span style="color: #5bc0de; font-weight: bold;">' . $field->SEP . '</span>';
        } else {
            $statusSEP = '<span style="color: red; font-weight: bold;">' . $field->SEP . '</span>';
        }
        if ($field->STATUS_COSTING == 'BELUM COSTING') {
          $tanggal_layanan = '<span style="color: red;">' . date_format(date_create($field->TANGGAL_LAYANAN), 'd-m-Y H:i:s') . '</span>';
        } elseif ($field->STATUS_COSTING == 'BATAL COSTING') {
            $tanggal_layanan = '<span style="color: yellow;">' . date_format(date_create($field->TANGGAL_LAYANAN), 'd-m-Y H:i:s') . '</span>';
        } else {
            $tanggal_layanan = date_format(date_create($field->TANGGAL_LAYANAN), 'd-m-Y H:i:s');
        }
      
  
        $data[] = array(
        $no,
        $tanggal_layanan,
        $field->NAMA_PASIEN.'  '.'['. $field->NORM .']'. ' ' . $det,         
        $hasil_rad,
        $statusSEP. ' ' . $info,       
        $buka,
        empty($field->DONE_DATE) ? "-" : $field->DONE_DATE,
        $cek,
        );
      }
      
      $no++;

    }

    $output = array(
      "draw" => intval($this->input->post("draw")),
      "recordsTotal" => $listdata['recordsTotal'],
      "recordsFiltered" => $listdata['recordsFiltered'],
      "data" => $data
    );
    echo json_encode($output);
  }

  public function deleteCoding() {
    $this->db->trans_begin();
    $ids = $this->input->post('ids');   

    $this->db->set('STATUS', 0);
    $this->db->where_in('ID', $ids);  
    $this->db->update('db_rekammedis.tb_coding_detail');

    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $response = array('status' => 'failed');
    } else {
        $this->db->trans_commit();
        $response = array('status' => 'success');
    }
    echo json_encode($response);
  }  
  public function modalPindah(){
    $id   = $this->input->post('id');
    $petugas   = $this->input->post('id_petugas');
    $nama   = $this->input->post('nama');
    
    $data = array(
      'id' => $id,
      'nama' => $nama,
      'petugas' => $petugas
    );
  
    $this->load->view('Coder/index_modal_pindah', $data);
  }
  public function petugasCoder()
  {
      $result = $this->ModelCoder->list_Petugas();
      $data = array();
      foreach ($result as $row) {
          $sub_array = array();
          $sub_array['id'] = $row['id'];
          $sub_array['text'] = $row['nama_lengkap'];
          $data[] = $sub_array;
      }
      echo json_encode($data);
  }
  public function updateTugas() {
    $this->db->trans_begin();
    $id = $this->input->post('id');
    $id_petugas_lama = $this->input->post('id_petugas_lama');
    $id_petugas_baru = $this->input->post('id_petugas');
    
    $data_insert = array(
        'ID_PETUGAS' => $id_petugas_baru, 
    );
    
    $this->db->where('ID', $id);
    $this->db->update('db_rekammedis.tb_coding_detail', $data_insert);

    $data_log = array(
        'TANGGAL' => date('Y-m-d H:i:s'), 
        'OLEH' => $this->session->userdata('id'),
        'ID_CODING_DET' => $id,            
        'ID_PETUGAS_OLD' => $id_petugas_lama, 
        'ID_PETUGAS_NEW' => $id_petugas_baru,  
        'STATUS' => 2,  
    );
    $this->db->insert('db_rekammedis.coding_detail_log', $data_log);
    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
    } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
    }
    echo json_encode($result);
}



  public function addStatusSEP(){
    $ID   = $this->input->post('id');
    $status = $this->ModelCoder->getCoding($ID );
    $data = array(
      'id' => $ID,
      'status' => $status,
    );
  
    $this->load->view('Coder/index_modal_SEP', $data);
  }
  public function list_statusSEP(){
    $result = $this->ModelCoder->getstatus(); 
    $data = array();
      foreach ($result as $row) {
          $sub_array = array();
          $sub_array['id'] = $row['ID'];
          $sub_array['text'] = $row['DESKRIPSI'];
          $data[] = $sub_array;
      }
      
    echo json_encode($data);   
  }
  public function updateStatusSEP(){
    $this->db->trans_begin();
    $id = $this->input->post('ID');
    $data_insert = array(
        'STATSEP'    => $this->input->post('statSEP'),
    );
    $this->db->where('ID', $id);
    $this->db->update('db_rekammedis.tb_coding_detail', $data_insert);

    // Periksa status transaksi dan kirimkan respons sesuai dengan itu
    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
    } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

  public function modalCek(){
    $norm = $this->input->post('norm'); 
    $nama_pasien = $this->input->post('nama_pasien'); 
    $nopen = $this->input->post('nopen'); 

    // $judul = $this->ModelCoder->getJudul($norm);   
    // // var_dump($id);
    // if ($judul->num_rows() > 0) {
    //   $row = $judul->row_array();
      $data = [
          'NORM'       => $norm,
          'NAMAPASIEN' => $nama_pasien,
          'NOPEN'      => $nopen,
      ];
    // } 
    // $data['norm'] = $norm;
    
    $msg=[
      'sukses'=>$this->load->view('Coder/index_modal',$data, TRUE)
    ];
    echo json_encode($msg);
  }
  public function radiologi() {
    $nopen = $this->input->post('id'); 
    $listdata = $this->ModelCoder->daataRadiologi($nopen);
    $data = array();
    $no = 1;

    foreach ($listdata->result() as $field) {
        $tgl = ($field->TANGGAL_KUNJUNGAN != null) ? date('d-m-Y H:i:s', strtotime($field->TANGGAL_KUNJUNGAN)) : " - ";
        $namatindakan = ($field->NAMATINDAKAN != null) ? $field->NAMATINDAKAN : " - ";
        
        // Button untuk membuka modal index_modal_radio
        $button = '<button type="button" class="btn btn-primary btn-rounded btn-sm viewDetail" data-id="' . $field->ID . '">View</button>';
        
        $data[] = array(
            "No" => $no,
            "Nama Tindakan" => $namatindakan,
            "Tgl Kunjungan" => $tgl,
            "#" => $button // Gunakan "#" sebagai kolom untuk button
        );

        $no++;
    }

    $output = array(
        "data" => $data
    );

    echo json_encode($output);
}
public function modalRad() {
  $id = $this->input->post('id');
  $hasilRad = $this->ModelCoder->hasilRadiologi($id);

  if ($hasilRad->num_rows() > 0) {
      $row = $hasilRad->row_array();
      $status = '-';
      if ($row['STATUS'] == 1) {
          $status = "BELUM FINAL";
      } elseif ($row['STATUS'] == 2) {
          $status = "FINAL";
      }
      $data = array(
          'TGLHASIL'      => isset($row['TGLHASIL']) ? $row['TGLHASIL'] : "-",
          'KESAN'         => isset($row['KESAN']) ? $row['KESAN'] : "-",
          'HASIL'         => isset($row['HASIL']) ? $row['HASIL'] : "-",
          'DOKTER_SATU'   => isset($row['DOKTER_SATU']) ? $row['DOKTER_SATU'] : "-",
          'DOKTER_DUA'    => isset($row['DOKTER_DUA']) ? $row['DOKTER_DUA'] : "-",
          'STATUS'        => $status
      );

    }else {
        $data = array(
            'TGLHASIL' => 'Data tidak ditemukan',
            'KESAN' => 'Data tidak ditemukan',
            'HASIL' => 'Data tidak ditemukan',
            'DOKTER_SATU' => 'Data tidak ditemukan',
            'DOKTER_DUA' => 'Data tidak ditemukan', 
            'STATUS' => 'Data tidak ditemukan',                
        );
    }
    echo json_encode($data);


}


  // public function radiologi(){
  //   $norm = $this->input->post('norm'); 
  //   $listdata = $this->ModelCoder->daataRadiologi($norm);
  //   $data=array();
  //   $no =1;
  //   foreach ($listdata->result() as $field) {
  //     $button = '<button type="button" class="btn btn-primary btn-rounded btn-sm" title="Expertise Radiologi" onclick="viewRadiologi('.$field->ID.')" style="width:72px"><i class="fa fa-eye"></i></button>';   
  //     $tgl = ($field->TANGGAL_KUNJUNGAN != null) ? date('d-m-Y H:i:s', strtotime($field->TANGGAL_KUNJUNGAN)) : " - ";
  //     $namatindakan = ($field->NAMATINDAKAN != null) ? $field->NAMATINDAKAN : " - ";
  //     $data[] = array(
  //       $no,
  //       $namatindakan,
  //       $tgl,
  //       $button
  //       );
        
  //     $no++;
  //   }

  //   $output = array(
  //     "data"            => $data
  //   );
  //   echo json_encode($output);    
  // }

  // public function modalRad(){
  //   $id = $this->input->post('id');
  //   $hasilRad = $this->ModelCoder->hasilRadiologi($id);
  //   if($hasilRad->num_rows()>0){
  //     $row=$hasilRad->row_array();
  //     $status = '-';
  //     if ($row['STATUS'] == 1) {
  //         $status = "BELUM FINAL";
  //     } elseif ($row['STATUS'] == 2) {
  //         $status = "FINAL";
  //     }
  //     $data=[
  //       'TGLHASIL'      => isset($row['TGLHASIL']) ? $row['TGLHASIL'] : "-",
  //       'KESAN'         => isset($row['KESAN']) ? $row['KESAN'] : "-",
  //       'HASIL'         => isset($row['HASIL']) ? $row['HASIL'] : "-",
  //       'DOKTER_SATU'   => isset($row['DOKTER_SATU']) ? $row['DOKTER_SATU'] : "-",
  //       'DOKTER_DUA'    => isset($row['DOKTER_DUA']) ? $row['DOKTER_DUA'] : "-",
  //       'STATUS'        => $status
  //     ];
  //   }
  //   $msg=[
  //     'sukses'=>$this->load->view('Coder/index_modal_radio', $data, true)
  //   ];
  //   echo json_encode($msg);

  // }
  //lab PA
  public function get_data_sito(){
		$draw   = intval($this->input->POST("draw"));
		$start  = intval($this->input->POST("start"));
		$length = intval($this->input->POST("length"));
	
		$nopen = $this->input->post('nopen');
		$listSito = $this->ModelCoder->datatablesSito($nopen);
	
		$data = array();
		$no = $_POST['start'];
		foreach ($listSito as $LS) {
		$no++;

    $button = '<button type="button" class="btn btn-primary btn-rounded btn-sm Sito" data-id="' . $LS->NOMOR_LAB . '">View</button>';
		// $button = '<a class="btn btn-primary cetakSito" href="javascript:;" data="'.$LS->ID.'" nolab="'.$LS->NOMOR_LAB.'"> Cetak</a>';
    $data[] = array(
      $no,
      $LS->NOMOR_LAB,
      $LS->TANGGAL_ORDER ? date_format(date_create($LS->TANGGAL_ORDER), 'd-m-Y H:i:s') : '-',
      $button,			
    );
  
		  
		}
	
		$output = array(
		  "draw"            => $draw,
		  "recordsTotal"    => $this->ModelCoder->total_count_Sito($nopen),
		  "recordsFiltered" => $this->ModelCoder->filter_count_Sito($nopen),
		  "data"            => $data
		);
		echo json_encode($output);
	}
  public function getDataSito() {
    $id = $this->input->post('id');
    $sitologi= $this->ModelCoder->getSito($id);

    $data = array(
        'sitologi' => $sitologi,
    );
    
    if ($sitologi) {
        $html = $this->load->view('Coder/hasilLab/hasilSito', $data, true);
        echo json_encode(array('success' => true, 'html' => $html));
    } else {
        echo json_encode(array('success' => false));
    }
  }
  




	public function get_data_histo(){
		$draw   = intval($this->input->POST("draw"));
		$start  = intval($this->input->POST("start"));
		$length = intval($this->input->POST("length"));
	
		$nopen = $this->input->post('nopen');
		$listHisto = $this->ModelCoder->datatablesHisto($nopen);
	
		$data = array();
		$no = $_POST['start'];
		foreach ($listHisto as $LH) {
		$no++;
    $button = '<button type="button" class="btn btn-primary btn-rounded btn-sm Histo" data-id="' . $LH->NOMOR_LAB . '">View</button>';   

		// $button = '<a class="btn btn-primary cetakHisto" href="javascript:;" data="'.$LH->ID.'" nolab="'.$LH->NOMOR_LAB.'"> Cetak</a>';

		  $data[] = array(
			$no,
			$LH->NOMOR_LAB,
			$LH->TANGGAL_ORDER ? date_format(date_create($LH->TANGGAL_ORDER), 'd-m-Y H:i:s') : '-',
			$button,
			
		  );
		  
		}
	
		$output = array(
		  "draw"            => $draw,
		  "recordsTotal"    => $this->ModelCoder->total_count_Histo($nopen),
		  "recordsFiltered" => $this->ModelCoder->filter_count_Histo($nopen),
		  "data"            => $data
		);
		echo json_encode($output);
	}
  public function getDataHisto() {
    $id = $this->input->post('id');
    $histologi= $this->ModelCoder->getHisto($id);

    $data = array(
        'histologi' => $histologi,
    );
    
    if ($histologi) {
        $html = $this->load->view('Coder/hasilLab/hasilHisto', $data, true);
        echo json_encode(array('success' => true, 'html' => $html));
    } else {
        echo json_encode(array('success' => false));
    }
  }

	public function get_data_imuno(){
		$draw   = intval($this->input->POST("draw"));
		$start  = intval($this->input->POST("start"));
		$length = intval($this->input->POST("length"));
	
		$nopen = $this->input->post('nopen');
		$listImuno = $this->ModelCoder->datatablesImuno($nopen);
	
		$data = array();
		$no = $_POST['start'];
		foreach ($listImuno as $LI) {
		$no++;
    $button = '<button type="button" class="btn btn-primary btn-rounded btn-sm Imuno" data-id="' . $LI->NOMOR_IMUNO . '">View</button>';   

		// $button = '<a class="btn btn-primary cetakImuno" href="javascript:;" data="'.$LI->ID.'" nolab="'.$LI->NOMOR_IMUNO.'"> Cetak</a>';

		  $data[] = array(
			$no,
			$LI->NOMOR_IMUNO,
			$LI->TANGGAL_ORDER ? date_format(date_create($LI->TANGGAL_ORDER), 'd-m-Y H:i:s') : '-',
			$button,
			
		  );
		  
		}
	
		$output = array(
		  "draw"            => $draw,
		  "recordsTotal"    => $this->ModelCoder->total_count_Imuno($nopen),
		  "recordsFiltered" => $this->ModelCoder->filter_count_Imuno($nopen),
		  "data"            => $data
		);
		echo json_encode($output);
	}
  public function getDataImuno() {
    $id = $this->input->post('id');
    $imunohisto= $this->ModelCoder->getImuno($id);

    $data = array(
        'imunohisto' => $imunohisto,
    );
    
    if ($imunohisto) {
        $html = $this->load->view('Coder/hasilLab/hasilImuno', $data, true);
        echo json_encode(array('success' => true, 'html' => $html));
    } else {
        echo json_encode(array('success' => false));
    }
  }

	public function get_data_patmol(){
		$draw   = intval($this->input->POST("draw"));
		$start  = intval($this->input->POST("start"));
		$length = intval($this->input->POST("length"));
	
		$nopen = $this->input->post('nopen');
		$listPatmol = $this->ModelCoder->datatablesPatmol($nopen);
	
		$data = array();
		$no = $_POST['start'];
		foreach ($listPatmol as $LP) {
		$no++;
    $button = '<button type="button" class="btn btn-primary btn-rounded btn-sm Patmol" data-id="' . $LP->NOMOR_PATMOL . '">View</button>';   


		// $button = '<a class="btn btn-primary cetakPatmol" href="javascript:;" data="'.$LP->NOMOR_PATMOL.'" jenis="'.$LP->JENIS_PEMERIKSAAN.'" desk="'.$LP->DESK_JENIS_PEMERIKSAAN.'"> Cetak</a>';
    
		  $data[] = array(
			$no,
			$LP->NOMOR_PATMOL.' ('.$LP->DESK_JENIS_PEMERIKSAAN.')',
			$LP->TANGGAL_ORDER ? date_format(date_create($LP->TANGGAL_ORDER), 'd-m-Y H:i:s') : '-',
			$button,
			
		  );
		  
		}
	
		$output = array(
		  "draw"            => $draw,
		  "recordsTotal"    => $this->ModelCoder->total_count_Patmol($nopen),
		  "recordsFiltered" => $this->ModelCoder->filter_count_Patmol($nopen),
		  "data"            => $data
		);
		echo json_encode($output);
	}
  public function getDataPatmol() {
    $id = $this->input->post('id');
    $patmol= $this->ModelCoder->getPatmol($id);

    $data = array(
        'patmol' => $patmol,
    );
    
    if ($patmol) {
        $html = $this->load->view('Coder/hasilLab/hasilPatmol', $data, true);
        echo json_encode(array('success' => true, 'html' => $html));
    } else {
        echo json_encode(array('success' => false));
    }
  }
  //end PA

  public function UpdateCek() {
    $this->db->trans_begin();
    $post = $this->input->post();
    $user_idcoder = $this->session->userdata('id');
    $id =$post['id'];
    $value =$post['value'];

    if($value==1){
      $dataUpdate = array (
          'STATUS'                           => 2,
          'DONE_BY'                          =>$user_idcoder,
          'DONE_DATE'                        => date('Y-m-d H:i:s')

        );
    } else if($value==2){
      $dataUpdate = array (
          'STATUS'                           => 1,
          'DONE_BY'                          =>NULL,
          'DONE_DATE'                        =>NULL,
        );
    }
    // $dataUpdate['DONE_BY'] = $user_idcoder; 
    // $dataUpdate['DONE_DATE'] = date('Y-m-d'); 
      
    // echo "<pre>";print_r($id);exit();
        $this->db->where('db_rekammedis.tb_coding_detail.ID', $id);
        $this->db->update('db_rekammedis.tb_coding_detail', $dataUpdate);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
    }

    public function get_data_cppt() {
      $nopen = $this->input->post('id');
      $result = $this->ModelCoder->datatablesCppt($nopen);
  
      $data = array();
      $no = 1; 
      foreach ($result as $row) {
          $verif = '<h6 style="text-align: center; vertical-align: middle;"><i class="fa fa-minus" aria-hidden="true"></i></h6>';
          $tbak = '<h6 style="text-align: center; vertical-align: middle;"><i class="fa fa-minus" aria-hidden="true"></i></h6>';
          $cetak = '<h6 style="text-align: center; vertical-align: middle;"><i class="fa fa-minus" aria-hidden="true"></i></h6>';
  
          // verifikasi
          if ($row->jenis == 2 && $row->pemberi_cppt == 2) {
              $verif = ($row->status_verif == 0) ? 
                  '<h4 style="text-align: center;"><i class="fa fa-times" aria-hidden="true"></i></h4>' : 
                  '<h4 style="text-align: center;"><i class="fa fa-check" aria-hidden="true"></i></h4>';
          }  
          // TBAK
          if ($row->TBAK == 1) {
              $tbak = '<h4 style="text-align: center;"><i class="fa fa-check" aria-hidden="true"></i></h4>';
          }
          // kondisine cetak cppt
          if ($row->pemberi_cppt == 3) {
            $cetak = '<a id="view-cppt" data-url="http://192.168.7.45/reports/simrskd/cppt/cpptgizi.php?format=pdf&id=' . $row->IDCPPT . '" class="btn btn-warning btn-block btn-sm"><i class="fa fa-print"></i> Cetak</a>';
          } else {
            if (in_array($row->JENIS_KUNJUNGAN, [1, 13, 14])) {
                $cetak = '<a id="view-cppt" data-url="http://192.168.7.45/reports/simrskd/cppt/cpptnew.php?format=pdf&id=' . $row->IDCPPT . '" class="btn btn-warning btn-block btn-sm"><i class="fa fa-print"></i> Cetak</a>';
                if ($row->IDRUANGAN == 105120101) {
                    $cetak = '<a id="view-cppt" data-url="http://192.168.7.45/reports/simrskd/cppt/cpptRadioterapinew.php?format=pdf&id=' . $row->IDCPPT . '" class="btn btn-warning btn-block btn-sm"><i class="fa fa-print"></i> Cetak</a>';
                } elseif ($row->IDRUANGAN == 105110101) {
                    $cetak = '<a id="view-cppt" data-url="http://192.168.7.45/reports/simrskd/cppt/cpptrehab.php?format=pdf&nokun=' . $row->nokun . '&jenis=' . $row->JENIS_CPPT . '" class="btn btn-warning btn-block btn-sm"><i class="fa fa-print"></i> Cetak</a>';
                }
            } elseif (in_array($row->JENIS_KUNJUNGAN, [2, 3])) {
                $cetak = '<a id="view-cppt" data-url="http://192.168.7.45/reports/simrskd/cppt/cpptRawatInap.php?format=pdf&id=' . $row->IDCPPT . '" class="btn btn-warning btn-block btn-sm"><i class="fa fa-print"></i> Cetak</a>';
            }
          }          
          $data[] = array(
              'no' => $no++, 
              'tanggal' => $row->tanggal,
              'verif' => $verif,
              'tbak' => $tbak,
              'ruangan' => $row->RUANGAN,
              'profesi' => $row->PROFESI,
              'user' => $row->NAMAPEGAWAI,
              'dpjp' => $row->DOKTERDPJP,
              'action' => $cetak
          );
      }
  
      $output = array(
          "data" => $data
      );
  
      echo json_encode($output);
  }  
//   public function get_data_tagihan() {
//     $nopen = $this->input->post('id');
//     $result = $this->ModelCoder->datatablesTagihan($nopen);

//     $data = array();
//     $no = 1; 
//     foreach ($result as $row) {
//         // Menggunakan cURL untuk melakukan request
//         $url = 'http://192.168.7.137/webservice/plugins/request-report';
//         $data_post = json_encode(array(
//             "NAME" => "pembayaran.CetakRincianPasienPerGroup",
//             "TYPE" => "Pdf",
//             "EXT" => "pdf",
//             "PARAMETER" => array(
//                 "PTAGIHAN" => $row->TAGIHAN,
//                 "PSTATUS" => 1
//             ),
//             "REQUEST_FOR_PRINT" => false,
//             "PRINT_NAME" => "CetakRincian",
//             "CONNECTION_NUMBER" => 0,
//             "COPIES" => 1,
//             "id" => "data.model.RequestReport-2"
//         ));

//         // Set up cURL options
//         $ch = curl_init($url);
//         curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
//         curl_setopt($ch, CURLOPT_HTTPHEADER, array(
//             'Accept: */*',
//             'Accept-Language: en-US,en',
//             'Content-Type: application/json',
//             'X-Requested-With: XMLHttpRequest',
//             'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
//             'Cookie: PHPSESSID=sbihq1e9a975ls1otrqlf6usr8',
//             'Origin: http://192.168.7.137',
//             'Referer: http://192.168.7.137/apps/SIMpel/'
//         ));
//         curl_setopt($ch, CURLOPT_POST, true);
//         curl_setopt($ch, CURLOPT_POSTFIELDS, $data_post);
//         curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // --insecure option in cURL

//         // Execute cURL
//         $response = curl_exec($ch);
//         if (curl_errno($ch)) {
//             $error_msg = curl_error($ch);
//             $cetak = 'Error: ' . $error_msg;
//         } else {
//             $response_data = json_decode($response, true);
//             if (isset($response_data['url'])) {
//                 // Gunakan URL dari respons untuk membuat link cetak
//                 $report_url = $response_data['url'];
//                 $cetak = '<a href="' . $report_url . '" target="_blank" class="btn btn-warning btn-block btn-sm"><i class="fa fa-print"></i> Cetak</a>';
//             } else {
//                 $cetak = 'Error: URL tidak ditemukan dalam respons.';
//             }
//         }

//         curl_close($ch);

//         $data[] = array(
//             'no' => $no++, 
//             'tagihan' => $row->TAGIHAN,
//             'tanggalTagihan' => $row->TANGGAL,
//             'tanggalBayar' => $row->TANGGALBAYAR,
//             'total' => $row->TOTAL,
//             'action' => $cetak
//         );
//     }

//     $output = array(
//         "data" => $data
//     );

//     echo json_encode($output);
// }
public function get_data_tagihan() {
  $nopen = $this->input->post('id');
  $result = $this->ModelCoder->datatablesTagihan($nopen);

  $data = array();
  $no = 1; 
  foreach ($result as $row) {
      // Menggunakan cURL untuk melakukan request
      $url = 'http://192.168.7.137/webservice/plugins/request-report';
      $data_post = json_encode(array(
          "NAME" => "pembayaran.CetakRincianPasienPerGroup",
          "TYPE" => "Pdf",
          "EXT" => "pdf",
          "PARAMETER" => array(
              "PTAGIHAN" => $row->TAGIHAN,
              "PSTATUS" => 1
          ),
          "REQUEST_FOR_PRINT" => false,
          "PRINT_NAME" => "CetakRincian",
          "CONNECTION_NUMBER" => 0,
          "COPIES" => 1,
          "id" => "data.model.RequestReport-2"
      ));

      // Set up cURL options
      $ch = curl_init($url);
      curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
      curl_setopt($ch, CURLOPT_HTTPHEADER, array(
          'Accept: */*',
          'Accept-Language: en-US,en',
          'Content-Type: application/json',
          'X-Requested-With: XMLHttpRequest',
          'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
          'Cookie: PHPSESSID=sbihq1e9a975ls1otrqlf6usr8',
          'Origin: http://192.168.7.137',
          'Referer: http://192.168.7.137/apps/SIMpel/'
      ));
      curl_setopt($ch, CURLOPT_POST, true);
      curl_setopt($ch, CURLOPT_POSTFIELDS, $data_post);
      curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // --insecure option in cURL

      // Execute cURL
      $response = curl_exec($ch);
      if (curl_errno($ch)) {
          $error_msg = curl_error($ch);
          $cetak = 'Error: ' . $error_msg;
      } else {
          $response_data = json_decode($response, true);
          if (isset($response_data['url'])) {
              $report_url = $response_data['url'];
              $cetak = '<button class="btn btn-warning btn-block btn-sm" data-url="' . $report_url . '" id="view-tagihan"><i class="fa fa-print"></i> Cetak</button>';
          } else {
              $cetak = 'Error: URL tidak ditemukan dalam respons.';
          }
      }

      curl_close($ch);

      $data[] = array(
          'no' => $no++, 
          'tagihan' => $row->TAGIHAN,
          'tanggalTagihan' => $row->TANGGAL,
          'tanggalBayar' => $row->TANGGALBAYAR,
          'total' => $row->TOTAL,
          'action' => $cetak
      );
  }

  $output = array(
      "data" => $data
  );

  echo json_encode($output);
}
//bukti layanan
public function get_data_sep(){
  $nopen = $this->input->post('id');
  $result = $this->ModelCoder->getSep($nopen);
  $data = array();
  $no = 1; 
  foreach ($result as $row) {
  $cetak = '<button id="view-sep" data-url="http://*************/reports/resume_medis/V2.1/resumerajal.php?format=pdf&nopen=' . $row->NOMOR . '" class="btn btn-warning btn-block btn-sm"><i class="fa fa-print"></i> Cetak </button>';

    $data[] = array(
      'no' => $no++, 
      'nopen' => $row->NOMOR,
      'tanggal' => $row->TANGGAL,
      'sep' => $row->SEP,
      'jenis' => $row->DESKRIPSI,
      'action' => $cetak
    );
  }

  $output = array(
    "data" => $data
  );

  echo json_encode($output);
}




  
  
   



    



    

}
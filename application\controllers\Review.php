<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Review extends CI_Controller {    
    public function __construct()
    {
    parent::__construct();
    if($this->session->userdata('logged_in') == FALSE ){
        redirect('login');  
    }
    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('ModelReview'));
    }
    
    public function saveReview(){     
      $ID = $this->input->post('ID');
      $user = $this->session->userdata('id');
      $tgl  = date('Y-m-d H:i:s');
      $this->load->database();

      $dataUpdate = array(
        'ID_INPUT' => $user,
        'STATUS' => 2,
        'TGL_REVIEW' => $tgl,
      );

      $this->db->where('ID_ANALISIS', $ID);
      $this->db->update('db_rekammedis.tb_review', $dataUpdate);       
      if ($this->db->affected_rows() > 0) {
          $response['status'] = 'success';
      } else {
          $response['status'] = 'error';
      }
      echo json_encode($response);     
      
    }

    public function indexAwal(){
      $data=array(      
          'isi'            => 'Review/indexAwal'
      );
      $this->load->view('Layout/Wrapper',$data);
    }

    public function index(){
      // $data=array(      
      //     'isi'            => 'Review/index'
      // );
      $this->load->view('Review/index');
    }
    public function indexHistory(){
      $this->load->view('Review/indexHistory');
    }

    public function getTglDatangOptions(){
      $nomr = $this->input->post('NOMR'); 
      $term = $this->input->post('term');
      $result = $this->ModelReview->getTglDatangOptions($nomr,$term);
      $data = array();
      foreach ($result as $row) {
          $sub_array = array();
          $sub_array['id'] = $row['NOMOR'];
          $sub_array['text'] = date('d-F-Y H:i:s', strtotime($row['TANGGAL']));
          $data[] = $sub_array;
      }
      echo json_encode($data); 
    }

    // public function simpanRev(){
    //   $this->db->trans_begin();
    //   $tglcreate = date('Y-m-d H:i:s');
                    
    //   $data_insert = array (
    //     'ID_INPUT'          => $this->session->userdata('id'),
    //     'TGL_INPUT'         => $tglcreate,
    //     'TGL_MASUK'         => date('Y-m-d H:i:s', strtotime($this->input->post('tglAwal'))),
    //     'TGL_KELUAR'        => date('Y-m-d H:i:s', strtotime($this->input->post('tglPulang'))),
    //     'NOPEN'             => $this->input->post('idtglDatang'),
    //     'NORM'              => $this->input->post('NOMR'),
    //     'NIPDOK'              => $this->input->post('NIPDOK'),
    //     'STATUS'            => 1,        
    //   );
    //   // var_dump($data_insert);
    //   $this->db->insert('db_rekammedis.tb_review', $data_insert);
    //   $id_detail = $this->db->insert_id();

    //   $getData = $this->ModelReview->formReview()->result_array();
    //   $data_insert_detail = array();
    //   foreach ($getData as $row) {
    //       $data_insert_detail[] = array(
    //           'ID_REVIEW' => $id_detail,
    //           'ID_FORM'   => $row['ID'],
    //       );
    //   }
    //   $data_insert_detail = array_unique($data_insert_detail, SORT_REGULAR);
      
    //   if (!empty($data_insert_detail)) {
    //       $this->db->insert_batch('db_rekammedis.tb_review_detail', $data_insert_detail);
    //   }
  
    //   if ($this->db->trans_status() === false) {
    //       $this->db->trans_rollback();
    //       $result = array('status' => 'failed');
    //     } else {
    //       $this->db->trans_commit();
    //       $result = array('status' => 'success');
    //     }
   
    //   echo json_encode($result);
      
    // }
    // public function tbl(){
    //   $listdata = $this->ModelReview->dataTbl();
    //   $data=array();
    //   $no =1;
    //   foreach ($listdata->result() as $field) {
    //     // if ($field->STATUS == 1) {
    //       $button = '<button type="button" class="btn btn-primary btn-rounded btn-sm" title="Add Review Form" onclick="addRev('.$field->ID.')" style="width:72px"><i class="fas fa-tasks"></i></i></button>';         
    //     // } elseif ($field->STATUS == 2) {
    //       // $button = '<button type="button" class="btn btn-primary btn-rounded btn-sm" title="edit data" onclick="editRev('.$field->ID.')" style="width:72px"><i class="fa fa-edit"></i></button>';
    //     //  } 
    //     // $hapus = '<button type="button" class="btn btn-danger btn-rounded btn-sm hapusReview" onclick="hapusReview('.$field->ID.',\''.date('d F Y H:i:s', strtotime($field->TGL_MASUK)).'\',\''.date('d F Y H:i:s', strtotime($field->TGL_KELUAR)).'\')" id="hapusReview" title="hapusRev" style="width:72px"><i class="fa fa-trash"></i></button>';

    //     $tgl_masuk = date('d-F-Y H:i:s', strtotime($field->TGL_MASUK));
    //     $tgl_keluar = date('d-F-Y H:i:s', strtotime($field->TGL_KELUAR));
    //     $tgl_input = date('d-F-Y H:i:s', strtotime($field->TGL_INPUT));
    //     $tgl_rev = ($field->TGL_REVIEW != null) ? date('d-F-Y H:i:s', strtotime($field->TGL_REVIEW)) : " - ";
    //     $data[] = array(
    //       $no,
    //       $tgl_input,
    //       $field->NAMA_INPUT,
    //       $field->NORM,
    //       $field->NAMAPASIEN,
    //       $tgl_masuk,
    //       $tgl_keluar,
    //       $field->NAMADOK,
    //       $field->STATUS_REVIEW,
    //       $tgl_rev,
    //       $button
    //       );
          
    //     $no++;
    //   }

    //   $output = array(
    //     "data"            => $data
    //   );
    //   echo json_encode($output);

    // }
    // public function modalRev(){
    //   $id = $this->input->post('id');
    //   $judul = $this->ModelReview->getJudul($id);
    //   if($judul->num_rows()>0){
    //     $row=$judul->row_array();
    //     $data=[
    //       'ID'                  => $row['ID'],
    //       'NORM'                => $row['NORM'],
    //       'NAMAPASIEN'          => $row['NAMAPASIEN'],
    //       'NAMADOK'             => $row['NAMADOK'],
    //       'TGL'                => $row['TGL_INPUT']
    //     ];
    //   }
    //   $data['form_j1'] = $this->ModelReview->getFormsByJenis(1,$id);
    //   $data['form_j2'] = $this->ModelReview->getFormsByJenis(2,$id);

    //   $msg=[
    //     'sukses'=>$this->load->view('Review/index_modal', $data, true)
    //   ];
    //   echo json_encode($msg);    

    //   // $label = $this->db->where('jenis', 1)
    //   //           ->get('db_rekammedis.tb_review_form')
    //   //           ->result_array();    
    //   // $list = [];
    //   // foreach ($label as $key => $value) :
    //   //     $list[] = [
    //   //         'id' => $value['ID'],
    //   //         'title' => $value['NAMA'],
    //   //         'isi' => $value['ID_ISI'],
    //   //         'data' => $this->getDataJawab($value['ID_ISI'])
    //   //     ];
    //   // endforeach;

    //   // $label1 = $this->db->where('jenis', 2)
    //   // ->get('db_rekammedis.tb_review_form')
    //   // ->result_array();
    //   // $list1 = [];
    //   // foreach ($label1 as $key => $value) :
    //   //   $list1[] = [
    //   //       'id' => $value['ID'],
    //   //       'title' => $value['NAMA'],
    //   //       'isi' => $value['ID_ISI'],
    //   //       'data' => $this->getDataJawab($value['ID_ISI'])
    //   //   ];
    //   // endforeach;

    //   // $data['label'] = $list;
    //   // $data['label1'] = $list1;
    //   // $msg=[
    //   //   'sukses'=>$this->load->view('Review/index_modal', $data, true)
    //   // ];
    //   // echo json_encode($msg);
    // }
    // // public function getDataJawab($id){      
    // //   $sql = "
    // //   SELECT 
    // //       ID,NILAI,DESKRIPSI
    // //   FROM db_rekammedis.tb_analisis_isi
    // //   WHERE JENIS = '$id'
    // //   ORDER BY CAST(ID as SIGNED INTEGER) ASC
    // //   ";
    // //   return $this->db->query($sql)->result_array();
  
    // // }
    // public function simpanReview() {
    //   $this->db->trans_begin();
    //   $id = $this->input->post('id');
    //   $type = $this->input->post('type');     
      
    //   $idIsi = $this->input->post('idIsi');
    //   $nilai = $this->input->post('nilai');
    //   if (!empty($id) && !empty($idIsi) && !empty($nilai)) {
    //       $dataUpdate = array(
    //           'ID_JAWAB' => $idIsi,
    //           'STATUS' => $nilai,
    //       );

    //       $this->db->where('ID', $id);
    //       $this->db->update('db_rekammedis.tb_review_detail', $dataUpdate);

    //   } else {
    //       $this->db->trans_rollback();
    //       $result = array('status' => 'failed', 'message' => 'Data radio tidak valid');
    //       echo json_encode($result);
    //       return;
    //   }  
    //   if ($this->db->trans_status() === FALSE) {
    //       $this->db->trans_rollback();
    //       $result = array('status' => 'failed');
    //   } else {
    //       $this->db->trans_commit();
    //       $result = array('status' => 'success');
    //   }
    //   echo json_encode($result);
    // }
    // // public function saveReview(){     
    // //   $ID = $this->input->post('ID');
    // //   $user = $this->session->userdata('id');
    // //   $tgl  = date('Y-m-d H:i:s');
    // //   $this->load->database();

    // //   $dataUpdate = array(
    // //     'ID_INPUT' => $user,
    // //     'STATUS' => 2,
    // //     'TGL_REVIEW' => $tgl,
    // //   );

    // //   $this->db->where('ID', $ID);
    // //   $this->db->update('db_rekammedis.tb_review', $dataUpdate);       
    // //   if ($this->db->affected_rows() > 0) {
    // //       $response['status'] = 'success';
    // //   } else {
    // //       $response['status'] = 'error';
    // //   }
    // //   echo json_encode($response);     
      
    // // }
   
    // public function getHistory(){
    //   $listdata = $this->ModelReview->dataTblHis();
    //   $data=array();
    //   $no =1;
    //   foreach ($listdata->result() as $field) {
    //     $button = '<button type="button" class="btn btn-primary btn-rounded btn-sm" title="edit data" onclick="editRev('.$field->ID.')" style="width:72px"><i class="fa fa-edit"></i></button>';    
    //     $hapus = '<button type="button" class="btn btn-danger btn-rounded btn-sm hapusReview" onclick="hapusReview('.$field->ID.',\''.date('d F Y H:i:s', strtotime($field->TGL_MASUK)).'\',\''.date('d F Y H:i:s', strtotime($field->TGL_KELUAR)).'\')" id="hapusReview" title="hapusRev" style="width:72px"><i class="fa fa-trash"></i></button>';

    //     $tgl_masuk = date('d-F-Y H:i:s', strtotime($field->TGL_MASUK));
    //     $tgl_keluar = date('d-F-Y H:i:s', strtotime($field->TGL_KELUAR));
    //     $tgl_input = date('d-F-Y H:i:s', strtotime($field->TGL_INPUT));
    //     $tgl_rev = ($field->TGL_REVIEW != null) ? date('d-F-Y H:i:s', strtotime($field->TGL_REVIEW)) : " - ";
    //     $data[] = array(
    //       $no,
    //       $tgl_input,
    //       $field->NAMA_INPUT,
    //       $field->NORM,
    //       $field->NAMAPASIEN,
    //       $tgl_masuk,
    //       $tgl_keluar,
    //       $field->NAMADOK,
    //       $field->STATUS_REVIEW,
    //       $tgl_rev,
    //       $button.''.$hapus
    //       );
          
    //     $no++;
    //   }

    //   $output = array(
    //     "data"            => $data
    //   );
    //   echo json_encode($output);

    // }

   



    // sini

   
    public function modalRevEdit() {
      $id = $this->input->post('id');
      $judul = $this->ModelReview->getJudul($id);
      if($judul->num_rows()>0){
        $row=$judul->row_array();
        $data=[
          'ID'                  => $row['ID'],
          'NORM'                => $row['NORM'],
          'NAMAPASIEN'          => $row['NAMAPASIEN'],
          'NAMADOK'             => $row['NAMADOK'],
          'TGL'                => $row['TGL_REVIEW']
        ];
      }
      $data['form_j1'] = $this->ModelReview->getFormsByJenis(1,$id);
      $data['form_j2'] = $this->ModelReview->getFormsByJenis(2,$id);  

      // $id = $this->input->post('id');
      // $judul = $this->ModelReview->getJudul($id);
  
      // if ($judul->num_rows() > 0) {
      //     $row = $judul->row_array();
      //     $data = [
      //         'ID'         => $row['ID'],
      //         'NORM'       => $row['NORM'],
      //         'NAMAPASIEN' => $row['NAMAPASIEN'],
      //         'NAMADOK'    => $row['NAMADOK'],
      //         'TGL1'       => $row['TGL_MASUK'],
      //         'TGL2'       => $row['TGL_KELUAR']
      //     ];
      // }

      // $label = $this->ModelReview->getLabelByJenis(1,$data['ID']); //jenis 1
      // $list = $this->listEdit($label);
  
      // $label1 = $this->ModelReview->getLabelByJenis(2,$data['ID']); //jenis 2
      // $list1 = $this->listEdit($label1);
  
      // $data['label'] = $list;
      // $data['label1'] = $list1;
  
      $msg = [
          'sukses' => $this->load->view('Review/index_modal_edit', $data, true)
      ];
  
      echo json_encode($msg);
  }
  
  private function listEdit($labels) {
    $list = [];
    foreach ($labels as $key => $value) {
      $list[] = [
        'id'   => $value['ID'],
        'title' => $value['NAMALABEL'],
        'isi'   => $value['ID_ISI'],
        'SELECTED_ID' => $value['SELECTED_ID'],
        'data'  => $this->getDataJawab($value['ID_ISI'])
      ];
    }
    return $list;
  }
  public function saveReviewEdit(){
    $post = $this->input->post('dataToSend');
    foreach ($post as $data) {
      $id = $data['id'];
      $selectedId = $data['selectedId'];
      $selectedNilai = $data['selectedNilai'];

      $dataUpdate = array(
          'ID_JAWAB' => $selectedId,
          'STATUS' => $selectedNilai,
      );

      $this->db->where('ID', $id);
      $this->db->update('db_rekammedis.tb_review_detail', $dataUpdate); 

      if ($this->db->affected_rows() <= 0) {
          $response = array('status' => 'error');
          echo json_encode($response);
      }
    }
    $response = array('status' => 'success');
    echo json_encode($response);

  }
  public function hapusRev(){
    $id = $this->input->post('id');
    $this->db->set('status', 0);
    $this->db->where('ID', $id); 
    $this->db->update('db_rekammedis.tb_review'); 

    $this->db->set('status', 0);
    $this->db->where('ID_REVIEW', $id); 
    $this->db->update('db_rekammedis.tb_review_detail'); 

    $response = array('status' => 'success');
    echo json_encode($response);
  }
  
    



    
}
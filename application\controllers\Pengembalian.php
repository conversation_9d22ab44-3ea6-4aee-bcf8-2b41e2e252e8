<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class <PERSON>gembalian extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if($this->session->userdata('logged_in') == FALSE ){
      redirect('login');  
    }
    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('ModelPengembalian'));
  }

  public function index()
  { 
    // $data = array(      
      
    //   'isi'             => 'Pengembalian/Index'
    // );
    $this->load->view('Pengembalian/Index');
  }

public function getListdata()
  {
    // $draw   = intval($this->input->POST("draw"));
    // $start  = intval($this->input->POST("start"));
    // $length = intval($this->input->POST("length"));

    // $button = '<div class="btn-group">
    //               <button type="button" class="btn btn-light dropdown-toggle waves-effect" data-toggle="dropdown" aria-expanded="false">
    //                   <i class="mdi mdi-folder font-18 vertical-middle"></i>
    //                   <i class="mdi mdi-chevron-down"></i>
    //               </button>
    //               <div class="dropdown-menu">
    //                   <a class="dropdown-item" href="javascript: void(0);">Detail</a>
    //                   <a class="dropdown-item" href="javascript: void(0);">Edit</a>
    //                   <a class="dropdown-item" href="javascript: void(0);">Hapus</a>
    //               </div>
    //           </div>';

    $listdata = $this->ModelPengembalian->tampildata();
    $data=array();
    $no =1;
    foreach ($listdata->result() as $field) {
      
      $button = '<button type="button" class="btn btn-primary" data-toggle="modal" data-target="#ModalPengembalian" data-id="'.$field->ID.'">
  Terima Pengembalian Rekam Medis
</button>';

      $data[] = array(
        $no,
        $field->TANGGAL,
        $field->NOMR,
        $field->NAMAPASIEN,
        $field->nama_dokter,
        $field->unit,
        $field->nama_lengkap,
        $button
      );
      $no++;

    }

    $output = array(
      // "draw"            => $draw,
      // "recordsTotal"    => $listdata->num_rows(),
      // "recordsFiltered" => $listdata->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }

public function list_ruangan()
    {
        $result = $this->ModelCoderAdmin->list_Ruangan();
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row['ID_SIMPEL'];
            $sub_array['text'] = $row['DESKRIPSI'];
            $data[] = $sub_array;
        }
        echo json_encode($data);
    }

public function get_dokter()
    {
        $result = $this->ModelPendistribusian->list_dokter();
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row['ID'];
            $sub_array['text'] = $row['nama_dokter'];
            $data[] = $sub_array;
        }
        echo json_encode($data);
    }

public function list_filing()
    {
        $result = $this->ModelPendistribusian->list_Filing();
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row['id'];
            $sub_array['text'] = $row['nama_lengkap'];
            $data[] = $sub_array;
        }
        echo json_encode($data);
    }

public function get_results($val, $qt)
  {
    $division = intdiv($val, $qt); // PHP <7: $division = ($val - ($val % $qt)) / $qt;
    $ret = array_fill(0, $qt, $division); // fill array with $qt equal values

    if($division != $val / $qt) // if not whole division, add remaning to lsat element
    { 
      $ret[count($ret)-1] = $ret[0] + ($val % $qt);
    }

    return $ret;
  }
   
public function TarikDataCoding()
{
  if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {

  $TANGGAL = $_POST['TANGGAL'];
  $tgl_awal = $TANGGAL.' '.'00:00:00';
  $tgl_akhir = $TANGGAL.' '.'23:59:59';
  $ruangan1 =$_POST['RUANGAN'];
  $ID_PETUGAS = $_POST['CODER'];
  $jmlpetugas = count($_POST['CODER']);

  
  $result=mysqli_query($conn1,$sql); 
              // echo "<pre>";print_r($result);exit();
  $JUMLAH = $result->num_rows;

  $hasil = get_results($JUMLAH, $jmlpetugas);

  $listdata = $this->ModelCoderAdmin->TarikData();
  $data=array();
  $no =1;
    foreach ($listdata->result() as $field) {

      $data[] = array(
        $no,
        $field->NORM, 
        $field->NAMA_PASIEN,
        // date_format($date,'Y-m-d'),
        $field->TANGGAL_PENDAFTARAN,
        $field->DESKRIPSI,
        $edit        
      );
      $no++;

    }

    $output = array(
      // "draw"            => $draw,
      // "recordsTotal"    => $listdata->num_rows(),
      // "recordsFiltered" => $listdata->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }
}

public function simpanPengembalian()
{
    $this->db->trans_begin();

    $post = $this->input->post();
    $tglcreate = date('Y-m-d H:i:s');

    $id = $post['ID'];
    $data_edit = array(
      'STATUS' => 2
    );

    $this->db->where('ID', $id);
    $this->db->update('db_rekammedis.tb_pendistribusian', $data_edit);
                 
    $data_insert = array(
        'ID_DISTRIBUSI' => $id,
        'RUANGAN' => $this->input->post('ruangan'),
        'OLEH' => $this->session->userdata('id'),
    );

    // Insert data into tb_lembarlepas_pengembalian
    $this->db->insert('db_rekammedis.tb_lembarlepas_pengembalian', $data_insert);
    $id_pengembalian = $this->db->insert_id(); // Get the last inserted ID

    // Insert each lembar into tb_lembarlepas_pengembalian_detail
    $lembar_array = $this->input->post('lembar');
    if (!empty($lembar_array) && is_array($lembar_array)) {
    foreach ($lembar_array as $lembar) {
        $data_detail_insert = array(
            'ID_PENGEMBALIAN' => $id_pengembalian,
            'LEMBAR' => $lembar
        );
        $this->db->insert('db_rekammedis.tb_lembarlepas_pengembalian_detail', $data_detail_insert);
    }
    }

    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
    } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
    }

    echo json_encode($result);
}

  function getListLembar()
	{
    $ruangan = $this->input->post('id');

    $listCeklis = $this->ModelPengembalian->list_lembar($ruangan);
  
    echo json_encode($listCeklis);
	}

  function modalEditPengembalian()
	{
		$id = $this->input->post('id');
    $ruangan = $this->input->post('ruangan');

		// $ruangan = $post['ruangan'];
    $listCeklis = $this->ModelPengembalian->list_lembar($ruangan);
		// $gpFormBMN = $this->Inventory_model->getDataInventoryId($id)->row_array();
	
		$data = array(
		  'id'              => $id,
      'listCeklis'		  => $listCeklis,
		);
  
	  // $this->load->view('Inventory/v_editBMN_modal', $data);
    $this->load->view('Pengembalian/modal_pengembalian', $data);
	}

  public function getHistoryData()
  {
    // $draw   = intval($this->input->POST("draw"));
    // $start  = intval($this->input->POST("start"));
    // $length = intval($this->input->POST("length"));

    // $button = '<div class="btn-group">
    //               <button type="button" class="btn btn-light dropdown-toggle waves-effect" data-toggle="dropdown" aria-expanded="false">
    //                   <i class="mdi mdi-folder font-18 vertical-middle"></i>
    //                   <i class="mdi mdi-chevron-down"></i>
    //               </button>
    //               <div class="dropdown-menu">
    //                   <a class="dropdown-item" href="javascript: void(0);">Detail</a>
    //                   <a class="dropdown-item" href="javascript: void(0);">Edit</a>
    //                   <a class="dropdown-item" href="javascript: void(0);">Hapus</a>
    //               </div>
    //           </div>';

    $listdata = $this->ModelPengembalian->tampildataHistory();
    $data=array();
    $no =1;
    foreach ($listdata->result() as $field) {
      
      $button = '<button type="button" class="btn btn-primary" data-toggle="modal" data-target="#ModalPengembalian" data-id="'.$field->ID.'">
  Terima Pengembalian Rekam Medis
</button>';

      $data[] = array(
        $no,
        $field->TANGGAL,
        $field->NOMR,
        $field->NAMAPASIEN,
        $field->nama_dokter,
        $field->unit,
        $field->nama_lengkap,
        $field->DESKRIPSI !== NULL ? $field->DESKRIPSI : '-',
        $button
      );
      $no++;

    }

    $output = array(
      // "draw"            => $draw,
      // "recordsTotal"    => $listdata->num_rows(),
      // "recordsFiltered" => $listdata->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }

}
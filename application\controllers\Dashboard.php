<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Dashboard extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if($this->session->userdata('logged_in') == FALSE ){
      redirect('login');  
    }
    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('ModelDashboard'));
  }

  public function chart_data() {
    $data = $this->ModelDashboard->ListTabelPks();
    echo json_encode($data);
  }

  public function index()
  {
    $data=array(      
      'listpks'   => $this->ModelDashboard->ListTabelPks(),
      'isi'       => 'Dashboard/Index'
    );
    $this->load->view('Layout/Wrapper',$data, FALSE);
  }

  public function chart()
    {
        // Ambil data dari model
        $data_from_model = $this->ModelDashboard->ListTabelPks();

        // Format data untuk grafik
        $labels = [];
        $data = [];
        foreach ($data_from_model as $row) {
            $labels[] = $row['KATEGORI'];
            $data[] = $row['JUMLAH'];
        }

        // Kirim data ke tampilan
        $data['labels'] = json_encode($labels);
        $data['jumlah'] = json_encode($data);
        $data['chart_title'] = 'Grafik PKS';

        // Load tampilan
        $this->load->view('Dashboard/Index/myChart', $data);
    }


}
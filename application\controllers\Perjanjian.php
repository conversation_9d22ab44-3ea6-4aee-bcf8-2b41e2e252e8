<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class <PERSON><PERSON><PERSON><PERSON> extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if($this->session->userdata('logged_in') == FALSE ){
      redirect('login');  
    }
    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('ModelPerjanjian'));
  }

  public function indexHistory(){    
    $data=array(   
      'isi'            => 'Perjanjian/IndexHistory'
    );
    $this->load->view('Layout/Wrapper',$data);
  }  
  
  public function dokter(){
    $result = $this->ModelPerjanjian->dokter();
    $data = array();
    foreach ($result as $row) {
        $sub_array = array();
        $sub_array['id'] = $row -> ID;
        $sub_array['text'] = $row -> DOKTER;
        $data[] = $sub_array;
    }
    echo json_encode($data);
  }
  public function ruangan()
  {
      $result = $this->ModelPerjanjian->ruangan();
      $data = array();
      foreach ($result as $row) {
          $sub_array = array();
          $sub_array['id'] = $row -> ID;
          $sub_array['text'] = $row -> DESKRIPSI;
          $data[] = $sub_array;
      }
      echo json_encode($data);
  }
  public function rencana()
  {
      $result = $this->ModelPerjanjian->rencana();
      $data = array();
      foreach ($result as $row) {
          $sub_array = array();
          $sub_array['id'] = $row -> ID;
          $sub_array['text'] = $row -> DESKRIPSI;
          $data[] = $sub_array;
      }
      echo json_encode($data);
  }

  // public function history()
  // {
  //     $start = $this->input->post('start');
  //     $length = $this->input->post('length');
  //     $searchValue = $this->input->post('search')['value']; 
  
  //     // Ambil data dari model
  //     $result = $this->ModelPerjanjian->getHistory($start, $length, $searchValue);
  //     $data = array();
  //     foreach ($result as $row) {
  //         $sub_array = array();
  //         $sub_array[] = $row->ID;
  //         $sub_array[] = $row->NAMAPASIEN . "<b> [ " . $row->NOMR . " ]</b>";
  //         $sub_array[] =  date('d-m-Y', strtotime($row->TANGGAL));
  //         $sub_array[] = $row->NOKONTROL;
  //         $sub_array[] = $row->DOKTER;
  //         $sub_array[] = $row->RUANGAN;
  //         $sub_array[] = $row->RENCANA;
  //         $sub_array[] = ($row->STATUS_SORE == 1) ? "Poli Sore" : "Poli Pagi";
  
  //         $data[] = $sub_array;
  //     }
  //     $output = array(
  //         "draw" => intval($this->input->post('draw')),
  //         "recordsTotal" => $this->ModelPerjanjian->total_count(),
  //         "recordsFiltered" => $this->ModelPerjanjian->filter_count($searchValue),
  //         "data" => $data
  //     );
  //     echo json_encode($output);
  // }
  public function history()
  {
    $draw = intval($this->input->post("draw"));
    $start = $this->input->post('start');  
    $length = $this->input->post('length'); 
    $searchValue = $this->input->post('search')['value'];  
    
    $listdata = $this->ModelPerjanjian->getHistory($start, $length, $searchValue);
    
    $data = array();
    foreach ($listdata['data'] as $row)  {
        $sub_array = array();
        $sub_array[] = $row->ID;
        $sub_array[] = $row->NAMAPASIEN . "<b> [ " . $row->NOMR . " ]</b>";
        $sub_array[] = date('d-m-Y', strtotime($row->TANGGAL));  
        $sub_array[] = $row->NOKONTROL;
        $sub_array[] = $row->DOKTER;
        $sub_array[] = $row->RUANGAN;
        $sub_array[] = $row->RENCANA;
        $sub_array[] = ($row->STATUS_SORE == 1) ? "Poli Sore" : "Poli Pagi";  

        $data[] = $sub_array;
    }

    $output = array(
      "draw" => $draw,
      "recordsTotal" => $listdata['recordsTotal'],
      "recordsFiltered" => $listdata['recordsFiltered'],
      "data" => $data
    );

    echo json_encode($output);  
  }

  
    

}
<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Input extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if($this->session->userdata('logged_in') == FALSE ){
      redirect('login');  
    }
    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('ModelInput'));
  }

  public function index()
  {
    $data=array(      
      'listkat'        => $this->ModelInput->tampilKategori(),
      'listperusahaan' => $this->ModelInput->listPerusahaan(),
      'listno'         => $this->ModelInput->listnoPks(),
      'isi'            => 'Input/Index'
    );
    $this->load->view('Layout/Wrapper',$data, FALSE);
  }

public function getlistPks()
  {
    $draw   = intval($this->input->POST("draw"));
    $start  = intval($this->input->POST("start"));
    $length = intval($this->input->POST("length"));

    $listpks = $this->ModelInput->datalistPks();
    $data=array();
    $no =1;
    foreach ($listpks->result() as $field) {

      $proses = '<button type="button" class="btn btn-info btn-sm" title="proses data" onclick="prosesinput('.$field->ID_INPUT_PKS.')" style="width:72px"><i class="fa fa-file"></i> Proses</button>';

      $edit = '<button type="button" class="btn btn-warning btn-sm" title="edit data" onclick="editinput('.$field->ID_INPUT_PKS.')" style="width:72px"><i class="fa fa-edit"></i> Edit</button>';

      $lihatdata = '<button type="button" class="btn btn-primary btn-sm" title="lihat data" onclick="lihatinput('.$field->ID_INPUT_PKS.')" style="width:72px"><i class="fa fa-eye"></i> Lihat</button>';

      $hapus = '<button type="button" class="btn btn-danger btn-sm hapus_input" href="javaScript:;" id="hapus_input" title="hapus data" data="'.$field->ID_INPUT_PKS.'" style="width:72px"><i class="fa fa-trash"></i> Hapus</button>';

      $button = '<button type="button" class="btn btn-success btn-sm lihatFile" href="javascript:;" file="'.$field->FILE.'" style="width:72px"><i class="fa fa-file"></i> File</button>';

      $upload = '<button type="button" class="btn btn-primary btn-sm" title="edit data" onclick="uploadinput('.$field->ID_INPUT_PKS.')" style="width:72px"><i class="fa fa-upload"></i> Upload</button>';

      // $upload = '<button type="button" class="btn btn-warning btn-sm uploadFile" href="javascript:;" file="'.$field->FILE.'"><i class="fa fa-upload"></i> Upload</button>';     

      // $button = '<a class="btn btn-success lihatFile" href="javascript:;" file="'.$field->FILE.'"> <i class="fa fa-eye"></i> Lihat</a>';

      $file = '<div class="input-group-btn">
        <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown">
        <i class="fa fa-file"></i> File
        <span class="fa fa-caret-down"></span></button>
        <ul class="dropdown-menu">
        <li><a class="lihatFile" href="javascript:;" file="'.$field->FILE.'"> Lihat File</a></li>
        <li><a onclick="uploadinput('.$field->ID_INPUT_PKS.')" href="javascript:;"> Upload File</a></li>
        </ul>
        </div>';

      $aksi = '<div class="input-group-btn">
        <button type="button" class="btn btn-warning dropdown-toggle" data-toggle="dropdown">
        <i class="fa fa-edit"></i> Aksi
        <span class="fa fa-caret-down"></span></button>
        <ul class="dropdown-menu">
        <li><a onclick="prosesinput('.$field->ID_INPUT_PKS.')" href="javascript:;"> Proses</a></li>
        <li><a onclick="editinput('.$field->ID_INPUT_PKS.')" href="javascript:;"> Edit</a></li>
        <li><a class="hapus_input" href="javaScript:;" data="'.$field->ID_INPUT_PKS.'"> Hapus</a></li>
        </ul>
        </div>';

      $tgl = date_create(date('Y-m-d'));
      $due = date_create($field->DUE_DATE);
      $diff = date_diff($tgl, $due);
      $sisa2 = $diff->format("%r%a Hari");
      $proses = $field->PRO_STATUS;

      if ($proses == 3) {
        $sisa3 = '<span class="badge bg-green"> Selesai </span>';
      } elseif ($sisa2 > 0) {
        if ($sisa2 <= 10) {
          $sisa3 = '<span class="badge bg-yellow">' . $sisa2 . '</span>';
        } else {
          $sisa3 = $sisa2;
        }
      } else {
        $sisa3 = '<span class="badge bg-red">' . $sisa2 . '</span>';
      }
      
      switch ($field->PRO_STATUS) {
        case '1':
          $pro = '<span class="badge bg-aqua">Proses</span>';
          break;
        
        case '2':
          $pro = '<span class="badge bg-yellow">Pending</span>';
          break;

        case '3':
          $pro = '<span class="badge bg-green">Selesai</span>';
          break;

        case '4':
          $pro = '<span class="badge bg-red">Cancel</span>';
          break;

        case '5':
          $pro = '<span class="badge bg-purple">Putus</span>';
          break;
      }

      if($field->NO_PKS_INDUK == ""){
        $add = $field->NO_PKS;
      } else {
        $add = $field->NO_PKS.'<br>'.'add '.$field->NO_PKS_INDUK;
      }

      // if($this->session->userdata('id')!='2813'){
      //     $aksi = $lihatdata;
      //     if($field->FILE==""){
      //         $file = "File Belum Diupload!";              
      //     } else {
      //         $file=$button;
      //     }
      //   } 

      $data[] = array(
        $no,
        $field->PERUSAHAAN, 
        // $field->NO_PKS,
        $add,
        date_indo($field->TGL_BERLAKU),
        date_indo($field->TGL_BERAKHIR),
        $field->KETERANGAN,
        $field->KODE_PRODUKSI,
        $field->DUE_DATE,
        $sisa3,
        $pro,
        $file,
        $aksi
      );
      $no++;
    }

    $output = array(
      "draw"            => $draw,
      "recordsTotal"    => $listpks->num_rows(),
      "recordsFiltered" => $listpks->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }

public function simpanInput()
  {
    $post = $this->input->post();
    // echo "<pre>";print_r($post);exit();
    $this->db->trans_begin();

    $tglcreate = date('Y-m-d H:i:s');
                 
    $data_insert = array (
      'ID_PERUSAHAAN'       => $this->input->post('PERUSAHAAN'),
      'ID_KATEGORI'         => $this->input->post('KATEGORI'),
      'NO_PKS'              => $this->input->post('NO_PKS'), 
      'TGL_BERLAKU'         => $this->input->post('TGL_BERLAKU'),
      'TGL_BERAKHIR'        => $this->input->post('TGL_BERAKHIR'),
      'KETERANGAN'          => $this->input->post('KETERANGAN'),
      'KODE_PRODUKSI'       => $this->input->post('KODE_PRODUKSI'),
      'DUE_DATE'            => $this->input->post('DUE_DATE'),
      // 'FILE'             => $this->input->post('FILE_PKS'),
      // 'FILE'                => $berkas['file_name'],
      'CREATED_BY'          => $this->session->userdata('id'),
      'CREATED_DATE'        => $tglcreate,
      'STATUS'              => 1
    );
   // echo "<pre>";print_r($data_insert);exit();

    $this->db->insert('db_pks.tb_input_pks', $data_insert);

    $id_input_pks = $this->db->insert_id();

    $data_insert_proses = array(
      'ID_INPUT_PKS' => $id_input_pks, // Gunakan ID_INPUT_PKS yang sama
      'TGL_PROSES' => $this->input->post('TGL_PROSES'),
      'KET_PROSES' => $this->input->post('PROSES_PERTAMA'),
      'STATUS' => 1
    );

    $this->db->insert('db_pks.tb_proses_pks', $data_insert_proses);

    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
      } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
      }
 
    echo json_encode($result);
    
  }

public function simpanInputAdendum()
  {
    $post = $this->input->post();
    // echo "<pre>";print_r($post);exit();
    $this->db->trans_begin();

    $tglcreate = date('Y-m-d H:i:s');
                 
    $data_insert = array (
      'ID_PERUSAHAAN'       => $this->input->post('PERUSAHAAN_ADENDUM'),
      'ID_KATEGORI'         => $this->input->post('KATEGORI_ADENDUM'),
      'NO_PKS'              => $this->input->post('NO_PKS_ADENDUM'), 
      'TGL_BERLAKU'         => $this->input->post('TGL_BERLAKU_ADENDUM'),
      'TGL_BERAKHIR'        => $this->input->post('TGL_BERAKHIR_ADENDUM'),
      'KETERANGAN'          => $this->input->post('KETERANGAN_ADENDUM'),
      'KODE_PRODUKSI'       => $this->input->post('KODE_PRODUKSI_ADENDUM'),
      'DUE_DATE'            => $this->input->post('DUE_DATE_ADENDUM'),
      // 'FILE'             => $this->input->post('FILE_PKS'),
      // 'FILE'                => $berkas['file_name'],
      'CREATED_BY'          => $this->session->userdata('id'),
      'CREATED_DATE'        => $tglcreate,
      'STATUS'              => 1
    );
   // echo "<pre>";print_r($data_insert);exit();

    $this->db->insert('db_pks.tb_input_pks', $data_insert);

    $id_input_pks = $this->db->insert_id();

    $data_insert_proses = array(
      'ID_INPUT_PKS' => $id_input_pks, // Gunakan ID_INPUT_PKS yang sama
      'TGL_PROSES' => $this->input->post('TGL_PROSES_ADENDUM'),
      'KET_PROSES' => $this->input->post('PROSES_PERTAMA_ADENDUM'),
      'STATUS' => 1
    );

    $this->db->insert('db_pks.tb_proses_pks', $data_insert_proses);

    $data_insert_adendum = array(
      'ID_INPUT_PKS' => $id_input_pks, // Gunakan ID_INPUT_PKS yang sama
      'NO_PKS_INDUK' => $this->input->post('NO_PKS_INDUK'),
      'NO_PKS_ADENDUM' => $this->input->post('NO_PKS_ADENDUM'),
      'STATUS' => 1
    );

    $this->db->insert('db_pks.tb_adendum', $data_insert_adendum);

    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
      } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
      }
 
    echo json_encode($result);
    
  }

public function simpanInputXXX()
  {
    $post = $this->input->post();
    // echo "<pre>";print_r($post);exit();
    $this->db->trans_begin();

    $tglcreate = date('Y-m-d H:i:s');

    $config['upload_path'] = FCPATH .'/berkas/';
    $config['allowed_types'] = 'pdf';
    $config['max_size']     = '3000';
    $config['file_name'] = date('YmdHis').$this->input->post('PERUSAHAAN').'.pdf';

    $this->upload->initialize($config);
        
    $berkas = $this->upload->data();

    // $now = time();
    // $this->load->library('upload', $config);

    if($this->upload->do_upload('FILE_PKS')){
    //   $berkas = $this->upload->data();
                  
      $data_insert = array (
        'ID_PERUSAHAAN'       => $this->input->post('PERUSAHAAN'),
        'NO_PKS'              => $this->input->post('NO_PKS'), 
        'TGL_BERLAKU'         => $this->input->post('TGL_BERLAKU'),
        'TGL_BERAKHIR'        => $this->input->post('TGL_BERAKHIR'),
        'KETERANGAN'          => $this->input->post('KETERANGAN'),
        'KODE_PRODUKSI'       => $this->input->post('KODE_PRODUKSI'),
        'DUE_DATE'            => $this->input->post('DUE_DATE'),
        // 'FILE'             => $this->input->post('FILE_PKS'),
        'FILE'                => $berkas['file_name'],
        'CREATED_BY'          => $this->session->userdata('id'),
        'CREATED_DATE'        => $tglcreate,
        'STATUS'              => 1
      );
   // echo "<pre>";print_r($data_insert);exit();

    $this->db->insert('db_pks.tb_input_pks', $data_insert);

    $id_input_pks = $this->db->insert_id();

    $data_insert_proses = array(
      'ID_INPUT_PKS' => $id_input_pks, // Gunakan ID_INPUT_PKS yang sama
      'TGL_PROSES' => $this->input->post('TGL_PROSES'),
      'KET_PROSES' => $this->input->post('PROSES_PERTAMA'),
      'STATUS' => 1
    );

    $this->db->insert('db_pks.tb_proses_pks', $data_insert_proses);

      if ($this->db->trans_status() === false) {
          $this->db->trans_rollback();
          $result = array('status' => 'failed');
      } else {
          $this->db->trans_commit();
          $result = array('status' => 'success');
      }

    } else {
      //   $result = array('status' => 'noupload');
    }
    
    echo json_encode($result);
    
  } 

public function HapusInput()
    {
    $this->db->trans_begin();
    $post = $this->input->post();
    $id =$post['ID'];

    if($post['PAR']='deactivated')
    {
      $dataUpdate = array (
          'STATUS'                           => 0,
        );
    }
    // echo "<pre>";print_r($id);exit();
        $this->db->where('db_pks.tb_input_pks.ID_INPUT_PKS', $id);
        $this->db->update('db_pks.tb_input_pks', $dataUpdate);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
    }


  public function UpdateInputXXX()
  {
    $post = $this->input->post();
    $this->db->trans_begin();
    $id = $this->input->post('ID');
    $tgledit = date('Y-m-d H:i:s');

    if($_FILES['FILE_PKS_EDIT']['name'] == ""){

      $data_insert = array (
        'NO_PKS'              => $this->input->post('NO_PKS_EDIT'), 
        'TGL_BERLAKU'         => $this->input->post('TGL_BERLAKU_EDIT'),
        'TGL_BERAKHIR'        => $this->input->post('TGL_BERAKHIR_EDIT'),
        'KETERANGAN'          => $this->input->post('KETERANGAN_EDIT'),
        'KODE_PRODUKSI'       => $this->input->post('KODE_PRODUKSI_EDIT'),
        'DUE_DATE'            => $this->input->post('DUE_DATE_EDIT'),
        'UPDATED_BY'          => $this->session->userdata('id'),
        'UPDATED_DATE'        => $tgledit,
      );
    // echo "<pre>";print_r($data_insert);

    } else {

      $config['upload_path'] = FCPATH .'/berkas/';
      $config['allowed_types'] = 'pdf';
      $config['max_size']     = '3000';
      $config['file_name'] = date('YmdHis').$this->input->post('ID_PERUSAHAAN').'.pdf';

      $this->upload->initialize($config);
      $berkas = $this->upload->data();

      if($this->upload->do_upload('FILE_PKS_EDIT')){

        $data_insert = array (
          'NO_PKS'              => $this->input->post('NO_PKS_EDIT'), 
          'TGL_BERLAKU'         => $this->input->post('TGL_BERLAKU_EDIT'),
          'TGL_BERAKHIR'        => $this->input->post('TGL_BERAKHIR_EDIT'),
          'KETERANGAN'          => $this->input->post('KETERANGAN_EDIT'),
          'KODE_PRODUKSI'       => $this->input->post('KODE_PRODUKSI_EDIT'),
          'DUE_DATE'            => $this->input->post('DUE_DATE_EDIT'),
          'FILE'                => $berkas['file_name'],
          'UPDATED_BY'          => $this->session->userdata('id'),
          'UPDATED_DATE'        => $tgledit,
        );
        // echo "<pre>";print_r($data_insert);
    

        $this->db->where('db_pks.tb_input_pks.ID_INPUT_PKS', $id);
        $this->db->update('db_pks.tb_input_pks', $data_insert);

        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
        } else {
          $this->db->trans_commit();
          $result = array('status' => 'success');
        }
      }
      echo json_encode($result);
    }
  }

public function UpdateInput()
{
    $post = $this->input->post();
    $this->db->trans_begin();
    $id = $this->input->post('ID');
    $tgledit = date('Y-m-d H:i:s');
    $data_insert = array(
        'NO_PKS'        => $this->input->post('NO_PKS_EDIT'), 
        'TGL_BERLAKU'   => $this->input->post('TGL_BERLAKU_EDIT'),
        'TGL_BERAKHIR'  => $this->input->post('TGL_BERAKHIR_EDIT'),
        'KETERANGAN'    => $this->input->post('KETERANGAN_EDIT'),
        'KODE_PRODUKSI' => $this->input->post('KODE_PRODUKSI_EDIT'),
        'DUE_DATE'      => $this->input->post('DUE_DATE_EDIT'),
        'UPDATED_BY'    => $this->session->userdata('id'),
        'UPDATED_DATE'  => $tgledit,
    );

    // Cek apakah ada file yang diunggah
    if(isset($_FILES['FILE_PKS_EDIT']) && $_FILES['FILE_PKS_EDIT']['name'] != "") {
        $config['upload_path'] = FCPATH .'/berkas/';
        $config['allowed_types'] = 'pdf';
        $config['max_size']     = '3000';
        $config['file_name'] = date('YmdHis').$this->input->post('ID_PERUSAHAAN').'.pdf';

        $this->upload->initialize($config);
        
        if($this->upload->do_upload('FILE_PKS_EDIT')) {
            // Jika file berhasil diunggah, tambahkan nama file baru ke dalam data yang akan dimasukkan
            $berkas = $this->upload->data();
            $data_insert['FILE'] = $berkas['file_name'];
        } else {
            // Jika gagal unggah file, atur pesan kesalahan dan kembalikan respon
            $error = array('error' => $this->upload->display_errors());
            echo json_encode(array('status' => 'failed', 'error' => $error));
            $this->db->trans_rollback();
            return;
        }
    }

    // Lakukan pembaruan di database
    $this->db->where('ID_INPUT_PKS', $id);
    $this->db->update('db_pks.tb_input_pks', $data_insert);

    // Periksa status transaksi dan kirimkan respons sesuai dengan itu
    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
    } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
    }

    echo json_encode($result);
}


  public function modalEditpks()
  {
    if($this->input->is_ajax_request()==true){
      $id = $this->input->post('id');
      $ambildata = $this->ModelInput->editlistInput($id);
      if($ambildata->num_rows()>0){
        $row=$ambildata->row_array();
        $data=[
          
          // 'listkat'           => $this->ModelPerusahaan->tampilKategori(),
          'ID_INPUT_PKS'        => $row['ID_INPUT_PKS'],
          'ID_PERUSAHAAN'       => $row['ID_PERUSAHAAN'],
          'NO_PKS'              => $row['NO_PKS'],
          'TGL_BERLAKU'         => $row['TGL_BERLAKU'],
          'TGL_BERAKHIR'        => $row['TGL_BERAKHIR'],
          'KETERANGAN'          => $row['KETERANGAN'],
          'KODE_PRODUKSI'       => $row['KODE_PRODUKSI'],
          'DUE_DATE'            => $row['DUE_DATE'],
          'FILE'                => $row['FILE'],
          'PERUSAHAAN'          => $row['PERUSAHAAN'],
          'KATEGORI'            => $row['KATEGORI'],
          'TGL_PROSES'          => $row['TGL_PROSES'],
          'KET_PROSES'          => $row['KET_PROSES'],

        ];
      }
      $msg=[
        'sukses'=>$this->load->view('Input/modal_edit_pks', $data, true)
      ];
      echo json_encode($msg);
    }
  }

public function modalLihatpks()
  {
    if($this->input->is_ajax_request()==true){
      $id = $this->input->post('id');
      $ambildata = $this->ModelInput->editlistInput($id);

      foreach ($ambildata->result() as $field) {
        if($field->NO_PKS_INDUK == ""){
          $add = $field->NO_PKS;
        } else {
          $add = $field->NO_PKS.' add: '.$field->NO_PKS_INDUK;
        }

        $tgl = date_create(date('Y-m-d'));
        $due = date_create($field->DUE_DATE);
        $diff = date_diff($tgl, $due);
        $sisa2 = $diff->format("%r%a Hari");
        $proses = $field->PRO_STATUS;

        if ($proses == 3) {
          $sisa3 = '<span class="badge bg-green"> Selesai </span>';
        } elseif ($sisa2 > 0) {
          if ($sisa2 <= 10) {
            $sisa3 = '<span class="badge bg-yellow">' . $sisa2 . '</span>';
          } else {
            $sisa3 = $sisa2;
          }
        } else {
          $sisa3 = '<span class="badge bg-red">' . $sisa2 . '</span>';
        }
      
        switch ($field->PRO_STATUS) {
        case '1':
          $pro = '<span class="badge bg-aqua">Proses</span>';
          break;
        
        case '2':
          $pro = '<span class="badge bg-yellow">Pending</span>';
          break;

        case '3':
          $pro = '<span class="badge bg-green">Selesai</span>';
          break;

        case '4':
          $pro = '<span class="badge bg-red">Cancel</span>';
          break;

        case '5':
          $pro = '<span class="badge bg-purple">Putus</span>';
          break;
        }

      }

      if($ambildata->num_rows()>0){
        $row=$ambildata->row_array();
        $data=[
          
          // 'listkat'           => $this->ModelPerusahaan->tampilKategori(),
          'ID_INPUT_PKS'        => $row['ID_INPUT_PKS'],
          'ID_PERUSAHAAN'       => $row['ID_PERUSAHAAN'],
          'NO_PKS'              => $row['NO_PKS'], 
          'TGL_BERLAKU'         => $row['TGL_BERLAKU'],
          'TGL_BERAKHIR'        => $row['TGL_BERAKHIR'],
          'KETERANGAN'          => $row['KETERANGAN'],
          'KODE_PRODUKSI'       => $row['KODE_PRODUKSI'],
          'DUE_DATE'            => $row['DUE_DATE'],
          'FILE'                => $row['FILE'],
          'PERUSAHAAN'          => $row['PERUSAHAAN'],
          'KATEGORI'            => $row['KATEGORI'],
          'TGL_PROSES'          => $row['TGL_PROSES'],
          'KET_PROSES'          => $row['KET_PROSES'],
          'NO_PKS_INDUK'        => $add,
          'pro' => $pro,

        ];
      }
      $msg=[
        'sukses'=>$this->load->view('Input/modal_lihat_pks', $data, true)
      ];
      echo json_encode($msg);
    }
  }

  public function modalProsespks()
  {
    if($this->input->is_ajax_request()==true){
      $id = $this->input->post('id');
      $ambildata = $this->ModelInput->editlistInput($id);
      if($ambildata->num_rows()>0){
        $row=$ambildata->row_array();
        $data=[
          
          // 'listkat'           => $this->ModelPerusahaan->tampilKategori(),
          'ID_INPUT_PKS'        => $row['ID_INPUT_PKS'],
          'ID_PROSES'           => $row['ID_PROSES'],
          'ID_PERUSAHAAN'       => $row['ID_PERUSAHAAN'],
          'NO_PKS'              => $row['NO_PKS'],
          'PERUSAHAAN'          => $row['PERUSAHAAN'],
          'KATEGORI'            => $row['KATEGORI'],
          'TGL_PROSES'          => $row['TGL_PROSES'],
          'KET_PROSES'          => $row['KET_PROSES'],
          'TGL_PENDING'          => $row['TGL_PENDING'],
          'KET_PENDING'          => $row['KET_PENDING'],
          'TGL_SELESAI'          => $row['TGL_SELESAI'],
          'KET_SELESAI'          => $row['KET_SELESAI'],
          'TGL_CANCEL'          => $row['TGL_CANCEL'],
          'KET_CANCEL'          => $row['KET_CANCEL'],
          'TGL_PUTUS'          => $row['TGL_PUTUS'],
          'KET_PUTUS'          => $row['KET_PUTUS'],

        ];
      }
      $msg=[
        'sukses'=>$this->load->view('Input/modal_proses_pks', $data, true)
      ];
      echo json_encode($msg);
    }
  }

public function UpdateProses()
  {
    $post = $this->input->post();
    $this->db->trans_begin();
    $id = $this->input->post('ID_PROSES');
    $tgledit = date('Y-m-d H:i:s');

    if($this->input->post('TGL_PUTUS')!=""){
      $status = 5;
    } elseif ($this->input->post('TGL_CANCEL')!=""){
      $status = 4;
    } elseif ($this->input->post('TGL_SELESAI')!=""){
      $status = 3;
    } elseif ($this->input->post('TGL_PENDING')!="") {
      $status = 2;
    }
                
        $data_insert = array (
        'TGL_PENDING'         => $this->input->post('TGL_PENDING') !="" ? $this->input->post('TGL_PENDING') : NULL, 
        'KET_PENDING'         => $this->input->post('KET_PENDING') !="" ? $this->input->post('KET_PENDING') : NULL,
        'TGL_SELESAI'         => $this->input->post('TGL_SELESAI') !="" ? $this->input->post('TGL_SELESAI') : NULL,
        'KET_SELESAI'         => $this->input->post('KET_SELESAI') !="" ? $this->input->post('KET_SELESAI') : NULL,
        'TGL_CANCEL'          => $this->input->post('TGL_CANCEL')  !="" ? $this->input->post('TGL_CANCEL') : NULL,
        'KET_CANCEL'          => $this->input->post('KET_CANCEL') !="" ? $this->input->post('KET_CANCEL') : NULL,
        'TGL_PUTUS'           => $this->input->post('TGL_PUTUS')  !="" ? $this->input->post('TGL_PUTUS') : NULL,
        'KET_PUTUS'           => $this->input->post('KET_PUTUS') !="" ? $this->input->post('KET_PUTUS') : NULL,
        'STATUS'  => $status,
      );
        // echo "<pre>";print_r($data_insert);  

    $this->db->where('db_pks.tb_proses_pks.ID_PROSES', $id);
    $this->db->update('db_pks.tb_proses_pks', $data_insert);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }
      echo json_encode($result);
  }

public function tampilPerusahaan()
  {
    $this->load->view('ModelInput/getKategori');
  }

  function getdatakategori()
  {
    $id_perusahaan = $this->input->post('perusahaan');

    $getdatakat = $this->ModelInput->getdatakat($id_perusahaan);

    echo json_encode($getdatakat);
    // var_dump($getdatakat);
  } 

public function modalUploadpks()
  {
    if($this->input->is_ajax_request()==true){
      $id = $this->input->post('id');
      $ambildata = $this->ModelInput->editlistInput($id);
      if($ambildata->num_rows()>0){
        $row=$ambildata->row_array();
        $data=[
          
          // 'listkat'           => $this->ModelPerusahaan->tampilKategori(),
          'ID_INPUT_PKS'        => $row['ID_INPUT_PKS'],
          // 'ID_PROSES'           => $row['ID_PROSES'],
          'ID_KATEGORI'         => $row['ID_KATEGORI'],
          'ID_PERUSAHAAN'       => $row['ID_PERUSAHAAN'],
          'NO_PKS'              => $row['NO_PKS'],
          'PERUSAHAAN'          => $row['PERUSAHAAN'],
          'KATEGORI'            => $row['KATEGORI'],

        ];
      }
      $msg=[
        'sukses'=>$this->load->view('Input/modal_upload_pks', $data, true)
      ];
      echo json_encode($msg);
    }
  }

public function UploadInput()
{
    $post = $this->input->post();
    $this->db->trans_begin();
    $id = $this->input->post('ID_INPUT_PKS');
    $tgledit = date('Y-m-d H:i:s');
    // $data_insert = array(
    //     'NO_PKS'        => $this->input->post('NO_PKS_EDIT'), 
    //     'TGL_BERLAKU'   => $this->input->post('TGL_BERLAKU_EDIT'),
    //     'TGL_BERAKHIR'  => $this->input->post('TGL_BERAKHIR_EDIT'),
    //     'KETERANGAN'    => $this->input->post('KETERANGAN_EDIT'),
    //     'KODE_PRODUKSI' => $this->input->post('KODE_PRODUKSI_EDIT'),
    //     'DUE_DATE'      => $this->input->post('DUE_DATE_EDIT'),
    //     'UPDATED_BY'    => $this->session->userdata('id'),
    //     'UPDATED_DATE'  => $tgledit,
    // );

    // Cek apakah ada file yang diunggah
    if(isset($_FILES['FILE_PKS']) && $_FILES['FILE_PKS']['name'] != "") {
        $config['upload_path'] = FCPATH .'/berkas/';
        $config['allowed_types'] = 'pdf';
        $config['max_size']     = '3000';
        $config['file_name'] = date('YmdHis').$this->input->post('ID_PERUSAHAAN').$this->input->post('ID_KATEGORI').'.pdf';

        $this->upload->initialize($config);
        
        if($this->upload->do_upload('FILE_PKS')) {
            // Jika file berhasil diunggah, tambahkan nama file baru ke dalam data yang akan dimasukkan
            $berkas = $this->upload->data();
            $data_insert['FILE'] = $berkas['file_name'];
        } else {
            // Jika gagal unggah file, atur pesan kesalahan dan kembalikan respon
            $error = array('error' => $this->upload->display_errors());
            echo json_encode(array('status' => 'failed', 'error' => $error));
            $this->db->trans_rollback();
            return;
        }
    }

    // Lakukan pembaruan di database
    $this->db->where('ID_INPUT_PKS', $id);
    $this->db->update('db_pks.tb_input_pks', $data_insert);

    // Periksa status transaksi dan kirimkan respons sesuai dengan itu
    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
    } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
    }

    echo json_encode($result);
}

function getnoPksXXX()
    {
      $result = $this->ModelInput->listnoPks()->result_array();
      $data = array();
      foreach ($result as $row) {
          $sub_array = array();
          $sub_array['id'] = $row['ID_INPUT_PKS'];
          $sub_array['text'] = $row['NO_PKS'];
          $data[] = $sub_array;
      }
      // $output = array(
      //     "item" -> $data
      // );
      echo json_encode($data);
    }



















  public function modalTambahPksAdendum()
  {
    $this->load->view('Input/modal_input_adendum');
  }


  public function modalTambahPerusahaan()
  {
    $this->load->view('Perusahaan/modal_tambah_perusahaan');
  }

  public function getKategori()
  {
    $result = $this->ModelPerusahaan->listKategori()->result_array();
    $data = array();
    foreach ($result as $row) 
    {
      $sub_array = array();
      $sub_array['id'] = $row['ID_KATEGORI'];
      $sub_array['text'] = $row['KATEGORI'];
      $data[] = $sub_array;
    }

    echo json_encode($data);
  }
   



    



    

}
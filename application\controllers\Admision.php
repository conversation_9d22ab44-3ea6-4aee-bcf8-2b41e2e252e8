<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Admision extends CI_Controller {
    private $nomorAdmin;
    private $stat_admision;

  public function __construct()
  {
    parent::__construct();
    if($this->session->userdata('logged_in') == FALSE ){
      redirect('login');  
    }
    date_default_timezone_set("Asia/Bangkok");
    setlocale(LC_TIME, 'id_ID.UTF-8'); 
    $this->load->model(array('ModelAdmision'));
    $this->load->library ('whatsapp');
    $this->nomorAdmin = $this->ModelAdmision->getAdminPhone(); //ambil nomer admin ADMISION
    $this->stat_admision = $this->session->userdata('stat_admision'); 
    
  }
  private function formatIndonesianTgl($date) {
    if (!$date) return '';    
    $timestamp = strtotime($date);
    $months = array(
        1 => 'Januari', 'Febru<PERSON>', '<PERSON><PERSON>', 'April', 'Mei', 'Juni',
        'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
    );
    
    $day = date('d', $timestamp);
    $month = $months[date('n', $timestamp)];
    $year = date('Y', $timestamp);    
    return $day . ', ' . $month . ' ' . $year;
  }
  public function index(){
    $data=array(      
      'isi'            => 'Admision/reservasi/Index'
    );
    $this->load->view('Layout/Wrapper',$data);
  }

  public function indexReservasi(){
    $data = [
        'kamarKosong' => $this->ModelAdmision->getKamarKosong(),
        'lastUpdate' => date('Y-m-d H:i:s')
    ];
    $this->load->view('Admision/reservasi/IndexReservasi', $data);
  }
  //bagian kamarkosong
  public function listKamarKosong() {
    $rooms = $this->ModelAdmision->getKamarKosongList();
    $data = ['rooms' => $rooms];
    
    $msg = [
        'sukses' => $this->load->view('Admision/reservasi/IndexKamarKosong', $data, true)
    ];
    
    echo json_encode($msg);
  }
  //
//   public function checkKamarKosong() {
//     $currentKamar = $this->ModelAdmision->getKamarKosong();
//     $newEmptyRoom = $this->ModelAdmision->getNewEmptyRoom();
//     $lastUpdate = $this->input->post('lastUpdate');
    
//     $response = [
//         'kamarKosong' => $currentKamar,
//         'lastUpdate' => date('Y-m-d H:i:s'),
//         'hasChanges' => false,
//         'newEmptyRoom' => null
//     ];

//     if ($lastUpdate && $newEmptyRoom) {
//         $response['hasChanges'] = true;
//         $response['newEmptyRoom'] = $newEmptyRoom;
//     }

//     $this->session->set_userdata('previous_kamar_kosong', $currentKamar);
//     echo json_encode($response);
//  }

public function checkKamarKosong() {
    $this->ModelAdmision->syncLogKamarKosong();
    $currentKamar = $this->ModelAdmision->getKamarKosong();
    $lastUpdate = $this->input->post('lastUpdate');
    $newEmptyRooms = $this->ModelAdmision->getNewEmptyRoom($lastUpdate);

    // Get notified rooms from session
    $notifiedRooms = $this->session->userdata('notified_rooms') ?? [];

    $response = [
        'kamarKosong' => $currentKamar,
        'lastUpdate' => date('Y-m-d H:i:s'),
        'hasChanges' => false,
        'newEmptyRoom' => []
    ];

    if ($lastUpdate && !empty($newEmptyRooms)) {
        foreach ($newEmptyRooms as $room) {
            // Only add rooms that haven't been notified
            if (!in_array($room['key'], $notifiedRooms)) {
                $response['hasChanges'] = true;
                $response['newEmptyRoom'][] = $room['detail'];
                $notifiedRooms[] = $room['key'];
            }
        }
    }

    $this->session->set_userdata('notified_rooms', $notifiedRooms);

    echo json_encode($response);
}


//   public function checkKamarKosong() {
//     $currentKamar = $this->ModelAdmision->getKamarKosong();
//     $kamarDetail = $this->ModelAdmision->getKamarKosongList();
//     $lastUpdate = $this->input->post('lastUpdate');
    
//     $roomDetails = [];
//     foreach ($kamarDetail as $kamar) {
//         $roomDetails[] = $kamar['NAMA_RUANGAN'] . '/' . $kamar['KAMAR'] . '/' . $kamar['TEMPAT_TIDUR'];
//     }
    
//     $response = [
//         'kamarKosong' => $currentKamar,
//         'lastUpdate' => date('Y-m-d H:i:s'),
//         'hasChanges' => false,
//         'roomDetails' => $roomDetails
//     ];

//     if ($lastUpdate) {
//         $previousKamar = $this->session->userdata('previous_kamar_kosong');
//         if ($previousKamar !== null && $currentKamar > $previousKamar) {
//             $response['hasChanges'] = true;
//         }
//     }

//     $this->session->set_userdata('previous_kamar_kosong', $currentKamar);
//     echo json_encode($response);
//   }
  public function getListRuanganPerKelas() {
    $kelas_list = $this->ModelAdmision->getKelasWithRooms();
    $data = [];

    foreach ($kelas_list as $kelas) {
        $rooms = $this->ModelAdmision->getRoomsByKelas($kelas['ID']);
        $data[] = [
            'kelas' => $kelas['DESKRIPSI'],
            'rooms' => $rooms
        ];
    }

    $msg = [
        'sukses' => $this->load->view('Admision/reservasi/IndexListRuangan', ['data' => $data], true)
    ];

    echo json_encode($msg);
}

  public function listKelas()
  {
      $result = $this->ModelAdmision->listKelas();
      $data = array();
      foreach ($result as $row) {
          $sub_array = array();
          $sub_array['id'] = $row['ID'];
          $sub_array['text'] = $row['DESKRIPSI'];
          $data[] = $sub_array;
      }
      echo json_encode($data);
  }
  public function tambahReservasi(){
    $tambah = $this->load->view('Admision/reservasi/IndexReservasiTambah','',true);
    $msg = [
        'data' => $tambah
    ];
    echo json_encode($msg);      
  }
  public function checkReservasi()
  {
      $nomr = $this->input->post('nomr');
      $this->load->model('ModelAdmision');
      $reservasi = $this->ModelAdmision->cekReservasiAktif($nomr);
      if ($reservasi) {
          $response = [
              'hasReservasi' => true,
              'tanggalReservasi' => date('d F Y', strtotime($reservasi['TGL_RENCANA']))
          ];
      } else {
          $response = [
              'hasReservasi' => false
          ];
      }
      echo json_encode($response);
  }

  public function autocomplateMR(){
    $nomr= $this->input->post('nomr');    
    $ceknomr = $this->ModelAdmision->cekNomr($nomr);
    
    if ($ceknomr->num_rows() > 0) {
        $result = $ceknomr->row_array();
        $row = array(
            'nama' => $result['pasien'],
            'jk' => $result['jk'],
            'tanggal_lahir' =>  date('d-m-Y', strtotime($result['tglLahir'])),
            'no_telp' => $result['hp']
        );
    } else {
        $row = array(
            'nama' => 'Data tidak ditemukan'
        );
    }
    echo json_encode($row);
    
  }
  public function listDokter(){
    $result = $this->ModelAdmision->getdokter();
    $data = array();
    foreach ($result as $row) {
        $sub_array = array();
        $sub_array['id'] = $row['ID']; 
        $sub_array['text'] = $row['DOKTER'];
        $data[] = $sub_array;
    }
    echo json_encode($data);
  }
  public function cara_bayar()
  {
      $penjamin = $this->ModelAdmision->cara_bayar();
  
      $data = [];
      foreach ($penjamin as $row) {
          $data[] = [
              'id' => $row['ID'],
              'value' => $row['DESKRIPSI'], 
          ];
      }
      echo json_encode($data); 
  }
  public function listTujuan()
  {
      $result = $this->ModelAdmision->listTujuan();
      $data = array();
      foreach ($result as $row) {
          $sub_array = array();
          $sub_array['id'] = $row['ID'];
          $sub_array['text'] = $row['DESKRIPSI'];
          $data[] = $sub_array;
      }
      echo json_encode($data);
  }
  public function simpanReservasi()
  {
    $this->db->trans_begin();
    $data = $this->input->post();
    // $stat_admision = $this->session->userdata('stat_admision');
    switch ($this->stat_admision) {
        case 2:
            $gedung = 2;
            break;
        case 3:
        case 1: 
            $gedung = 1;
            break;
        default:
            $gedung = null;
            break;
    }

    $reservasi_data = [
        'NORM' => $data['nomr'],
        'KODE_BOOKING' => $data['kode_booking'],
        'TGL_RENCANA' => $data['tgl_rencana_masuk'],
        'TGL_PCR' => $data['tgl_pcr'],
        'DIAGNOSA' => $data['diagnosa'],
        'KETERANGAN' => $data['keterangan'],
        'ID_BAYAR' => $data['idCara_bayar'],
        'ID_KELAS' => $data['kelasPasien'],
        'ID_DOKTER' => $data['dokter'],
        'ID_RAWAT' => $data['tujuan_dirawat'],
        'OLEH' => $this->session->userdata('id_simpel'),
        'TGL_INPUT' => date('Y-m-d H:i:s'),
        'GEDUNG' => $gedung,
    ];
    $this->db->insert('db_rekammedis.tb_reservasi', $reservasi_data);
    if ($this->db->trans_status() === FALSE) {
        $this->db->trans_rollback();
        $response = ['status' => 'error', 'message' => 'Database transaction failed.'];
    } else {
        $this->db->trans_commit();
        $nomorTujuan = $data['no_telp'];
        if (substr($nomorTujuan, 0, 1) == '0') {
            $nomorTujuan = '+62' . substr($nomorTujuan, 1);
        } elseif (substr($nomorTujuan, 0, 1) != '+') {
            $nomorTujuan = '+62' . $nomorTujuan;
        }
        $tglRencana = $this->formatIndonesianTgl($data['tgl_rencana_masuk']);

        $salam = (date("H") >= '05' && date("H") < "10" ? "Pagi" : (date("H") >= '10' && date("H") < "15" ? "Siang" : (date("H") >= '15' && date("H") < "19" ? "Sore" : "Malam")))." "."Bapak/Ibu " . $data['nama'] . ",";
        $pembuka = ": ". $data['kode_booking'] ;
        $mr = $data['nomr'] ;
        $nama = $data['nama'];
        $dpjp = $data['nama_dokter'];
        $tujuan = $data['nama_tujuan'];
        $kelas = $data['nama_kelasPasien'];
        // $tgl =  $data['tgl_rencana_masuk'];
        $tgl = $tglRencana;
        $penutup = " : ".$this->nomorAdmin;       
        
        $messageParams = [
            "1" => $salam,  
            "2" => $pembuka, 
            "3" => $mr, 
            "4" => $nama,
            "5" => $dpjp,
            "6" => $tujuan,
            "7" => $kelas,
            "8" => $tgl,
            "9" => $penutup
        ];

        try {
            $whatsappResponse = $this->whatsapp->kirim($nomorTujuan, $messageParams);
            $whatsappResult = json_decode($whatsappResponse, true);

            if (isset($whatsappResult['success']) && $whatsappResult['success'] === true) {
                $response = [
                    'status' => 'success',
                    'message' => 'Reservasi diterima dan pesan terkirim.',
                    'api_response' => $whatsappResult
                ];
            } else {
                $response = [
                    'status' => 'error',
                    'message' => 'API returned: ' . json_encode($whatsappResult),
                    'api_response' => $whatsappResult
                ];
            }
        } catch (Exception $e) {
            $response = [
                'status' => 'error',
                'message' => 'Gagal mengirim pesan: ' . $e->getMessage(),
                'api_response' => null
            ];
        }
    }
    $this->output
        ->set_content_type('application/json')
        ->set_output(json_encode($response));
  }

  //tbltunggunew
  public function getDataReservasi() {
    $kelas = $this->input->post('kelas'); 
    $idAwal = $this->input->post('idAwal');
    $idAkhir = $this->input->post('idAkhir');
    $dokter = $this->input->post('dokter');

    $listdata = $this->ModelAdmision->getDataReservasi($kelas, $idAwal, $idAkhir, $dokter);
    $data = array();
    $no = 1;

    foreach ($listdata->result() as $field) {
        $TGL_RENCANA = date('d F Y', strtotime($field->TGL_RENCANA));
        $TANGGAL_LAHIR = (!$field->TANGGAL_LAHIR || $field->TANGGAL_LAHIR == '0000-00-00') ? 
                        null : date('d F Y', strtotime($field->TANGGAL_LAHIR));
        $TGL_PCR = (!$field->TGL_PCR || $field->TGL_PCR == '0000-00-00') ? 
                   null : date('d F Y', strtotime($field->TGL_PCR));

        // Detail button
        $det = '<a href="#viewDet" data-toggle="modal"
                data-kode_booking="'.$field->KODE_BOOKING.'"
                data-tgl_lahir="'.$TANGGAL_LAHIR.'"
                data-norm="'.$field->NORM.'"
                data-diagnosa="'.$field->DIAGNOSA.'"
                data-rawat="'.$field->RAWAT.'"
                data-tgl_pcr="'.$TGL_PCR.'"
                data-backdrop="static" data-keyboard="false">
                <i class="fa fa-info-circle"></i>
                </a>';

        // Action buttons
        $actions = '<button type="button" class="btn btn-primary btn-sm btn-spacing" title="Edit Reservasi" 
                    onclick="edit('.$field->ID.')" style="margin-bottom: 5px;">
                    <i class="fas fa-pencil-alt fa-fw"></i>
                </button>
                <button type="button" class="btn btn-success btn-sm btn-spacing" title="Terima Reservasi" 
                    style="margin-bottom: 5px;" onclick="terima('.$field->ID.')">
                    <i class="fas fa-check fa-fw"></i>
                </button>
                <button type="button" class="btn btn-danger btn-sm" title="Batalkan Reservasi" 
                    style="margin-bottom: 5px;" onclick="tolak('.$field->ID.')">
                    <i class="fas fa-times fa-fw"></i>
                </button>';

        // Check if date is past
        $tgl_rencana = strtotime($field->TGL_RENCANA);
        $today = strtotime('today');
        
        if ($tgl_rencana < $today) {
            $TGL_RENCANA = '<span style="color: red;">' . $TGL_RENCANA . '</span>';
        } elseif ($tgl_rencana == $today) {
            $TGL_RENCANA = '<span style="color: orange;">' . $TGL_RENCANA . '</span>';
        }

        // SMS badge
        $smsBadge = !empty($field->SMS) 
            ? '<span class="badge badge-danger badge-pill float-right sms-history-badge" style="cursor:pointer" data-id="'.$field->ID.'">
                <i class="fas fa-envelope"></i> ' . htmlspecialchars($field->SMS) . '
              </span>'
            : '<span class="badge badge-secondary badge-pill float-right"> <i class="fas fa-envelope"></i> 0</span>';

        $data[] = array(
            $no,
            $field->PASIEN.'  ['. $field->NORM .']'. ' ' . $det,
            $field->JK,
            $field->TGL_INPUT,
            $TGL_RENCANA . ' ' . $smsBadge,
            $field->BAYAR,
            $field->KELAS,
            $field->DOKTER,
            $field->RAWAT,
            $field->KETERANGAN,
            $actions,
            $field->STATUS,//11
            $field->PASIEN,
            $field->NOHP,//13
            $field->TGL_RENCANA,
            $field->SMS,
            $field->ID,
            $field->KODE_BOOKING,
            $field->WAKTU
        );
        
        $no++;
    }

    $output = array("data" => $data);
    echo json_encode($output);
  }
  //endtbltunggu

  //tblTerimaNew
  public function getDataReservasiTerima() {
    $kelas = $this->input->post('kelas'); 
    $idAwal = $this->input->post('idAwal');
    $idAkhir = $this->input->post('idAkhir');
    $status = $this->input->post('statusReservasi');

    $listdata = $this->ModelAdmision->getTblReservasiTerima($kelas, $idAwal, $idAkhir, $status);
    $data = array();
    $no = 1;

    foreach ($listdata->result() as $field) {
        $TGL_RENCANA = date('d F Y', strtotime($field->TGL_RENCANA));
        $tglAcc = strtotime($field->TGL_ACC);
        $tglRencana = strtotime($field->TGL_RENCANA);
        $cutoffDate = strtotime('2025-04-30');
        
        if (empty($field->TGL_ACC)) {
            $TGL_ACC = '-';
        } else {
            $tglAcc = strtotime($field->TGL_ACC);
            $tglRencana = strtotime($field->TGL_RENCANA);
            $cutoffDate = strtotime('2025-04-30');
            
            if ($tglAcc <= $cutoffDate) {
                if ($tglAcc < $tglRencana) {
                    $tglAcc = strtotime('+1 day', $tglAcc);
                }
                $TGL_ACC = date('d F Y', $tglAcc);
            } else {
                $TGL_ACC = date('d F Y', strtotime($field->TGL_ACC));
            }
        }
        $TGL_INPUT = (!empty($field->TGL_INPUT)) ? date('d F Y', strtotime($field->TGL_INPUT)) : null;
        $TGL_PCR = (!$field->TGL_PCR || $field->TGL_PCR == '0000-00-00') ? null : date('d F Y', strtotime($field->TGL_PCR));
        $TANGGAL_LAHIR = (!$field->TANGGAL_LAHIR || $field->TANGGAL_LAHIR == '0000-00-00') ? null : date('d F Y', strtotime($field->TANGGAL_LAHIR));

        $det = '<a href="#viewDet2" data-toggle="modal"
            data-kode_booking="'.$field->RE_SIMPEL.'"
            data-tgl_lahir="'.$TANGGAL_LAHIR.'"
            data-norm="'.$field->NORM.'"
            data-diagnosa="'.$field->DIAGNOSA.'"
            data-rawat="'.$field->RAWAT.'"
            data-tgl_pcr="'.$TGL_PCR.'"
            data-backdrop="static" data-keyboard="false">
            <i class="fa fa-info-circle"></i>
            </a>';

        $actions = '';
        if ($status == 2) {
            $edit = '<button type="button" class="btn btn-warning btn-sm btn-spacing" title="Edit Reservasi" 
                onclick="edit2('.$field->ID.')" style="margin-bottom: 5px;">
                <i class="fas fa-pencil-alt fa-fw"></i>
                </button>';
            $tolak = '<button type="button" class="btn btn-danger btn-sm" title="Batalkan Reservasi" 
                onclick="batal2('.$field->ID.')" style="margin-bottom: 5px;">
                <i class="fas fa-times fa-fw"></i>
                </button>';
            $actions = $edit . ' ' . $tolak;
        }

        // untuk warna tanggal
        // $tgl_rencana = strtotime($field->TGL_RENCANA);
        // $today = strtotime('today');
        
        // if ($tgl_rencana < $today) {
        //     $TGL_RENCANA = '<span style="color: red;">' . $TGL_RENCANA . '</span>';
        // } elseif ($tgl_rencana == $today) {
        //     $TGL_RENCANA = '<span style="color: orange;">' . $TGL_RENCANA . '</span>';
        // }

        $smsBadge = !empty($field->SMS) 
            ? '<span class="badge badge-danger badge-pill float-right sms-history-badge2" style="cursor:pointer" data-id="'.$field->ID.'">
                <i class="fas fa-envelope"></i> ' . htmlspecialchars($field->SMS) . '
              </span>'
            : '<span class="badge badge-secondary badge-pill float-right"> <i class="fas fa-envelope"></i> 0</span>';

        $data[] = array(
            $no,
            $field->PASIEN.'  ['. $field->NORM .']'. ' ' . $det,
            $field->JK,
            $field->RE_SIMPEL . (!empty($field->nama_kamar) ? ' [' . $field->nama_kamar.']' : ' - '),
            $TGL_RENCANA . ' ' . $smsBadge,
            $TGL_ACC,
            $field->BAYAR,
            $field->KELAS,
            $field->DOKTER,
            $field->RAWAT,
            $field->KETERANGAN,
            $actions,
            $field->STATUS,
        $field->PASIEN,
        $field->NOHP,//14
        $field->TGL_RENCANA,
        $field->SMS,
        $field->ID,
        $field->WAKTU
        );
        $no++;
    }

    $output = array("data" => $data);
    echo json_encode($output);
}
  //end terima
  public function tblReservasi(){
    $kelas = $this->input->post('kelas'); 
    $idAwal = $this->input->post('idAwal');
    $idAkhir = $this->input->post('idAkhir');
    $status = $this->input->post('statusReservasi');

    $listdata = $this->ModelAdmision->getTblReservasi($kelas, $idAwal, $idAkhir,$status);
    $data=array();
    $no =1;
    foreach ($listdata->result() as $field) {  
      $TGL_RENCANA = date('d F Y', strtotime($field->TGL_RENCANA));
      $TGL_INPUT = date('d F Y', strtotime($field->TGL_INPUT));
      $TGL_PCR = (!$field->TGL_PCR || $field->TGL_PCR == '0000-00-00') ? null : date('d F Y', strtotime($field->TGL_PCR));
      $TANGGAL_LAHIR = (!$field->TANGGAL_LAHIR || $field->TANGGAL_LAHIR == '0000-00-00') ? null : date('d F Y', strtotime($field->TANGGAL_LAHIR));
    //   $TANGGAL_LAHIR = date('d F Y', strtotime($field->TANGGAL_LAHIR));
      $det = '';
      $edit = '';
      $acc = '';
      $tolak = '';

        switch ($status) {
            case 1: 
                $det = '<a href="#viewDet" data-toggle="modal"
                data-kode_booking="'.$field->KODE_BOOKING.'"
                data-tgl_lahir="'.$TANGGAL_LAHIR.'"
                data-norm="'.$field->NORM.'"
                data-diagnosa="'.$field->DIAGNOSA.'"
                data-rawat="'.$field->RAWAT.'"
                data-tgl_pcr="'.$TGL_PCR.'"
                data-backdrop="static" data-keyboard="false">
                <i class="fa fa-info-circle"></i>
                </a>';
                $edit = '<button type="button" class="btn btn-primary btn-sm btn-spacing" title="Edit Reservasi" 
                            onclick="edit('.$field->ID.')" style="margin-bottom: 5px;">
                            <i class="fas fa-pencil-alt fa-fw"></i>
                        </button>';
                $acc = '<button type="button" class="btn btn-success btn-sm btn-spacing" title="Terima Reservasi" 
                            style="margin-bottom: 5px;" onclick="terima('.$field->ID.', \''.$field->NORM.'\', '.$field->ID_KELAS.', \''.$field->PASIEN.'\', \''.$field->NOHP.'\', \''.$field->TGL_RENCANA.'\', \''.$field->JENIS_KELAMIN.'\')">
                            <i class="fas fa-check fa-fw"></i>
                        </button>';
                $tolak = '<button type="button" class="btn btn-danger btn-sm" title="Batalkan Reservasi" 
                            style="margin-bottom: 5px;" onclick="tolak('.$field->ID.', \''.$field->NORM.'\', \''.$field->PASIEN.'\', \''.$field->NOHP.'\', \''.$field->TGL_RENCANA.'\')">
                            <i class="fas fa-times fa-fw"></i>
                        </button>';
                break;
            case 2:
                $det = '<a href="#viewDet2" data-toggle="modal"
                data-kode_booking="'.$field->RE_SIMPEL.'"
                data-tgl_lahir="'.$TANGGAL_LAHIR.'"
                data-norm="'.$field->NORM.'"
                data-diagnosa="'.$field->DIAGNOSA.'"
                data-rawat="'.$field->RAWAT.'"
                data-tgl_pcr="'.$TGL_PCR.'"
                data-backdrop="static" data-keyboard="false">
                <i class="fa fa-info-circle"></i>
                </a>';
              $tolak = '<button type="button" class="btn btn-danger btn-sm" title="Batalkan Reservasi" 
                    onclick="batal2('.$field->ID.', \''.$field->NORM.'\', \''.$field->PASIEN.'\', \''.$field->NOHP.'\', \''.$field->TGL_RENCANA.'\', \''.$field->RE_SIMPEL.'\')">
                    <i class="fas fa-times fa-1x"></i>
                    </button>';
                break;
            case 3:
                    $det = '<a href="#viewDet2" data-toggle="modal"
                    data-kode_booking="'.$field->RE_SIMPEL.'"
                    data-tgl_lahir="'.$TANGGAL_LAHIR.'"
                    data-norm="'.$field->NORM.'"
                    data-diagnosa="'.$field->DIAGNOSA.'"
                    data-rawat="'.$field->RAWAT.'"
                    data-tgl_pcr="'.$TGL_PCR.'"
                    data-backdrop="static" data-keyboard="false">
                    <i class="fa fa-info-circle"></i>
                    </a>';
            default:
                break;
        }
      
        // $tgl_rencana = strtotime($field->TGL_RENCANA);
        // $today = strtotime('today');
        
        // if ($tgl_rencana < $today) {
        //     $TGL_RENCANA = '<span style="color: red;">' . $TGL_RENCANA . '</span>';
        // } elseif ($tgl_rencana == $today) {
        //     $TGL_RENCANA = '<span style="color: orange;">' . $TGL_RENCANA . '</span>';
        // }
      

      $data[] = array(
        $no,
        $field->PASIEN.'  ['. $field->NORM .']'. ' ' . $det,
        $field->STATUS == 0 ? $field->TGL_RENCANA : $field->JK,
        $field->STATUS == 0 ? $field->BAYAR : 
        ($field->STATUS == 1 ? $field->BAYAR :
        ($field->STATUS == 2 ? $field->RE_SIMPEL . (!empty($field->nama_kamar) ? ' [' . $field->nama_kamar.']' : ' - ') : $field->nama_kamar)),     
        $field->STATUS == 0 ? $field->KELAS : 
        ($field->STATUS == 1 ? $TGL_RENCANA . ' ' . (!empty($field->SMS) 
            ? '<span class="badge badge-danger badge-pill float-right sms-history-badge" style="cursor:pointer" data-id="'.$field->ID.'">
                <i class="fas fa-envelope"></i> ' . htmlspecialchars($field->SMS) . '
              </span>'
            : '<span class="badge badge-secondary badge-pill float-right"> <i class="fas fa-envelope"></i>  0</span>'
        ) : ($field->STATUS == 2 || $field->STATUS == 3 ? $TGL_RENCANA : '')),
        $field->STATUS == 0 ? $field->RAWAT : ($field->STATUS == 1 || $field->STATUS == 2 ? $field->BAYAR : $field->TGL_MASUK),
        $field->STATUS == 0 ? $field->TGL_UPDATE : (in_array($field->STATUS, [1, 2]) ? $field->KELAS : $field->BAYAR),
        $field->STATUS == 0 ? $field->DOKTER : (in_array($field->STATUS, [1, 2]) ? $field->DOKTER : $field->KELAS),
        $field->STATUS == 0 ? $field->PEGAWAI : (in_array($field->STATUS, [1, 2]) ? $field->KETERANGAN : $field->DOKTER),
        $field->STATUS == 0 ? $field->ALASAN_EDIT : (in_array($field->STATUS, [1, 2]) ? $edit.' '.$acc.' '.$tolak : $field->KETERANGAN),        
        // $field->STATUS == 0 ? $field->TGL_UPDATE :$field->KELAS,
        // $field->STATUS == 0 ? $field->DOKTER : $field->DOKTER,
        // $field->STATUS == 0 ? $field->PEGAWAI : $field->KETERANGAN,
        // $field->STATUS == 0 ? $field->ALASAN_EDIT : $edit.' '.$acc.' '.$tolak,
        $field->STATUS,
        $field->PASIEN,
        $field->NOHP,//12
        $field->TGL_RENCANA,
        $field->SMS,
        $field->ID,
        $field->KODE_BOOKING,
        $field->WAKTU,
        $field->RAWAT
        );
        
      $no++;
    }

    $output = array(
      "data"            => $data
    );
    echo json_encode($output);
  }

  public function getSmsHistory() {
    $id = $this->input->post('id');
    $status = $this->input->post('status');
    $history = $this->ModelAdmision->getSmsHistory($id, $status);
    echo json_encode($history);
  }


  public function modalEdit(){
    $id       = $this->input->post('id');
    $statusForm = $this->input->post('statusForm');
    if ($statusForm == 1) {
        $reservasi = $this->ModelAdmision->getEditReservasi2($id);
    } else {
        $reservasi = $this->ModelAdmision->getEditReservasi($id);
    }
    $data = array(
        'id'  => $reservasi['ID'],
        'NORM' => $reservasi['NORM'],
        'PASIEN' => $reservasi['PASIEN'],
        'JK' => $reservasi['JK'],
        'TANGGAL_LAHIR' => $this->formatIndonesianTgl($reservasi['TANGGAL_LAHIR']),
        'NOHP' => $reservasi['NOHP'],
        'KODE_BOOKING' => $reservasi['KODE_BOOKING'],
        'TGL_RENCANA' => $reservasi['TGL_RENCANA'],
        'TGL_INPUT' => $reservasi['TGL_INPUT'],
        'BAYAR' => $reservasi['BAYAR'],
        'ID_BAYAR' => $reservasi['ID_BAYAR'],
        'KELAS' => $reservasi['KELAS'],
        'DIAGNOSA' => $reservasi['DIAGNOSA'],
        'KETERANGAN' => $reservasi['KETERANGAN'],
        'DOKTER' => $reservasi['ID_DOKTER'],
        'TGL_PCR' => $reservasi['TGL_PCR'],
        'ID_RAWAT' => $reservasi['ID_RAWAT'],
        'id_perjanjian' => $reservasi['id_perjanjian'],
        'list_dokter' => $this->ModelAdmision->getdokter(),
        'list_kelas' => $this->ModelAdmision->listKelas(),
        'list_tujuan' => $this->ModelAdmision->listTujuan(),
        'statusForm' => (int)$statusForm

    );
    if ($statusForm == 1) {
        $data['nama_kamar'] = $reservasi['nama_kamar'];
        $data['RE_SIMPEL'] = $reservasi['RE_SIMPEL'];
    }

    $msg = array(
        'sukses' => $this->load->view('Admision/reservasi/IndexReservasiEdit', $data, true)
    );
    echo json_encode($msg); 
  }
  public function modalTerima(){
    $id       = $this->input->post('id');
    $reservasi  = $this->ModelAdmision->getModalEdit($id);
    $data = array(
        'id'  => $this->input->post('id'),
        'NORM' => $reservasi['NORM'],
        'PASIEN' => $reservasi['PASIEN'],
        'KELAS' => $reservasi['ID_KELAS'],
        'NOHP' => $reservasi['NOHP'],
        'jk' => $reservasi['JENIS_KELAMIN'],
        'TGL_RENCANA' => $reservasi['TGL_RENCANA'],
        // 'NORM' => $this->input->post('norm'),
        // 'PASIEN' => $this->input->post('pas'),
        // 'KELAS' => $this->input->post('kelas'),
        // 'NOHP' => $this->input->post('hp'),
        // 'jk' => $this->input->post('jk'),
        // 'TGL_RENCANA' =>  $this->input->post('tgl'),
        'list_kelas' => $this->ModelAdmision->listKelas(),        
        'list_waktu' => $this->ModelAdmision->listWaktu(1)
        // 'list_ruangan' => $this->ModelAdmision->listRuang()
    );

    $msg = array(
        'sukses' => $this->load->view('Admision/reservasi/IndexReservasiTerima', $data, true)
    );
    echo json_encode($msg); 
  }
  public function updateReservasi(){
    $this->db->trans_start();
    $id = $this->input->post('idReservasi');
    $data = $this->input->post();
    $send_wa = $this->input->post('send_wa');
    $statusForm = $this->input->post('statusForm');
   
    
    // $statusForm = $this->input->post('statusForm');

    $reSimpel = $this->input->post('RE_SIMPEL');
    // if ($statusForm == 1) {
    //     $this->db->where('ID', $id);
    //     $this->db->update('db_rekammedis.tb_reservasi', ['STATUS' => 1]);
    // }
    if ($data['tgl_rencana_masuk'] !== $data['tgl_rencana_masuk_old'] && $reSimpel) {
        $this->db->where('NOMOR', $reSimpel);
        $this->db->update('pendaftaran.reservasi', ['status' => 0]);
    }
    if ($data['tgl_rencana_masuk'] !== $data['tgl_rencana_masuk_old'] &&  $statusForm = 1) {
        $this->db->where('ID', $id);
        // $this->db->update('db_rekammedis.tb_reservasi', ['status' => 1]);
        $this->db->update('db_rekammedis.tb_reservasi', ['status' => 1, 'TGL_ACC' => null]);
    }

    //log tanggal rencana masuk
    if ($data['tgl_rencana_masuk'] !== $data['tgl_rencana_masuk_old']) {
        $log_data = array(
            'ID_RESERVASI' => $id,
            'OLEH' => $this->session->userdata('id_simpel'),
            'FIELD_RUBAH' => 'TGL_RENCANA',
            'ISI_LAMA' => $data['tgl_rencana_masuk_old'],
            'ISI_NEW' => $data['tgl_rencana_masuk'],
            'ALASAN_EDIT' => $data['alasan_edit'],
            'TGL_UPDATE' => date('Y-m-d H:i:s'),
            'STATUS' => isset($data['isPasienRequest']) ? 2 : 1
        );
        $this->db->insert('db_rekammedis.tb_reservasi_log', $log_data);
    }

    $data_update = array(
        'TGL_RENCANA' => $data['tgl_rencana_masuk'],
        'TGL_PCR'     => $data['tgl_pcr'],
        'DIAGNOSA'    => $data['diagnosa'],
        'KETERANGAN'  => $data['keterangan'],
        'ID_BAYAR'    => $data['idCara_bayar'],
        'ID_KELAS'    => $data['kelasPasien'],
        'ID_DOKTER'   => $data['dokter'],
        'ID_RAWAT'    => $data['tujuan_dirawat'],
        'OLEH'        => $this->session->userdata('id_simpel'),
        'ALASAN_EDIT' => $data['alasan_edit'],
    );

    $this->db->where('ID', $id);
    $this->db->update('db_rekammedis.tb_reservasi', $data_update);
    
    if (!empty($data['id_perjanjian'])) {
        $data_update_perjanjian = array(
            'tgl_rencanaMasuk' => $data['tgl_rencana_masuk'],
            'tgl_pcr'     => $data['tgl_pcr'],
            'diagnosa'    => $data['diagnosa'],
            'keterangan'  => $data['keterangan'],
            'id_cara_bayar'    => $data['idCara_bayar'],
            'id_kelas'    => $data['kelasPasien'],
            'id_dokter'   => $data['dokter'],
            'id_tujuan_dirawat'    => $data['tujuan_dirawat'],
            'pengguna'        => $this->session->userdata('id_simpel'),
            'alasan_edit' => $data['alasan_edit'],
        );
        $this->db->where('id_perjanjian', $data['id_perjanjian']);
        $this->db->update('db_reservasi.tb_reservasi', $data_update_perjanjian);
    }
    $this->db->trans_complete();   

    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $response = ['status' => 'error', 'message' => 'Database transaction failed.'];
    } else {
        $this->db->trans_commit();
     if ($send_wa == '1') {
        $nomorTujuan = $data['no_telp'];
        $tglRencana = $this->formatIndonesianTgl($data['tgl_rencana']);
        $tglRubah = $this->formatIndonesianTgl($data['tgl_rencana_masuk']);
        if (substr($nomorTujuan, 0, 1) == '0') {
            $nomorTujuan = '+62' . substr($nomorTujuan, 1);
        } elseif (substr($nomorTujuan, 0, 1) != '+') {
            $nomorTujuan = '+62' . $nomorTujuan;
        }
        $dateChanged = $data['tgl_rencana_masuk'] !== $data['tgl_rencana'];
        
        $rubah = $dateChanged ? ". Terdapat Perubahan tanggal reservasi menjadi : *".$tglRubah."*" : " ";

        $buka = (date("H") >= '05' && date("H") < "10" ? "Pagi" : (date("H") >= '10' && date("H") < "15" ? "Siang" : (date("H") >= '15' && date("H") < "19" ? "Sore" : "Malam")))." "."Bapak/Ibu " .$data['nama'];
        $isiPesan = "kami sampaikan informasi terkait reservasi Anda di RSK Dharmais. Mohon maaf, reservasi tanggal ".$tglRencana." perlu *dijadwalkan ulang* karena  ";
        $tutup = $data['alasan_edit'] .$rubah." Jika membutuhkan informasi lebih lanjut, silakan hubungi kami di nomor :" . $this->nomorAdmin ;
        
        $messageParams = [
            "1" => $buka,  
            "2" => $isiPesan, 
            "3" => $tutup,    
        ];

        try {
            $whatsappResponse = $this->whatsapp->send($nomorTujuan, $messageParams);
            $whatsappResult = json_decode($whatsappResponse, true);

            if (isset($whatsappResult['success']) && $whatsappResult['success'] === true) {
                $response = [
                    'status' => 'success',
                    'message' => 'Reservasi telah diperbarui dan pesan telah terkirim.',
                    'api_response' => $whatsappResult
                ];
            } else {
                $response = [
                    'status' => 'error',
                    'message' => 'API returned: ' . json_encode($whatsappResult),
                    'api_response' => $whatsappResult
                ];
            }
        } catch (Exception $e) {
            $response = [
                'status' => 'error',
                'message' => 'Gagal mengirim pesan: ' . $e->getMessage(),
                'api_response' => null
            ];
        }
      } else {
        $response = [
            'status' => 'success',
            'message' => 'Reservasi telah diperbarui.'
        ];
      }
    }

    $this->output
        ->set_content_type('application/json')
        ->set_output(json_encode($response));
    }
//   public function updateReservasi(){
//     $this->db->trans_start();
//     $id = $this->input->post('idReservasi');
// 
//     $data_insert = array(
//       'TGL_RENCANA' => $this->input->post('tgl_rencana_masuk'),
//       'TGL_PCR'     => $this->input->post('tgl_pcr'),
//       'DIAGNOSA'    => $this->input->post('diagnosa'),
//       'KETERANGAN'  => $this->input->post('keterangan'),
//       'ID_BAYAR'    => $this->input->post('idCara_bayar'),
//       'ID_KELAS'    => $this->input->post('kelasPasien'),
//       'ID_DOKTER'   => $this->input->post('dokter'),
//       'ID_RAWAT'    => $this->input->post('tujuan_dirawat'),
//       'OLEH'        =>  $this->session->userdata('id_simpel'),
//       'ALASAN_EDIT' => $this->input->post('alasan_edit'),
      
//     );
//     $this->db->where('ID', $id);
//     $this->db->update('db_rekammedis.tb_reservasi', $data_insert);
// 
//     if ($this->db->trans_status() === false) {
//         $this->db->trans_rollback();
//         $result = array('status' => 'failed');
//       } else {
//         $this->db->trans_commit();
//         $result = array('status' => 'success');
//       }
 
//     echo json_encode($result);
//   }
  public function listRuang() {
    $kelas_id = $this->input->get('kelas_id'); 
    $jenis_kelamin = $this->input->get('jenis_kelamin');
    if ($kelas_id) {
        $ruangan = $this->ModelAdmision->listRuangOptimized($kelas_id, $jenis_kelamin);

        echo json_encode($ruangan);
    } else {
        echo json_encode([]); 
    }
  }
  


  public function terimaReservasi()
  {
    $data = $this->input->post();
    $has_room = !empty($data['ruang']);

    if ($has_room) {
        $no_reservasi = $this->ModelAdmision->generateNoReservasi();
        
        $reservasi_data = [
            'NOMOR' => $no_reservasi,
            'TANGGAL' => $data['tgl_rencana'],
            'RUANG_KAMAR_TIDUR' => $data['ruang'],
            'BERAKHIR' => NULL,
            'ATAS_NAMA' => $data['namaPas'],
            'KONTAK_INFO' => $data['nohp'],        
            'OLEH' => $this->session->userdata('id_simpel'),
            'STATUS' => 1,
        ];
        $this->db->insert('pendaftaran.reservasi', $reservasi_data);
    }

    $update_reservasi = [    
        'RE_SIMPEL' => $has_room ? $no_reservasi : NULL,
        'ID_ACC' => $this->session->userdata('id_simpel'),        
        // 'TGL_ACC' => date('Y-m-d H:i:s'),
        'TGL_ACC' => $data['tgl_rencana'] . ' ' . date('H:i:s'),
        'WAKTU' => $data['waktu'],
        'RUANG_KAMAR_TIDUR' => $has_room ? $data['ruang'] : NULL,
        'STATUS' => 2,
    ];
    $this->db->where('ID', $data['idReservasi']);
    $this->db->update('db_rekammedis.tb_reservasi', $update_reservasi);

    if ($this->db->trans_status() === FALSE) {
        $response = ['status' => 'error', 'message' => 'Database transaction failed.'];
    } else {
        $nomorTujuan = $data['nohp'];
        if (substr($nomorTujuan, 0, 1) == '0') {
            $nomorTujuan = '+62' . substr($nomorTujuan, 1);
        } elseif (substr($nomorTujuan, 0, 1) != '+') {
            $nomorTujuan = '+62' . $nomorTujuan;
        }
        $tglRencana = $this->formatIndonesianTgl($data['tgl_rencana']);

        $buka = (date("H") >= '05' && date("H") < "10" ? "Pagi" : (date("H") >= '10' && date("H") < "15" ? "Siang" : (date("H") >= '15' && date("H") < "19" ? "Sore" : "Malam")))." "."Bapak/Ibu " . $data['namaPas'] . ",";
        $isiPesan =  $tglRencana;
        $detail = $data['waktu'];
        $penutup = " : " . $this->nomorAdmin;
        $kodekamar = $has_room ? $no_reservasi : "-";
        //tower C
        $reservasiDetail = $this->ModelAdmision->reservasiDetailTowerC($data['idReservasi']);
        // echo '<pre>';
        // print_r($reservasiDetail);
        // echo '</pre>';
        // exit;
        // $tglRencanaC = $this->formatIndonesianTgl($reservasiDetail['TANGGAL']);
        $tindakanC = isset($reservasiDetail['tindakan']) ? $reservasiDetail['tindakan'] : '-';
        $kelasC = isset($reservasiDetail['kelas']) ? $reservasiDetail['kelas'] : '-';
        $dokterC = isset($reservasiDetail['DOKTER']) ? $reservasiDetail['DOKTER'] : '-';
        $bukaC = (date("H") >= '05' && date("H") < "10" ? "Pagi" : (date("H") >= '10' && date("H") < "15" ? "Siang" : (date("H") >= '15' && date("H") < "19" ? "Sore" : "Malam")));
        $pasienC = "Yth. Bapak/Ibu " . $data['namaPas'] . ",";
        $gedungC = "Gedung C";
        $waktuC =  $this->formatIndonesianTgl($data['tgl_rencana']);
        $statusC = "SUDAH TERSEDIA."."Jika tidak ada perubahan jadwal untuk rencana masuk rawat inapnya, mohon untuk Datang ke Pendaftaran Rawat Inap Tower Cendana pukul". $data['waktu'] . "ke bagian pendaftaran LOBBY UTAMA GEDUNG C dan mengambil antrian di kioak Pendaftaran";
        $penutupC = $this->nomorAdmin;


        switch ($this->stat_admision) {
            case 3:
            case 1:
                $messageParams = [
                    "1" => $buka,
                    "2" => $isiPesan,
                    "3" => $detail,
                    "4" => $kodekamar,
                    "5" => $penutup
                ];
                $whatsappMethod = 'kirim2';
                break;
        
            case 2:
                $messageParams = [
                   "1" => $bukaC,
                   "2" => $pasienC,
                   "3" => $gedungC,
                   "4" => $waktuC,
                   "5" => $tindakanC,
                   "6" => $kelasC,
                   "7" => $dokterC,
                   "8" => $statusC,
                   "9" => $penutupC
                ];
                $whatsappMethod = 'kirim_terima_towerc';
                break;
        
            default:
                // Tidak melakukan apa-apa
                $messageParams = [];
                $whatsappMethod = null;
                break;
        }
        try {
            $whatsappResponse = $this->whatsapp->{$whatsappMethod}($nomorTujuan, $messageParams);
            $whatsappResult = json_decode($whatsappResponse, true);
    
            if (isset($whatsappResult['success']) && $whatsappResult['success'] === true) {
                $response = [
                    'status' => 'success',
                    'message' => 'Reservasi diterima dan pesan terkirim.',
                    'api_response' => $whatsappResult
                ];
            } else {
                $response = [
                    'status' => 'error',
                    'message' => 'API returned: ' . json_encode($whatsappResult),
                    'api_response' => $whatsappResult
                ];
            }
        } catch (Exception $e) {
            $response = [
                'status' => 'error',
                'message' => 'Gagal mengirim pesan: ' . $e->getMessage(),
                'api_response' => null
            ];
        }
    }
        
    //     $messageParams = [
    //         "1" => $buka,  
    //         "2" => $isiPesan, 
    //         "3" => $detail,
    //         "4" => $kodekamar,
    //         "5" => $penutup   
    //     ];

    //     try {
    //         $whatsappResponse = $this->whatsapp->kirim2($nomorTujuan, $messageParams);
    //         $whatsappResult = json_decode($whatsappResponse, true);

    //         if (isset($whatsappResult['success']) && $whatsappResult['success'] === true) {
    //             $response = [
    //                 'status' => 'success',
    //                 'message' => 'Reservasi diterima dan pesan terkirim.',
    //                 'api_response' => $whatsappResult
    //             ];
    //         } else {
    //             $response = [
    //                 'status' => 'error',
    //                 'message' => 'API returned: ' . json_encode($whatsappResult),
    //                 'api_response' => $whatsappResult
    //             ];
    //         }
    //     } catch (Exception $e) {
    //         $response = [
    //             'status' => 'error',
    //             'message' => 'Gagal mengirim pesan: ' . $e->getMessage(),
    //             'api_response' => null
    //         ];
    //     }
    // }
    $this->output
        ->set_content_type('application/json')
        ->set_output(json_encode($response));
  }
  public function modalTolak(){
    $id = $this->input->post('id');
    $reservasi = $this->ModelAdmision->getModalEdit($id);
    $data = array(
        'id' => $id,
        'NORM' => $reservasi['NORM'],
        'PASIEN' => $reservasi['PASIEN'],
        'NOHP' => $reservasi['NOHP'],
        'TGL_RENCANA' => $reservasi['TGL_RENCANA']
    );

    $msg = array(
        'sukses' => $this->load->view('Admision/reservasi/IndexReservasiTolak', $data, true)
    );
    echo json_encode($msg); 
  }
  public function tolakReservasi()
  {
    $data = $this->input->post();
    $statusKirim = $this->input->post('statusKirim');

    $this->db->trans_start();

    // Log the reservation change
    $reservasi_data = [
        'ID_RESERVASI' => $data['idReservasi'],
        'OLEH' => $this->session->userdata('id_simpel'),
        'FIELD_RUBAH' => "STATUS",
        'ISI_LAMA' => 1,
        'ISI_NEW' => 0,
        'ALASAN_EDIT' => $data['pesan'],
        'TGL_UPDATE' => date('Y-m-d H:i:s'),
    ];
    $this->db->insert('db_rekammedis.tb_reservasi_log', $reservasi_data);

    // Update reservation status
    $update_reservasi = [    
        'ID_ACC' => $this->session->userdata('id_simpel'),        
        'TGL_ACC' => date('Y-m-d H:i:s'),
        'STATUS' => 0,
    ];
    $this->db->where('ID', $data['idReservasi']);
    $this->db->update('db_rekammedis.tb_reservasi', $update_reservasi);

    if ($this->db->trans_status() === FALSE) {
        $this->db->trans_rollback();
        $response = ['status' => 'error', 'message' => 'Database transaction failed.'];
    } else {
        $this->db->trans_commit();
        
        if ($statusKirim == 1) {
            $nomorTujuan = $data['nohp'];
            if (substr($nomorTujuan, 0, 1) == '0') {
                $nomorTujuan = '+62' . substr($nomorTujuan, 1);
            } elseif (substr($nomorTujuan, 0, 1) != '+') {
                $nomorTujuan = '+62' . $nomorTujuan;
            }
            
            $tglRencana = $this->formatIndonesianTgl($data['tgl_rencana']);
            $buka = (date("H") >= '05' && date("H") < "10" ? "Pagi" : (date("H") >= '10' && date("H") < "15" ? "Siang" : (date("H") >= '15' && date("H") < "19" ? "Sore" : "Malam")))." "."Bapak/Ibu " . $data['namaPas'];
            $isiPesan = "informasi terkait reservasi Anda"." ";
            $tutup = "Kami dari Admisi bagian Rawat Inap RSK Dharmais ingin menyampaikan bahwa reservasi Anda di tanggal ".$tglRencana." telah *dibatalkan* karena ". $data['pesan'] .". Jika membutuhkan informasi lebih lanjut, silakan hubungi nomor ".$this->nomorAdmin;

            
            $messageParams = [
                "1" => $buka,  
                "2" => $isiPesan, 
                "3" => $tutup,    
            ];

            try {
                $whatsappResponse = $this->whatsapp->send($nomorTujuan, $messageParams);
                $whatsappResult = json_decode($whatsappResponse, true);

                if (isset($whatsappResult['success']) && $whatsappResult['success'] === true) {
                    $response = [
                        'status' => 'success',
                        'message' => 'Reservasi telah ditolak dan pesan telah terkirim.',
                        'api_response' => $whatsappResult
                    ];
                } else {
                    $response = [
                        'status' => 'error',
                        'message' => 'API returned: ' . json_encode($whatsappResult),
                        'api_response' => $whatsappResult
                    ];
                }
            } catch (Exception $e) {
                $response = [
                    'status' => 'error',
                    'message' => 'Gagal mengirim pesan: ' . $e->getMessage(),
                    'api_response' => null
                ];
            }
        } else {
            $response = [
                'status' => 'success',
                'message' => 'Reservasi telah ditolak.',
            ];
        }
    }

    $this->output
        ->set_content_type('application/json')
        ->set_output(json_encode($response));
  } 

  public function indexReservasiTerima(){
    $this->load->view('Admision/reservasi/IndexReservasi2');
  }
  
  public function modalBatal(){
    $id = $this->input->post('id');
    $reservasi = $this->ModelAdmision->getModalEdit($id);
    $data = array(
      'id' => $id,
      'NORM' => $reservasi['NORM'],
      'PASIEN' => $reservasi['PASIEN'],
      'NOHP' => $reservasi['NOHP'],
      'TGL_RENCANA' => $reservasi['TGL_RENCANA'],
      'RE_SIMPEL' => $this->input->post('simpel'),
    );

    $msg = array(
        'sukses' => $this->load->view('Admision/reservasi/IndexReservasiBatal', $data, true)
    );
    echo json_encode($msg); 
  }
  public function batalReservasi()
  {
    $data = $this->input->post();

    $reservasi_data = [
      'ID_RESERVASI' => $data['idReservasi'],
      'OLEH' => $this->session->userdata('id_simpel'),
      'FIELD_RUBAH' => "STATUS",
      'ISI_LAMA' => 2,
      'ISI_NEW' => 0,
      'ALASAN_EDIT'  => 'batal rawat inap: ' . $data['pesan'],
      'TGL_UPDATE' => date('Y-m-d H:i:s'),
    ];
    $this->db->insert('db_rekammedis.tb_reservasi_log', $reservasi_data);

    
    $update_reservasi = [    
    //   'ID_ACC' => $this->session->userdata('id_simpel'),        
    //   'TGL_ACC' => date('Y-m-d H:i:s'),
      'STATUS' => 0,
    ];
    $this->db->where('ID', $data['idReservasi']);
    $this->db->update('db_rekammedis.tb_reservasi', $update_reservasi);

    $update_simpel = [    
        'STATUS' => 0,
    ];
    $this->db->where('NOMOR', $data['re_simpel']);
    $this->db->update('pendaftaran.reservasi', $update_simpel);

    if ($this->db->trans_status() === FALSE) {
        $response = ['status' => 'error', 'message' => 'Database transaction failed.'];
    } else {
      $nomorTujuan = $data['nohp'];
      if (substr($nomorTujuan, 0, 1) == '0') {
          $nomorTujuan = '+62' . substr($nomorTujuan, 1);
      } elseif (substr($nomorTujuan, 0, 1) != '+') {
          $nomorTujuan = '+62' . $nomorTujuan;
      }

      $tglRencana = $this->formatIndonesianTgl($data['tgl_rencana']);
      $buka = (date("H") >= '05' && date("H") < "10" ? "Pagi" : (date("H") >= '10' && date("H") < "15" ? "Siang" : (date("H") >= '15' && date("H") < "19" ? "Sore" : "Malam")))." "."Bapak/Ibu " . $data['namaPas'];
      $isiPesan = "informasi terkait reservasi Anda"." ";
      $tutup = "Kami dari Admisi bagian Rawat Inap RSK Dharmais ingin menyampaikan bahwa reservasi Anda di tanggal ".$tglRencana." telah *dibatalkan* karena ". $data['pesan'].". Jika membutuhkan informasi lebih lanjut, silakan hubungi nomor ".$this->nomorAdmin;
      // $isiPesanGabungan = $isiPesan . " \n " . $alasanPembatalan;      
      $messageParams = [
          "1" => $buka,  
          "2" => $isiPesan, 
          "3" => $tutup,    
      ];

      try {
          $whatsappResponse = $this->whatsapp->send($nomorTujuan, $messageParams);
          $whatsappResult = json_decode($whatsappResponse, true);

          if (isset($whatsappResult['success']) && $whatsappResult['success'] === true) {
              $response = [
                  'status' => 'success',
                  'message' => 'Status rawat inap telah dibatalkan dan pesan telah terkirim.',
                  'api_response' => $whatsappResult
              ];
          } else {
              $response = [
                  'status' => 'error',
                  'message' => 'API returned: ' . json_encode($whatsappResult),
                  'api_response' => $whatsappResult
              ];
          }
      } catch (Exception $e) {
          $response = [
              'status' => 'error',
              'message' => 'Gagal mengirim pesan: ' . $e->getMessage(),
              'api_response' => null
          ];
      }
    }
    $this->output
        ->set_content_type('application/json')
        ->set_output(json_encode($response));
  }

  public function indexReservasiTolak(){
    $this->load->view('Admision/reservasi/IndexReservasi3');
  }  
  public function indexReservasiKamar(){
    $this->load->view('Admision/reservasi/IndexKamar');    
  }
  public function listRuangan() {
    $kelas_id = $this->input->get('kelas_id'); 
    if ($kelas_id) {
        $ruangan = $this->ModelAdmision->listRuangan($kelas_id);

        echo json_encode($ruangan);
    } else {
        echo json_encode([]); 
    }
  }
  public function listKamar() {
    
    $kelas_id = $this->input->post('kelas_id');
    $ruangan_id = $this->input->post('ruangan_id');
    $status = $this->input->post('status');
    $list_ruangan = $this->ModelAdmision->list_ruangan($kelas_id,$ruangan_id,$status); 
		$carousel_data = [];

		foreach ($list_ruangan as $ruangan) {
			$ruangan_id = $ruangan['RUANGAN'];
			$nama_ruangan = $ruangan['NAMA_RUANGAN'];
			$pasien_data = $this->ModelAdmision->list_pasien($ruangan_id,$status);
			$chunked_pasien = array_chunk($pasien_data, 24);
			foreach ($chunked_pasien as $index => $chunk) {
				$carousel_data[] = [
					'nama_ruangan' => $nama_ruangan . ($index > 0 ? "(" . ($index + 1) . ")": ""),
					'pasien' => $chunk
				];
			}
		}

		$data = [
			'carousel_data' => $carousel_data,
		];

    $this->load->view('Admision/reservasi/IndexKamarList', $data);
  }

  public function pesanTunggu(){
    $data = $this->input->post('data'); 
    // var_dump($data);
    // exit;
    $successCount = 0;
    $whatsappResults = [];
    $processedNumbers = [];
    $besok = $this->formatIndonesianTgl(date('Y-m-d', strtotime('+1 day')));

    foreach ($data as $row) {
        $nomorTujuan = $row['nohp'];
        if (in_array($nomorTujuan, $processedNumbers)) {
            continue;
        }
        $processedNumbers[] = $nomorTujuan; 
        // $tglRencana = date("d,M Y", strtotime($row['tglrencana']));
        $tglRencana = $besok;
        if (substr($nomorTujuan, 0, 1) == '0') {
            $nomorTujuan = '+62' . substr($nomorTujuan, 1);
        } elseif (substr($nomorTujuan, 0, 1) != '+') {
            $nomorTujuan = '+62' . $nomorTujuan;
        }

        // isi pesan WhatsApp
        $buka = (date("H") >= '05' && date("H") < "10" ? "Pagi" : 
                 (date("H") >= '10' && date("H") < "15" ? "Siang" : 
                 (date("H") >= '15' && date("H") < "19" ? "Sore" : "Malam"))) 
                 . " Bapak/Ibu " . $row['namaPas'];

        // $isiPesan = "Kami mohon maaf atas pesan WhatsApp yang sebelumnya terkirim. Pesan tersebut merupakan bagian dari uji coba sistem kami dan tidak ditujukan secara personal.";
        // $tutup = "Terima kasih atas pengertian dan kesediaan Anda untuk memakluminya.";

        $isiPesan = "kami sampaikan informasi mengenai ketersediaan kamar rawat inap"." ";
        $tutup = "Kami dari Admisi bagian Rawat Inap RSK Dharmais, menyampaikan untuk ketersediaan kamar rawat inap pada reservasi tanggal " .$tglRencana.". MOHON MAAF sampai hari ini MASIH PENUH, untuk ketersediaan kamar berikutnya akan kami konfirmasi ulang setiap harinya. Jika membutuhkan informasi lebih lanjut , silahkan hubungi nomor ".$this->nomorAdmin;

        // towerC
        $keterangan = "Administrasi Terpadu";
        $penutup = "Layanan Eksekutif RSK DHARMAIS";
       
        switch ($this->stat_admision) {
            case 3:
            case 1:
                $messageParams = [
                    "1" => $buka,
                    "2" => $isiPesan,
                    "3" => $tutup
                ];
                $whatsappMethod = 'send';
                break;
        
            case 2:
                $messageParams = [
                   "1" => $buka,
                   "2" => $this->nomorAdmin,
                   "3" => $keterangan,
                   "9" => $penutup
                ];
                $whatsappMethod = 'kirim_tunggu_towerc';
                break;
        
            default:
                // Tidak melakukan apa-apa
                $messageParams = [];
                $whatsappMethod = null;
                break;
        }
        try {
            // $whatsappResponse = $this->whatsapp->send($nomorTujuan, $messageParams);
            $whatsappResponse = $this->whatsapp->{$whatsappMethod}($nomorTujuan, $messageParams);
            $whatsappResult = json_decode($whatsappResponse, true);

            if (isset($whatsappResult['success']) && $whatsappResult['success'] === true) {
                $successCount++;
                $whatsappResults[] = [
                    'ID_RESERVASI' => $row['id'],
                    'TANGGAL' => date('Y-m-d H:i:s')
                ];
            }
        } catch (Exception $e) {
            continue; 
        }
    }

    if ($successCount > 0) {
        $this->ModelAdmision->insertBatchPesanLog($whatsappResults);
    }

    $response = [
        'status' => ($successCount > 0) ? 'success' : 'error',
        'message' => ($successCount > 0) ? 'Pesan berhasil dikirim' : 'Tidak ada pesan yang terkirim',
        'data' => $whatsappResults
    ];

    $this->output
        ->set_content_type('application/json')
        ->set_output(json_encode($response));
 }
 public function checkSmsToday() {
    $id = $this->input->post('id');
    $today = date('Y-m-d');
    
    $count = $this->ModelAdmision->checkSmsHistory($id, $today);
    
    echo json_encode([
        'hasSentToday' => $count > 0
    ]);
}

//  public function pesanKepastian(){
//     $data = $this->input->post('data'); 
//     $successCount = 0;
//     $processedNumbers = [];

//     foreach ($data as $row) {
//         $nomorTujuan = $row['nohp'];
//         if (in_array($nomorTujuan, $processedNumbers)) {
//             continue;
//         }
//         $processedNumbers[] = $nomorTujuan; 
//         // $tglRencana = $this->formatIndonesianTgl($row['tglrencana']);
//         $tglRencana = $this->formatIndonesianTgl(date('Y-m-d'));
//         if (substr($nomorTujuan, 0, 1) == '0') {
//             $nomorTujuan = '+62' . substr($nomorTujuan, 1);
//         } elseif (substr($nomorTujuan, 0, 1) != '+') {
//             $nomorTujuan = '+62' . $nomorTujuan;
//         }

//         $buka = (date("H") >= '05' && date("H") < "10" ? "Pagi" : 
//                 (date("H") >= '10' && date("H") < "15" ? "Siang" : 
//                 (date("H") >= '15' && date("H") < "19" ? "Sore" : "Malam"))) 
//                 . " Bapak/Ibu " . $row['namaPas'];
//         $isiPesan = "kami mengingatkan jadwal rawat inap Anda"." ";
//         $tutup = "Mohon kehadirannya pada tanggal " . $tglRencana . " dengan range waktu " . $row['waktu'] . ". Apabila ada kendala, silakan menghubungi nomor " . $this->nomorAdmin;

//         $messageParams = [
//             "1" => $buka,
//             "2" => $isiPesan,
//             "3" => $tutup
//         ];

//         try {
//             $whatsappResponse = $this->whatsapp->send($nomorTujuan, $messageParams);
//             $whatsappResult = json_decode($whatsappResponse, true);

//             if (isset($whatsappResult['success']) && $whatsappResult['success'] === true) {
//                 $successCount++;
//             }
//         } catch (Exception $e) {
//             continue; 
//         }
//     }

//     $response = [
//         'status' => ($successCount > 0) ? 'success' : 'error',
//         'message' => ($successCount > 0) ? 'Pesan berhasil dikirim' : 'Tidak ada pesan yang terkirim'
//     ];

//     $this->output
//         ->set_content_type('application/json')
//         ->set_output(json_encode($response));
// }
public function pesanKepastian() {
    $data = $this->input->post('data'); 
    $successCount = 0;
    $failedMessages = [];
    $processedNumbers = []; // Array to track processed numbers
    $whatsappResults = [];

    foreach ($data as $row) {
        $nomorTujuan = $row['nohp'];
        // Skip if number already processed
        if (in_array($nomorTujuan, $processedNumbers)) {
            continue; // Skip sending message to already processed number
        }
        $processedNumbers[] = $nomorTujuan; // Add number to processed list
        
        // Format phone number
        if (substr($nomorTujuan, 0, 1) == '0') {
            $nomorTujuan = '+62' . substr($nomorTujuan, 1);
        } elseif (substr($nomorTujuan, 0, 1) != '+') {
            $nomorTujuan = '+62' . $nomorTujuan;
        }

        $tglRencana = $this->formatIndonesianTgl(date('Y-m-d'));
        
        // Prepare message
        $buka = (date("H") >= '05' && date("H") < "10" ? "Pagi" : 
                (date("H") >= '10' && date("H") < "15" ? "Siang" : 
                (date("H") >= '15' && date("H") < "19" ? "Sore" : "Malam"))) 
                . " Bapak/Ibu " . $row['namaPas'];
        $isiPesan = "kami mengingatkan jadwal rawat inap Anda";
        $tutup = "Mohon kehadirannya pada tanggal " . $tglRencana . " dengan range waktu " . $row['waktu'] . 
                ". Apabila ada kendala, silakan menghubungi nomor " . $this->nomorAdmin;

        $messageParams = [
            "1" => $buka,
            "2" => $isiPesan,
            "3" => $tutup
        ];

        try {
            $whatsappResponse = $this->whatsapp->send($nomorTujuan, $messageParams);
            $whatsappResult = json_decode($whatsappResponse, true);

            if (isset($whatsappResult['success']) && $whatsappResult['success'] === true) {
                $successCount++;
                $whatsappResults[] = [
                    'ID_RESERVASI' => $row['id'],
                    'TANGGAL' => date('Y-m-d H:i:s'),
                    'STATUS' => 2
                ];
            } else {
                $failedMessages[] = [
                    'number' => $nomorTujuan,
                    'error' => isset($whatsappResult['message']) ? $whatsappResult['message'] : 'Unknown error'
                ];
            }
        } catch (Exception $e) {
            $failedMessages[] = [
                'number' => $nomorTujuan,
                'error' => $e->getMessage()
            ];
            continue;
        }
    }
    if ($successCount > 0) {
        $this->ModelAdmision->insertBatchPesanLog($whatsappResults);
    }

    $response = [
        'status' => ($successCount > 0) ? 'success' : 'error',
        'message' => sprintf('Berhasil mengirim %d pesan dari %d total', $successCount, count($data)),
        'failed_messages' => $failedMessages,
        'success_count' => $successCount,
        'total_attempts' => count($data),
        'data' => $whatsappResults 
    ];

    $this->output
        ->set_content_type('application/json')
        ->set_output(json_encode($response));
}


public function updateKontakPasien() {
    $nomr = $this->input->post('nomr');
    $no_telp = $this->input->post('no_telp');

    $this->db->where('NORM', $nomr);
    $update = $this->db->update('master.kontak_pasien', ['NOMOR' => $no_telp]);
    
    if ($update) {
        echo json_encode(['status' => 'success']);
    } else {
        echo json_encode(['status' => 'error']);
    }
}


//gawe user
public function indexUser() {
    $data = array(      
        'isi' => 'Admision/user/Index'
    );
    $this->load->view('Layout/Wrapper', $data);
}
public function getPegawaiList() {
    $search = $this->input->get('query');
    $result = $this->ModelAdmision->getPegawaiList($search);
    
    $list = array();
    foreach ($result as $row) {
        $list[] = array(
            'label' => $row['PEGAWAI'],
            'id' => $row['ID'],
            'username' => $row['LOGIN']
        );
    }
    
    echo json_encode($list);
}
public function simpanUser() {
    $data = array(
        'id_simpel' => $this->input->post('id_simpel'),
        'nama_lengkap' => $this->input->post('nama_lengkap'),
        'admision' => $this->input->post('admision'),
        'stat_admision' => $this->input->post('stat_admision'),
        'status' => 1
    );

    try {
        $this->db->trans_start();
        $existing = $this->db->get_where('db_rekammedis.tb_operator', ['id_simpel' => $data['id_simpel']])->row();
        
        if ($existing) {
            $response = array('status' => 'error', 'message' => 'User sudah terdaftar');
        } else {
            $this->db->insert('db_rekammedis.tb_operator', $data);
            
            if ($this->db->affected_rows() > 0) {
                $this->db->trans_commit();
                $response = array('status' => 'success');
            } else {
                $this->db->trans_rollback();
                $response = array('status' => 'error', 'message' => 'Gagal menyimpan data');
            }
        }
    } catch (Exception $e) {
        $this->db->trans_rollback();
        $response = array('status' => 'error', 'message' => 'Terjadi kesalahan: ' . $e->getMessage());
    }

    echo json_encode($response);
}

public function getOperatorList() 
{
    $list = $this->ModelAdmision->getOperatorData();
    
    $data = array();
    foreach ($list as $operator) {
        $task = '';
        switch ($operator['stat_admision']) {
            case '1':
                $task = 'Admin';
                break;
            case '2':
                $task = 'Petugas Gedung C';
                break;
            case '3':
                $task = 'Petugas Gedung Lama';
                break;
            default:
                $task = 'Unknown';
        }
        
        $operator['TASK'] = $task;
        $data[] = $operator;
    }
    
    $output = array(
        "data" => $data
    );
    
    echo json_encode($output);
}





}
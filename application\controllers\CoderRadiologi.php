<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class CoderRadiologi extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if($this->session->userdata('logged_in') == FALSE ){
      redirect('login');  
    }
    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('ModelCoderRadiologi'));
  }

  public function index()
  {
    $data=array(      
      // 'listkat'        => $this->ModelInput->tampilKategori(),
      // 'listperusahaan' => $this->ModelInput->listPerusahaan(),
      // 'listno'         => $this->ModelInput->listnoPks(),
      // 'dataRadiologi' => $this->ModelCoder->editlistCosting(),
      'isi'            => 'CoderRadiologi/Index'
    );
    $this->load->view('Layout/Wrapper',$data);
  }

public function list_hasil()
    {
        $result = $this->ModelCoderRadiologi->master_hasil();
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row['ID'];
            $sub_array['text'] = $row['DESKRIPSI'];
            $data[] = $sub_array;
        }
        echo json_encode($data);
    }

public function getListdata()
  {
    $draw = intval($this->input->post("draw"));
    $start = intval($this->input->post("start"));
    $length = intval($this->input->post("length"));
    $search = $this->input->post("search")['value']; 

    $listdata = $this->ModelCoderRadiologi->dataListRadiologi($start, $length, $search);
    $data=array();
    $no =1;
    foreach ($listdata['data'] as  $field) {

      // $edit = "<button type='button' class='btn btn-primary radius-30' data-id='" .$field->ID. "' data-bs-toggle='modal' data-bs-target='#modal_edit_radiologi'>
      //         Edit
      //       </button>";
      $edit = '<button type="button" class="btn btn-primary btn-rounded btn-sm" title="edit data" onclick="editinput('.$field->ID.')" style="width:72px"><i class="fa fa-edit"></i> Edit</button>';
      // $cek = '<input type="checkbox" value="'.$field->ID.'"  class="cek_done">';

      // if($field->HASIL_RAD=='1'){
      //   $hasil_rad = 'Belum Ada Hasil';
      // } else if ($field->HASIL_RAD=='2'){
      //   $hasil_rad = 'Sudah Ada Hasil';
      // } else {
      //   $hasil_rad = 'Lain-lain';
      // }

      if($field->STATUS=='1'){
        $status = 'Belum Dikerjakan';
        $cek = '<input type="checkbox" data-id="'.$field->ID.'" value="'.$field->STATUS.'"  class="cek_done">';
      } else {
        $status = 'Sudah Dikerjakan';
        $cek = '<input type="checkbox" data-id="'.$field->ID.'" value="'.$field->STATUS.'"  class="cek_done" checked>';
      }

      $tgl_indo=date_format(date_create($field->TANGGAL_PENDAFTARAN),'d-m-Y H:i:s');

      $data[] = array(
        $no,
        $field->NORM, 
        $field->NAMA_PASIEN,
        $tgl_indo,
        // $field->TANGGAL_PENDAFTARAN,
        $field->DESKRIPSI,
        $edit        
      );
      $no++;

    }

    $output = array(
      "draw" => $draw,
      "recordsTotal" => $listdata['recordsTotal'],
      "recordsFiltered" => $listdata['recordsFiltered'],
      "data" => $data
    );
    echo json_encode($output);
  }

public function UpdateCek()
    {
    $this->db->trans_begin();
    $post = $this->input->post();
    $id =$post['id'];
    $value =$post['value'];

    if($value==1){
      $dataUpdate = array (
          'STATUS'                           => 2,
        );
    } else if($value==2){
      $dataUpdate = array (
          'STATUS'                           => 1,
        );
    }
      
    // echo "<pre>";print_r($id);exit();
        $this->db->where('db_rekammedis.tb_coding_detail.ID', $id);
        $this->db->update('db_rekammedis.tb_coding_detail', $dataUpdate);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
    }
   
  public function modalEditRadiologi()
  {
    if($this->input->is_ajax_request()==true){
      $id = $this->input->post('id');
      $ambildata = $this->ModelCoderRadiologi->editlistCosting($id);
      if($ambildata->num_rows()>0){
        $row=$ambildata->row_array();
        $data=[
          
          // 'listkat'           => $this->ModelPerusahaan->tampilKategori(),
          'ID'                  => $row['ID'],
          'NORM'                => $row['NORM'],
          'NAMA_PASIEN'         => $row['NAMA_PASIEN'],
          'TANGGAL_PENDAFTARAN' => $tgl_indo=date_format(date_create($row['TANGGAL_PENDAFTARAN']),'d-m-Y H:i:s'),
          'HASIL_RAD'           => $row['HASIL_RAD'],
          'DESKRIPSI'           => $row['DESKRIPSI'],
        ];
      }
      $msg=[
        'sukses'=>$this->load->view('CoderRadiologi/modal_edit_radiologi', $data, true)
      ];
      echo json_encode($msg);
    }
  }

function modalEditRadiologiXXX()
  {
    $id = $this->input->post('id');
    $datalistcosting = $this->ModelCoder->editlistCosting($id)->row_array();
    $data = array(
        'id'          => $id,
        'datalistcosting'     => $datalistcosting,
    );
    $this->load->view('CoderRadiologi/modal_edit_radiologi',$data);
  }

public function UpdateRadiologi()
{
    $post = $this->input->post();
    $this->db->trans_begin();
    $id = $this->input->post('ID');
    $tgledit = date('Y-m-d H:i:s');
    $data_insert = array(
        // 'NORM'        => $this->input->post('NORM'), 
        // 'NAMA_PASIEN'   => $this->input->post('NAMA_PASIEN'),
        // 'TANGGAL_PENDAFTARAN'  => $this->input->post('TANGGAL_PENDAFTARAN'),
        'HASIL_RAD'    => $this->input->post('HASIL'),
    );

    // Lakukan pembaruan di database
    $this->db->where('ID', $id);
    $this->db->update('db_rekammedis.tb_coding_detail', $data_insert);

    // Periksa status transaksi dan kirimkan respons sesuai dengan itu
    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
    } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
    }

    echo json_encode($result);
}

    

}
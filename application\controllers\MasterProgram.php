<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class MasterProgram extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    // if($this->session->userdata('logged_in') == FALSE ){
    //   redirect('login');
    // }
    date_default_timezone_set("Asia/Bangkok");
    // $this->load->model(array('profileModel','MasterModel','ModelIjoc'));
  }

  public function index()
  {
    $data=array(      
      'isi'       => 'MasterProgram/Index'
    );
    $this->load->view('Layout/Wrapper',$data, FALSE);
  }

  public function listprogram()
  {
    $draw   = intval($this->input->get("draw"));
    $start  = intval($this->input->get("start"));
    $length = intval($this->input->get("length"));
    $listprogram = $this->ModelAnggaran->listprogram();
// echo "<pre>";print_r($listPegawai);exit();
    $data  = array();
    $no    =1;
    foreach($listprogram->result() as $mp) {

     $edit = '<button type="button" class="btn btn-success btn-sm" title="edit data" onclick="btneditijoc('.$mp->ID.')"><i class="fa fa-edit"></i> Edit</button>';
     $verif = '<button type="button" class="btn btn-danger btn-sm verifijoc" title="Verif data" data-id="'.$mp->ID.'"><i class="fa fa-trash"></i> Hapus</button>';

     $data[] = array(
      $no,
      $mp->KODE,
      $mp->PROGRAM,
      $mp->JUMLAH,
      $edit.' '.$verif

    );
     $no++;
   }

   $output = array(
    "draw"            => $draw,
    "recordsTotal"    => $listprogram->num_rows(),
    "recordsFiltered" => $listprogram->num_rows(),
    "data"            => $data
  );
   echo json_encode($output);
 }

 public function simpan($param)
 {
  if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
    if ($param == 'tambah' || $param == 'ubah') {
      $post = $this->input->post();
        // echo "<pre>";print_r($post);exit();
      $this->db->trans_begin();

      $data_insert = array (
        'PROGRAM'     => $this->input->post('PROGRAM'), 
        'JUMLAH'      => $this->input->post('JUMLAH'),
        'KODE'        => $this->input->post('KODE'),
        'OLEH'        => $this->session->userdata("id")
      );
   // echo "<pre>";print_r($data_insert);exit();

      $this->db->insert('anggaran.program', $data_insert);

      if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
      } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
      }
      echo json_encode($result);
    }
  }
}

public function ModalEditijoc()
{
  if ($this->input->is_ajax_request() == true){
    $idijoc        = $this->input->POST('id',true);
    $dokter        = $this->MasterModel->listDr();
    $editor        = $this->MasterModel->listEditor();
    $ambildata     = $this->ModelIjoc->Ambildata($idijoc);
    if($ambildata->num_rows()>0){
      $row=$ambildata->row_array();
      $data=[   
       'ID'               => $row['ID'],
       'dokter'           => $dokter,
       'editor'           => $editor,
       'KETERANGAN_IJOC'  => $row['KETERANGAN_IJOC'],
       'JENIS_IJOC'       => $row['JENIS_IJOC'],
       'RUPIAH'           => $row['RUPIAH'],
       'ID_DOKTER'         => $row['ID_DOKTER'],
     ];
   }
   $msg=[
    'sukses'=> $this->load->view('Ijoc/ModalEditijoc',$data,true)
  ];
  echo json_encode($msg);
}
}

public function editijoc($param)
{
  if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
    if ($param == 'tambah' || $param == 'ubah') {
      $post = $this->input->post();

        // echo "<pre>";print_r($post);exit();
      $this->db->trans_begin();

      $dataedit = array (
       'ID_DOKTER'        => $this->input->post('DOKTER'), 
       'KETERANGAN_IJOC'  => $this->input->post('KETERANGAN_IJOC'),
       'JENIS_IJOC'       => $this->input->post('JENIS_IJOC'),
       'RUPIAH'           => $this->input->post('RUPIAH'),
     );

      $this->db->where('db_remunmedis.tb_ijoc.ID',$this->input->post('ID'));
      $this->db->update('db_remunmedis.tb_ijoc',$dataedit);

      if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
      } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
      }
      echo json_encode($result);
    }
  }
}

public function verifijoc($param)
{
  if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
    if ($param == 'tambah' || $param == 'ubah') {
      $post = $this->input->post();
      $id = $this->input->post('id');
        // echo "<pre>";print_r($post);exit();
      $this->db->trans_begin();
      $tglverif = date('Y-m-d H:i:s');
      $verif = array (
       'VERIF_OLEH'       => $this->session->userdata("id"),
       'TANGGAL_VERIF'    => $tglverif,  
       'STATUS_VERIF'     => 1, 
     );

      $this->db->where('db_remunmedis.tb_ijoc.ID',$id);
      $this->db->update('db_remunmedis.tb_ijoc',$verif);

      if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
      } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
      }
      echo json_encode($result);
    }
  }
}

function getrupiah()
{
  $id = $this->input->post('id');
  $data = $this->ModelIjoc->getrupiah($id);
  echo json_encode($data);
}

}
<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Pemantauan extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if($this->session->userdata('logged_in') == FALSE ){
      redirect('login');  
    }
    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('ModelPemantauan'));
  }
  public function index_pantau_ri()
  {
    $data=array(      
      'isi'            => 'Pemantauan/Index_pantau_ri'
    );
    $this->load->view('Layout/Wrapper',$data);
  }

  public function index_pantau_riT(){
  	// $data=array(      
    //   // 'listkat'        => $this->ModelInput->tampilKategori(),
  	// 	'isi'            => 'Pemantauan/Index_pantau_ri'
  	// );
  	$this->load->view('Pemantauan/Index_pantau_riT');
  }
  public function index_pantau_riH(){
    $this->load->view('Pemantauan/Index_pantau_riH');
  }


  public function index_pantau_rj()
  {
    $data=array(      
      // 'listkat'        => $this->ModelInput->tampilKategori(),
      'isi'            => 'Pemantauan/Index_pantau_rj'
    );
    $this->load->view('Layout/Wrapper',$data);
  }
  public function index_pantau_rjT(){
  	$this->load->view('Pemantauan/Index_pantau_rjT');
  }
  public function index_pantau_rjH(){
    $this->load->view('Pemantauan/Index_pantau_rjH');
  }

  public function dataPantauRI()
  {
    // $draw   = intval($this->input->POST("draw"));
    // $start  = intval($this->input->POST("start"));
    // $length = intval($this->input->POST("length"));
    $user = $this->session->userdata('id');
    $listdata = $this->ModelPemantauan->getdataPantauRI($user);
    
    $data=array();
    $no =1;
    foreach ($listdata->result() as $field) {

    // $cek = '<input type="checkbox" value="'.$field->ID.'"  class="cek_done">';
      // $edit = '<button type="button" class="btn btn-primary btn-rounded btn-sm" title="edit data" onclick="ceklis('.$field->ID.')" data-id="'.$field->JENIS_FORM.'" style="width:72px"><i class="fa fa-edit"></i> </button>';
      $edit = '<button type="button" class="btn btn-primary btn-rounded btn-sm" title="edit data" onclick="editArsip('.$field->ID.')" style="width:72px"><i class="fa fa-edit"></i></button>';      
      $hapus = '<button type="button" style="width: 45%;" class="btn btn-danger hapus_pantau_ri" href="javaScript:;" id="hapus_pantau_ri" title="hapus_pantau_ri" data="'.$field->ID.'"><i class="fa fa-trash"></i></button>';      
      $edit2 = '<a href="#modalcek" class="btn btn-sm btn-primary" data-toggle="modal" data-id="'.$field->ID.'"><i class="fas fa-search"></i> Lihat</a>';
      $button = '<button type="button" style="width: 45%;" class="btn btn-primary" data-toggle="modal" data-target="#modal_pantau_ri" data-id="'.$field->ID.'"><i class="fas fa-check-double"></i></button>';

      $data[] = array(
        $no,

        $field->NOMR,
        $field->NAMA_PASIEN,
        $field->TANGGAL_MASUK,
        $field->TANGGAL_KELUAR,
        $field->NAMA_DOKTER,
        // $field->JENIS_PANTAU,
        $button.' '.$hapus
        );
        
      $no++;

    }

    $output = array(
      // "draw"            => $draw,
      // "recordsTotal"    => $listdata->num_rows(),
      // "recordsFiltered" => $listdata->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }
  public function simpanRi() {       
    $ID = $this->input->post('ID');
    $user = $this->session->userdata('id');
    $tgl  = date('Y-m-d H:i:s');
    $dataUpdate = array(
      'CREATED_BY' => $user,
      'STATUS' => 2,
      'CREATED_DATE' => $tgl,
    );

    $this->db->where('ID', $ID);
    $this->db->update('db_rekammedis.tb_pemantauan', $dataUpdate);       
    if ($this->db->affected_rows() > 0) {
        $response['status'] = 'success';
    } else {
        $response['status'] = 'error';
    }
    echo json_encode($response);
  }

  // public function simpanRj() {       
  //   $ID = $this->input->post('ID');
  //   $user = $this->session->userdata('id');
  //   $tgl  = date('Y-m-d H:i:s');
  //   $dataUpdate = array(
  //     'CREATED_BY' => $user,
  //     'STATUS' => 2,
  //     'CREATED_DATE' => $tgl,
  //   );

  //   $this->db->where('ID', $ID);
  //   $this->db->update('db_rekammedis.tb_pemantauan', $dataUpdate);       
  //   if ($this->db->affected_rows() > 0) {
  //       $response['status'] = 'success';
  //   } else {
  //       $response['status'] = 'error';
  //   }
  //   echo json_encode($response);
  // }

  public function dataPantauRIH() {
    $user = $this->session->userdata('id');

    $start = $this->input->post('start'); 
    $length = $this->input->post('length'); 
    $search = $this->input->post('search')['value']; 

    // Panggil model untuk mendapatkan data
    $listdata = $this->ModelPemantauan->getdataPantauRIH($user, $start, $length, $search);

    $data = array();
    $no = $start + 1; 

    foreach ($listdata['data'] as $field) {
      $hapus = '<button type="button" style="width:75%;" class="btn btn-sm btn-danger hapus_pantau_ri" id="hapus_pantau_ri" title="hapus_pantau_riH" data="'.$field->ID.'"><i class="fa fa-trash"></i></button>';
      $button = '<button type="button" style="width: 75%;" class="btn btn-sm btn-primary" data-toggle="modal" data-target="#modal_pantau_riH" data-id="'.$field->ID.'"><i class="fa fa-edit"></i></button>';
      

        $data[] = array(
            $no,
            date('d-m-Y H:i:s', strtotime($field->CREATED_DATE)),            
            $field->NOMR,
            $field->NAMA_PASIEN,
            $field->TANGGAL_MASUK,
            $field->TANGGAL_KELUAR,
            $field->NAMA_DOKTER,
            $button . ' ' . $hapus
        );

        $no++;
    }

    $output = array(
        "draw" => $this->input->post('draw'),
        "recordsTotal" => $listdata['recordsTotal'], 
        "recordsFiltered" => $listdata['recordsFiltered'], 
        "data" => $data
    );

    echo json_encode($output);
}

  public function dataPantauRJ()
  {
    $user = $this->session->userdata('id');
    // $draw   = intval($this->input->POST("draw"));
    // $start  = intval($this->input->POST("start"));
    // $length = intval($this->input->POST("length"));

    $listdata = $this->ModelPemantauan->getdataPantauRJ($user);
    
    $data=array();
    $no =1;
    foreach ($listdata->result() as $field) {

    // $cek = '<input type="checkbox" value="'.$field->ID.'"  class="cek_done">';
      // $edit = '<button type="button" class="btn btn-primary btn-rounded btn-sm" title="edit data" onclick="ceklis('.$field->ID.')" data-id="'.$field->JENIS_FORM.'" style="width:72px"><i class="fa fa-edit"></i> </button>';
      // $edit = '<button type="button" class="btn btn-primary btn-rounded btn-sm" title="edit data" onclick="editArsip('.$field->ID.')" style="width:72px"><i class="fa fa-edit"></i></button>';      
      $hapus = '<button type="button" style="width:75%;" class="btn btn-danger hapus_pantau_rj" href="javaScript:;" id="hapus_pantau_rj" title="hapus_pantau_rj" data="'.$field->ID.'"><i class="fa fa-trash"></i></button>';      
      // $edit2 = '<a href="#modalpantaurj" class="btn btn-primary btn-rounded btn-sm" data-toggle="modal" data-id="'.$field->ID.'"><i class="fas fa-edit"></i></a>';
      // $ceklisRJx = '<button type="button" class="btn btn-primary btn-rounded btn-sm" title="ceklis rj" data-toggle="modal" data-id="'.$field->ID.'" href="#modalpantaurj" style="width:72px"><i class="fa fa-edit"></i></button>';
      // $ceklisRJxx = '<button type="button" class="btn btn-primary btn-rounded btn-sm pantauRJ" href="#modalpantaurj:;" data-id="'.$field->ID.'" data-bs-toggle="modal" data-bs-target="#modal_pantau_rj" style="width:72px"><i class="fas fa-edit"></i></button>';
      $button = '<button type="button"style="width:75%;"  class="btn btn-primary" data-toggle="modal" data-target="#modal_pantau_rj" data-id="'.$field->ID.'"><i class="fas fa-check-double"></i></button>';
      // $button2 = '<button type="button" class="btn btn-primary btn-rounded btn-sm" title="Checklist Form" onclick="addRev('.$field->ID.')" style="width:72px"><i class="fas fa-tasks"></i></i></button>';         
       

      $data[] = array(
        $no,

        $field->NOMR,
        $field->NAMA_PASIEN,
        $field->TANGGAL_MASUK,
        $field->TANGGAL_KELUAR,
        $field->NAMA_DOKTER,
        // $field->JENIS_PANTAU,
        $button.' '.$hapus
        );
        
      $no++;

    }

    $output = array(
      // "draw"            => $draw,
      // "recordsTotal"    => $listdata->num_rows(),
      // "recordsFiltered" => $listdata->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }
  public function dataPantauRJH()
{
    $user = $this->session->userdata('id');
    $start = $this->input->post('start'); // DataTables pagination start
    $length = $this->input->post('length'); // DataTables pagination length
    $search = $this->input->post('search')['value']; // DataTables search value

    $listdata = $this->ModelPemantauan->getdataPantauRJH($user, $start, $length, $search);
    
    $data = array();
    $no = $start + 1; // Start numbering from the current page
    foreach ($listdata['data'] as $field) {
        $hapus = '<button type="button" style="width:75%;" class="btn btn-danger hapus_pantau_rj" id="hapus_pantau_rj" title="hapus_pantau_rj" data="'.$field->ID.'"><i class="fa fa-trash"></i></button>';      
        $button = '<button type="button" style="width:75%;" class="btn btn-primary" data-toggle="modal" data-target="#modal_pantau_rjH" data-id="'.$field->ID.'"><i class="fa fa-edit"></i></button>';

        $data[] = array(
            $no,
            $field->NOMR,
            $field->NAMA_PASIEN,
            $field->TANGGAL_MASUK,
            $field->TANGGAL_KELUAR,
            $field->NAMA_DOKTER,
            $button . ' ' . $hapus
        );
        $no++;
    }

    $output = array(
        "draw" => $this->input->post('draw'),
        "recordsTotal" => $listdata['recordsTotal'],
        "recordsFiltered" => $listdata['recordsFiltered'],
        "data" => $data
    );
    echo json_encode($output);
}



  public function CekNomrPantauRI()
{
  $nomr = $this->input->post('NOMR');
  $cekmr = $this->ModelPemantauan->cek_nomr_pantau_ri($nomr);
  if ($cekmr->num_rows() > 0) {
    $result = $cekmr->row_array();
    $row = array(
        'nomr' => $result['NORM'],
        'nama' => $result['NAMAPASIEN'],
        'umur' => $result['UMUR'],
        'jk' => $result['JK'],
        'id_jk' => $result['JENIS_KELAMIN']
    );
  } else {
      $row = array(
          'nomr' => '',
          'nama' => 'Data tidak ditemukan',
          'umur' => 'Data tidak ditemukan',
          'jk' => 'Data tidak ditemukan'
      );
  }

  echo json_encode($row);

  // if ($cekmr->num_rows() > 0) {
  //   $result = $cekmr->result_array();
  //   $tgl_lahir = $result[0]['TANGGAL_LAHIR'];
  //   $age = $this->calculateAge($tgl_lahir);

  //   $jenis_kelamin = $result[0]['JENIS_KELAMIN'];
  //   if($jenis_kelamin==1){
  //   	$jenis_kel = 'LAKI-LAKI';
  //   } elseif ($jenis_kelamin==2) {
  //   	$jenis_kel = 'PEREMPUAN';
  //   }

  //   $data = array(
  //     'nomr' => $result[0]['NORM'], // Asumsi NORM sama untuk semua kunjungan
  //     'nama' => $result[0]['NAMA'], // Asumsi NAMA sama untuk semua kunjungan
  //     'dokter' => $result[0]['NAMA_DOKTER'], // Asumsi NAMA sama untuk semua kunjungan
  //     'id_dokter' => $result[0]['ID_DOKTER'], // Asumsi NAMA sama untuk semua kunjungan
      // 'tgl_masuk' => $result[0]['TGL_MASUK'], // Asumsi NAMA sama untuk semua kunjungan
      // 'tgl_keluar' => $result[0]['TGL_KELUAR'], // Asumsi NAMA sama untuk semua kunjungan
      // 'id_jk' => $result[0]['JENIS_KELAMIN'],
      // 'tgl_lahir' => $age,
      // 'jk' => $jenis_kel,

      // 'kunjungan' => array(),
      // 'ruangan' => array(),
      // 'id_ruangan' => array(),
      // 'tgl_masuk' => array()
    // );

    // foreach ($result as $row) {
      // $data['kunjungan'][] = $row['TGL_MASUK'];
      // $data['ruangan'][] = $row['NAMA_RUANGAN'];
      // $data['id_ruangan'][] = $row['RUANGAN'];
      // $data['tgl_masuk'][] = $row['TGL_MASUK'];
    // }
  // } else {
  //   $data = array(
  //     'nomr' => '',
  //     'nama' => 'Data tidak ditemukan',
  //     'tgl_lahir' => 'Data tidak ditemukan',
  //     'jk' => 'Data tidak ditemukan',
  //     'dokter' => 'Data tidak ditemukan',
  //     // 'tgl_masuk' => 'Data tidak ditemukan',
  //     // 'tgl_keluar' => 'Data tidak ditemukan',
  //     // 'kunjungan' => array(),
  //     // 'ruangan' => array(),
  //     // 'id_ruangan' => array(),
  //     // 'tgl_masuk' => array()
  //   );
  // }

  // echo json_encode($data);
}
public function getTglDatangOptions() {
  $nomr = $this->input->post('NOMR'); 
  $term = $this->input->post('term');
  // error_log("Nilai nomr: " . $nomr);
  $result = $this->ModelPemantauan->getTglDatangOptions($nomr,$term); 
  $data = array();
    foreach ($result as $row) {
        $sub_array = array();
        $sub_array['id'] = $row['NOMOR'];
        $sub_array['text'] = date('d-F-Y H:i:s', strtotime($row['TANGGAL']));
        $data[] = $sub_array;
    }
  echo json_encode($data); 
}
public function getTglDatangOptionsRJ() {
  $nomr = $this->input->post('NOMR'); 
  $term = $this->input->post('term');
  // error_log("Nilai nomr: " . $nomr);
  $result = $this->ModelPemantauan->getTglDatangOptionsRJ($nomr,$term); 
  $data = array();
    foreach ($result as $row) {
        $sub_array = array();
        $sub_array['id'] = $row['NOMOR'];
        $sub_array['text'] = date('d-F-Y H:i:s', strtotime($row['TANGGAL']));
        $data[] = $sub_array;
    }
  echo json_encode($data); 
}
public function autocomplate(){
  $id= $this->input->post('tgl');    
  $cektgl = $this->ModelPemantauan->cekTgl($id);
  
  if ($cektgl->num_rows() > 0) {
      $result = $cektgl->row_array();
      $tglAwal = date('d-F-Y H:i:s', strtotime($result['MASUK']));
      $tglPulang = date('d-F-Y H:i:s', strtotime($result['KELUAR']));
      $row = array(
          'idK' => $result['NOMOR'],
          'nama' => $result['NAMA'],
          'tglAwal' => $tglAwal,
          'tglPulang' => $tglPulang,          
          'nip' => $result['NIP']
          
      );
  } else {
      $row = array(
          'id' => '',
          'nama' => 'Data tidak ditemukan',
          'tglAwal' => 'Data tidak ditemukan',
          'tglPulang' => 'Data tidak ditemukan',
          'id' => 'Data tidak ditemukan'
      );
  }

  echo json_encode($row);
  
}

public function CekNomrPantauRJ()
{
  $nomr = $this->input->post('NOMR');
  $cekmr = $this->ModelPemantauan->cek_nomr_pantau_rj($nomr);
  if ($cekmr->num_rows() > 0) {
    $result = $cekmr->row_array();
    $row = array(
        'nomr' => $result['NORM'],
        'nama' => $result['NAMAPASIEN'],
        'umur' => $result['UMUR'],
        'jk' => $result['JK'],
        'id_jk' => $result['JENIS_KELAMIN']
    );
  } else {
      $row = array(
          'nomr' => '',
          'nama' => 'Data tidak ditemukan',
          'umur' => 'Data tidak ditemukan',
          'jk' => 'Data tidak ditemukan'
      );
  }

  echo json_encode($row);

  // if ($cekmr->num_rows() > 0) {
  //   $result = $cekmr->result_array();
  //   $tgl_lahir = $result[0]['TANGGAL_LAHIR'];
  //   $age = $this->calculateAge($tgl_lahir);

  //   $jenis_kelamin = $result[0]['JENIS_KELAMIN'];
  //   if($jenis_kelamin==1){
  //     $jenis_kel = 'LAKI-LAKI';
  //   } elseif ($jenis_kelamin==2) {
  //     $jenis_kel = 'PEREMPUAN';
  //   }

  //   $data = array(
  //     'nomr' => $result[0]['NORM'], // Asumsi NORM sama untuk semua kunjungan
  //     'nama' => $result[0]['NAMA'], // Asumsi NAMA sama untuk semua kunjungan
  //     'dokter' => $result[0]['NAMA_DOKTER'], // Asumsi NAMA sama untuk semua kunjungan
  //     'id_dokter' => $result[0]['ID_DOKTER'], // Asumsi NAMA sama untuk semua kunjungan
  //     'tgl_masuk' => $result[0]['TGL_MASUK'], // Asumsi NAMA sama untuk semua kunjungan
  //     'tgl_keluar' => $result[0]['TGL_KELUAR'], // Asumsi NAMA sama untuk semua kunjungan
  //     'id_jk' => $result[0]['JENIS_KELAMIN'],
  //     'tgl_lahir' => $age,
  //     'jk' => $jenis_kel,

  //     // 'kunjungan' => array(),
  //     'ruangan' => array(),
  //     'id_ruangan' => array(),
  //     // 'tgl_masuk' => array()
  //   );

  //   foreach ($result as $row) {
  //     // $data['kunjungan'][] = $row['TGL_MASUK'];
  //     $data['ruangan'][] = $row['NAMA_RUANGAN'];
  //     $data['id_ruangan'][] = $row['RUANGAN'];
  //     // $data['tgl_masuk'][] = $row['TGL_MASUK'];
  //   }
  // } else {
  //   $data = array(
  //     'nomr' => '',
  //     'nama' => 'Data tidak ditemukan',
  //     'tgl_lahir' => 'Data tidak ditemukan',
  //     'jk' => 'Data tidak ditemukan',
  //     'dokter' => 'Data tidak ditemukan',
  //     'tgl_masuk' => 'Data tidak ditemukan',
  //     'tgl_keluar' => 'Data tidak ditemukan',
  //     // 'kunjungan' => array(),
  //     'ruangan' => array(),
  //     'id_ruangan' => array(),
  //     // 'tgl_masuk' => array()
  //   );
  // }

  // echo json_encode($data);
}

private function calculateAge($dateOfBirth)
{
  $birthDate = new DateTime($dateOfBirth);
  $today = new DateTime('today');
  $ageYears = $today->diff($birthDate)->y;
  $ageMonths = $today->diff($birthDate)->m;
  $ageDays = $today->diff($birthDate)->d;

  return $ageYears . ' Tahun, ' . $ageMonths . ' Bulan, ' . $ageDays . ' Hari';
}

public function simpanPantauRI()
  { 
    $this->db->trans_begin();

    $post = $this->input->post();
    // echo "<pre>";print_r($post);exit();
    $tglcreate = date('Y-m-d H:i:s');
                 
    $data_insert_pantau_ri = array (
      'NOMR'                => $this->input->post('NOMR_PANTAU_RI'),
      'NAMA_PASIEN'         => $this->input->post('NAMA_PANTAU_RI'),
      'JENIS_KELAMIN'       => $this->input->post('ID_JK_PANTAU_RI'),
      'TANGGAL_MASUK'       => $this->input->post('TANGGAL_MASUK_PANTAU_RI'),
      'TANGGAL_KELUAR'       => $this->input->post('TANGGAL_KELUAR_PANTAU_RI'),
      'ID_DOKTER'             => $this->input->post('ID_DOKTER_PANTAU_RI'),
      'JENIS_PANTAU'         => 2,
      'CREATED_DATE'         => $tglcreate,
      'CREATED_BY'            => $this->session->userdata('id'),
      // 'TANGGAL_INPUT'            => $tglcreate,
      // 'OLEH'          => $this->session->userdata('id'),
    );
   // echo "<pre>";print_r($data_insert);exit();
    $this->db->insert('db_rekammedis.tb_pemantauan', $data_insert_pantau_ri);

    $id_detail = $this->db->insert_id();

    $jenis = 2;

    $getData = $this->ModelPemantauan->formCeklisPantau($jenis)->result_array();

    $data_insert_detail = array();

    foreach($getData as $row){
      $data_insert_detail[] = array(
      // 'ID'       => $id_detail,
      'ID_PANTAU' => $id_detail, 
      'ID_FORM'       => $row['ID']
      // 'STATUS'        => 1,
      );
    }
   // echo "<pre>";print_r($data_insert);exit();

    $this->db->insert_batch('db_rekammedis.tb_pemantauan_detail', $data_insert_detail);

    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
      } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
      }
 
    echo json_encode($result);
    
  }

  public function simpanPantauRJ()
  { 
    $this->db->trans_begin();

    $post = $this->input->post();
    // echo "<pre>";print_r($post);exit();
    $tglcreate = date('Y-m-d H:i:s');
                 
    $data_insert_pantau_rj = array (
      'NOMR'                => $this->input->post('NOMR_PANTAU_RJ'),
      'NAMA_PASIEN'         => $this->input->post('NAMA_PANTAU_RJ'),
      'JENIS_KELAMIN'       => $this->input->post('ID_JK_PANTAU_RJ'),
      'NOPEN'               => $this->input->post('idtglDaftar'),
      'TANGGAL_MASUK'       => date('Y-m-d H:i:s', strtotime($this->input->post('TANGGAL_MASUK_PANTAU_RJ'))),
      'TANGGAL_KELUAR'      => date('Y-m-d H:i:s', strtotime($this->input->post('TANGGAL_KELUAR_PANTAU_RJ'))),      
      'ID_DOKTER'           => $this->input->post('ID_DOKTER_PANTAU_RJ'),
      'JENIS_PANTAU'         => 1,
      'CREATED_DATE'         => $tglcreate,
      'CREATED_BY'            => $this->session->userdata('id'),
    );
   // echo "<pre>";print_r($data_insert);exit();
    $this->db->insert('db_rekammedis.tb_pemantauan', $data_insert_pantau_rj);

    $id_detail = $this->db->insert_id();

    $jenis = 1;

    $getData = $this->ModelPemantauan->formCeklisPantau($jenis)->result_array();

    $data_insert_detail = array();

    foreach($getData as $row){
      $data_insert_detail[] = array(
      // 'ID'       => $id_detail,
      'ID_PANTAU' => $id_detail, 
      'ID_FORM'       => $row['ID'],
      // 'STATUS'        => 1,
      );
    }
   // echo "<pre>";print_r($data_insert);exit();

    if (!empty($data_insert_detail)) {
        $this->db->insert_batch('db_rekammedis.tb_pemantauan_detail', $data_insert_detail);
    }

    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
      } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
      }
 
    echo json_encode($result);
    
  }

  public function simpanPantauRIx()
{
    $this->db->trans_begin();
    $post = $this->input->post();
    $tglcreate = date('Y-m-d H:i:s');
                 
    $data_insert_pantau_ri = array(
        'NOMR'          => $this->input->post('NOMR_PANTAU_RI'),
        'NAMA_PASIEN'   => $this->input->post('NAMA_PANTAU_RI'),
        'JENIS_KELAMIN' => $this->input->post('ID_JK_PANTAU_RI'),
        'NOPEN'         => $this->input->post('idtglDaftar'),
        'TANGGAL_MASUK' => date('Y-m-d H:i:s', strtotime($this->input->post('TANGGAL_MASUK_PANTAU_RI'))),
        'TANGGAL_KELUAR'=> date('Y-m-d H:i:s', strtotime($this->input->post('TANGGAL_KELUAR_PANTAU_RI'))),
        'ID_DOKTER'     => $this->input->post('ID_DOKTER_PANTAU_RI'),
        'JENIS_PANTAU'  => 2,
        'CREATED_DATE'  => $tglcreate,
        'CREATED_BY'    => $this->session->userdata('id'),
    );
    $this->db->insert('db_rekammedis.tb_pemantauan', $data_insert_pantau_ri);

    $id_detail = $this->db->insert_id();
    $jenis = 2;

    $getData = $this->ModelPemantauan->formCeklisPantau($jenis)->result_array();

    // Debug: Echo the retrieved data
    // echo "<pre>Data Retrieved: ";
    // print_r($getData);
    // echo "</pre>";

    $data_insert_detail = array();
    foreach ($getData as $row) {
        $data_insert_detail[] = array(
            'ID_PANTAU' => $id_detail,
            'ID_FORM'   => $row['ID'],
        );
    }

    // Debug: Echo the data to be inserted
    // echo "<pre>Data to Insert: ";
    // print_r($data_insert_detail);
    // echo "</pre>";

    // Remove duplicates from $data_insert_detail
    $data_insert_detail = array_unique($data_insert_detail, SORT_REGULAR);

    // Verify data_insert_detail before insert_batch
    if (!empty($data_insert_detail)) {
        $this->db->insert_batch('db_rekammedis.tb_pemantauan_detail', $data_insert_detail);
    }

    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
    } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
    }

    // Debug: Echo the result
    // echo "<pre>Result: ";
    // print_r($result);
    // echo "</pre>";

    // Comment out the JSON response for debugging purposes
    echo json_encode($result);
}





  public function hapusPantauRI()
  {
    $this->db->trans_begin();
    $post = $this->input->post();
    $id =$post['ID'];

    
    $dataUpdate = array (
      'STATUS'                           => 0,
      'JENIS_PANTAU'  =>0,
    );
    
    // echo "<pre>";print_r($id);exit();
    // $this->db->where('db_rekammedis.tb_pemantauan.ID', $id);
    // $this->db->update('db_rekammedis.tb_pemantauan', $dataUpdate);
    $this->db->where('ID', $id); 
    $this->db->update('db_rekammedis.tb_pemantauan', $dataUpdate); 

    $this->db->set('status', 0);
    $this->db->where('ID_PANTAU', $id); 
    $this->db->update('db_rekammedis.tb_pemantauan_detail');

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

  public function hapusPantauRJ()
  {
    $this->db->trans_begin();
    $post = $this->input->post();
    $id =$post['ID'];
    
    // echo "<pre>";print_r($id);exit();
    $dataUpdate = array (
      'STATUS'                           => 0,
      'JENIS_PANTAU'  =>0,
    );
   
    $this->db->where('ID', $id);
    $this->db->update('db_rekammedis.tb_pemantauan', $dataUpdate);

    $this->db->set('status', 0);
    $this->db->where('ID_PANTAU', $id); 
    $this->db->update('db_rekammedis.tb_pemantauan_detail');
    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

  public function modalPantauRJX()
  {
    // $id = $this->input->post('id');
    // $dataEwork = $this->Sirs_model->get_ework_id($id)->row_array();
  
    // $data = array(
    //   'id' => $id,
    //   'dataEwork' => $dataEwork,
    // //   'dataPeserta' => $this->Sutu_model->hitung_peserta($id)->num_rows(),
    // );
    $this->load->view('Pemantauan/modal_pantau_rj');
  }

  public function modalPantauRJ()
  {
    $id = $this->input->post('id');
    $jenis = $this->input->post('jenis');
    $dataPantauRJ = $this->ModelPemantauan->dataPantauRJ($id)->row_array();
    $dataParentRJ = $this->ModelPemantauan->getDataParentRJ($id)->result_array();
    // $dataTable = array();
    // for ($i = 1; $i <= 6; $i++) {
    //     $dataTable[$i] = $this->ModelPemantauan->getdataFormPantauRJl($i, $id)->result_array();
    // }

    $dataTable1 = $this->ModelPemantauan->getdataFormPantauRJl(1,$id)->result_array();
    $dataTable2 = $this->ModelPemantauan->getdataFormPantauRJl(2,$id)->result_array();
    $dataTable3 = $this->ModelPemantauan->getdataFormPantauRJl(3,$id)->result_array();
    $dataTable4 = $this->ModelPemantauan->getdataFormPantauRJl(4,$id)->result_array();
    $dataTable5 = $this->ModelPemantauan->getdataFormPantauRJl(5,$id)->result_array();
    $dataTable6 = $this->ModelPemantauan->getdataFormPantauRJl(6,$id)->result_array();

    // $radio1 =  '<div class="form-check form-check-inline">
    //                 <input class="form-check-input" type="radio" data-id="'.$field->ID_DETAIL.'" name="exampleRadios'.$field->ID_DETAIL.'" id="exampleRadios1_'.$field->ID_DETAIL.'" value="option1" '.($ceklis == '1' ? 'checked' : '').'>
    //                 <label class="form-check-label" for="exampleRadios1_'.$field->ID_DETAIL.'">Ada</label>
    //                 </div>
    //                 <div class="form-check form-check-inline">
    //                 <input class="form-check-input" type="radio" data-id="'.$field->ID_DETAIL.'" name="exampleRadios'.$field->ID_DETAIL.'" id="exampleRadios2_'.$field->ID_DETAIL.'" value="option2" '.($ceklis == '2' ? 'checked' : '').'>
    //                 <label class="form-check-label" for="exampleRadios2_'.$field->ID_DETAIL.'">Tidak Ada</label>
    //                 </div>
    //                 <div class="form-check form-check-inline">
    //                 <input class="form-check-input" type="radio" data-id="'.$field->ID_DETAIL.'" name="exampleRadios'.$field->ID_DETAIL.'" id="exampleRadios3_'.$field->ID_DETAIL.'" value="option3" '.($ceklis == '3' ? 'checked' : '').'>
    //                 <label class="form-check-label" for="exampleRadios3_'.$field->ID_DETAIL.'">Tidak Diperlukan</label>
    //                 </div>';    

    $data = array(
      'id' => $id,
      'dataPantauRJ' => $dataPantauRJ,
      'dataParentRJ' => $dataParentRJ,
      'dataTable1' => $dataTable1,
      'dataTable2' => $dataTable2,
      'dataTable3' => $dataTable3,
      'dataTable4' => $dataTable4,
      'dataTable5' => $dataTable5,
      'dataTable6' => $dataTable6,
      'jenis' => $jenis
    );
    // $msg=[
    //   'sukses'=>$this->load->view('Pemantauan/modal_pantau_rj', $data, true)
    // ];
    // echo json_encode($msg);
  
    $this->load->view('Pemantauan/modal_pantau_rj', $data);
  }

  public function modalPantauRI()
  {
    $id = $this->input->post('id');
    $jenis = $this->input->post('jenis');
    $dataPantauRJ = $this->ModelPemantauan->dataPantauRJ($id)->row_array();    
    $dataTables = [];
    for ($i = 1; $i <= 26; $i++) {
        $dataTables[$i] = $this->ModelPemantauan->getdataFormPantauRJl($i, $id)->result_array();
    }

  
    $data = array(
      'id' => $id,
      'jenis' => $jenis,
      'dataPantauRJ' => $dataPantauRJ,
    );
    for ($i = 1; $i <= 26; $i++) {
      $data["dataTable$i"] = $dataTables[$i];
    } 
  
    // $this->load->view('Inventory/v_editBMN_modal', $data);
    $this->load->view('Pemantauan/modal_pantau_ri', $data);
  }

public function dataFormPantauRJ()
{
    $id = $this->input->post('ID');
    $listdata = $this->ModelPemantauan->getdataFormPantauRJ($id);

    $data = array();
    $no = 1;

    foreach ($listdata->result() as $field) {
        $ceklis = $field->STATUS;

        $radio =  '<div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" data-id="'.$field->ID_DETAIL.'" name="exampleRadios'.$field->ID_DETAIL.'" id="exampleRadios1_'.$field->ID_DETAIL.'" value="option1" '.($ceklis == '1' ? 'checked' : '').'>
                    <label class="form-check-label" for="exampleRadios1_'.$field->ID_DETAIL.'">Ada</label>
                    </div>
                    <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" data-id="'.$field->ID_DETAIL.'" name="exampleRadios'.$field->ID_DETAIL.'" id="exampleRadios2_'.$field->ID_DETAIL.'" value="option2" '.($ceklis == '2' ? 'checked' : '').'>
                    <label class="form-check-label" for="exampleRadios2_'.$field->ID_DETAIL.'">Tidak Ada</label>
                    </div>
                    <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" data-id="'.$field->ID_DETAIL.'" name="exampleRadios'.$field->ID_DETAIL.'" id="exampleRadios3_'.$field->ID_DETAIL.'" value="option3" '.($ceklis == '3' ? 'checked' : '').'>
                    <label class="form-check-label" for="exampleRadios3_'.$field->ID_DETAIL.'">Tidak Diperlukan</label>
                    </div>';

        $data[] = array(
            $no,
            $field->DESKRIPSI,
            $radio
        );

        $no++;
    }

    $output = array(
        "data" => $data
    );
    echo json_encode($output);
}

public function simpanCeklisPantauRJ()
{
    $this->db->trans_begin();
    $post = $this->input->post();
    $id = $post['id'];
    $value = $post['value'];

    // Tentukan STATUS berdasarkan nilai radio button yang dipilih
    if ($value == 'option1') {
        $dataUpdate = array(
            'STATUS' => 1,
        );
    } elseif ($value == 'option2') {
        $dataUpdate = array(
            'STATUS' => 2,
        );
    } elseif ($value == 'option3') {
        $dataUpdate = array(
            'STATUS' => 3,
        );
    }

    // Update STATUS di database
    $this->db->where('ID', $id);
    $this->db->update('db_rekammedis.tb_pemantauan_detail', $dataUpdate);

    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
    } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
    }

    echo json_encode($result);
}

public function simpanCeklisPantauRI()
{
    $this->db->trans_begin();
    $post = $this->input->post();
    $id = $post['id'];
    $value = $post['value'];

    // Tentukan STATUS berdasarkan nilai radio button yang dipilih
    if ($value == 'option1') {
        $dataUpdate = array(
            'STATUS' => 1,
        );
    } elseif ($value == 'option2') {
        $dataUpdate = array(
            'STATUS' => 2,
        );
    } elseif ($value == 'option3') {
        $dataUpdate = array(
            'STATUS' => 3,
        );
    }

    // Update STATUS di database
    $this->db->where('ID', $id);
    $this->db->update('db_rekammedis.tb_pemantauan_detail', $dataUpdate);

    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
    } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
    }

    echo json_encode($result);
}

public function dataFormPantauRJ1()
{
    $id = $this->input->post('ID');
    $listdata = $this->ModelPemantauan->getdataFormPantauRJ1($id);
    $data = array();
    $no = 1;
    foreach ($listdata->result() as $field) {
      $ceklis = $field->STATUS;
      $radio =  '<div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" data-id="'.$field->ID_DETAIL.'" name="exampleRadios'.$field->ID_DETAIL.'" id="exampleRadios1_'.$field->ID_DETAIL.'" value="option1" '.($ceklis == '1' ? 'checked' : '').'>
                    <label class="form-check-label" for="exampleRadios1_'.$field->ID_DETAIL.'">Ada</label>
                    </div>
                    <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" data-id="'.$field->ID_DETAIL.'" name="exampleRadios'.$field->ID_DETAIL.'" id="exampleRadios2_'.$field->ID_DETAIL.'" value="option2" '.($ceklis == '2' ? 'checked' : '').'>
                    <label class="form-check-label" for="exampleRadios2_'.$field->ID_DETAIL.'">Tidak Ada</label>
                    </div>
                    <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" data-id="'.$field->ID_DETAIL.'" name="exampleRadios'.$field->ID_DETAIL.'" id="exampleRadios3_'.$field->ID_DETAIL.'" value="option3" '.($ceklis == '3' ? 'checked' : '').'>
                    <label class="form-check-label" for="exampleRadios3_'.$field->ID_DETAIL.'">Tidak Diperlukan</label>
                    </div>';
        $data[] = array(
            $no,
            $field->DESKRIPSI,
            $radio
        );
        $no++;
    }
    $output = array(
        "data" => $data
    );
    echo json_encode($output);
}

}
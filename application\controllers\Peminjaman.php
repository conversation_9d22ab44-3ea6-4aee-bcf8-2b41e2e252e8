<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Peminjaman extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if($this->session->userdata('logged_in') == FALSE ){
      redirect('login');  
    }
    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('ModelPeminjaman'));
    $this->load->model(array('ModelPengembalian'));
  }
  public function indexPinjamAwal(){    
    $data=array(   
      'isi'            => 'Peminjaman/IndexPinjamHome'
    );
    $this->load->view('Layout/Wrapper',$data);
  }  
  public function indexpinjam(){
    $this->load->view('Peminjaman/IndexPinjam');
  }
  
  public function indexpinjamh(){
    $this->load->view('Peminjaman/IndexPinjamHistory');
  }
  public function indexpinjamf(){
    $data=array(      
      'getPinjamF'        => $this->ModelPeminjaman->tblPinjamFarmasi(),
    );
    $this->load->view('Peminjaman/IndexPinjamFarmasi',$data, FALSE);
  }
  public function addPinjamFarmasi(){
    $this->db->trans_begin();    
    $tgl_tarik = date('Y-m-d H:i:s');
    $TANGGAL = $this->input->post('TANGGAL');
    $PETUGAS = $this->input->post('petugas');
    $getData = $this->ModelPeminjaman->TarikPerjanjianFarmasi($TANGGAL)->result_array();

   
    if (empty($getData)) {
        $result = array('status' => 'failed', 'message' => 'Tidak ada data perjanjian Ke Farmasi untuk tanggal tersebut.');
        echo json_encode($result);
        return;
    }

    $dataPerjanjian = array();
    
    foreach ($getData as $row) {
        $this->db->where('ID_PERJANJIAN', $row['ID']);
        $exists = $this->db->get('db_rekammedis.tb_pendistribusian_perjanjian')->num_rows();

        if ($exists > 0) {
            $result = array('status' => 'failed', 'message' => 'Data perjanjian dengan ID ' . $row['ID'] . ' sudah ada.');
            echo json_encode($result);
            return;
        }
        $dataPerjanjian[] = array(
            'ID_PERJANJIAN' => $row['ID'],
            'ID_CONTER' => NULL,
            'ID_PETUGAS' => $PETUGAS,
            'TGL_INPUT' => $tgl_tarik,
            'JUMLAH_RM' => NULL,
            'STATUS' => 3,
        );
    }

    $this->db->insert_batch('db_rekammedis.tb_pendistribusian_perjanjian', $dataPerjanjian);
    
    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed', 'message' => 'Proses penyimpanan data gagal, silakan coba lagi');
    } else {
        $this->db->trans_commit();
        $result = array('status' => 'success', 'message' => 'Proses penyimpanan data berhasil dilakukan');
    }
    
    echo json_encode($result);
  }

  public function indexPeneliti(){    
    $data=array(   
      'isi'            => 'Peminjaman/indexPenelitiHome'
    );
    $this->load->view('Layout/Wrapper',$data);
  }  
  public function indexPenelitiTrans(){
    $this->load->view('Peminjaman/IndexPeneliti');
  }
  
  public function indexPenelitiHis(){
    $this->load->view('Peminjaman/indexPenelitiHistory');
  }
  public function listPeneliti(){
    $result = $this->ModelPeminjaman->cariPeneliti();
    $data = array();
    foreach ($result as $row) {
        $sub_array = array();
        $sub_array['id'] = $row['nik'];
        $sub_array['text'] = $row['nik'] . ' - ' . $row['nama'];
        $data[] = $sub_array;
    }
    echo json_encode($data);
  }

  public function listPasien(){
      $result = $this->ModelPeminjaman->cariPasien(); 
      $data = array();
      foreach ($result as $row) {
          $sub_array = array();
          $sub_array['id'] = $row['NORM'];
          $sub_array['text'] =  ' [ ' .$row['NORM'] . ' ] '.$row['NAMA']; 
          $data[] = $sub_array;
      }
      echo json_encode($data);
  }
  public function simpanPinjamPeneliti(){
    $this->db->trans_begin();    
    $dataUtama = array(
        'tglpinjam'     => $this->input->post('tgl'),
        'nik_peneliti'  => $this->input->post('nik'),
        'oleh'          => $this->input->post('petugas'),            
        'status'        => 1,
        
    );
    $this->db->insert('db_rekammedis.tb_peminjaman', $dataUtama);

    $insert_id = $this->db->insert_id();
    $norm = $this->input->post('norm');    
    $dataDetailToInsert = array();
    foreach ($norm as $key) {
        $dataDetailToInsert[] = array(
            'nopinjam' => $insert_id,
            'nomr' => $key,
            'tglkembali' => null,
            'status' => 1,
        );
    }
    $this->db->insert_batch('db_rekammedis.tb_peminjaman_detail', $dataDetailToInsert);
    $this->db->trans_complete();
    if ($this->db->trans_status() === FALSE) {
        $this->db->trans_rollback();
        $response = array('status' => 'error', 'message' => 'Database transaction failed.');
    } else {
        $this->db->trans_commit();
        $response = array('status' => 'success');
    }      
    echo json_encode($response);
  }
  public function getTblPeneliti(){
    // $id=1;
    $listdata = $this->ModelPeminjaman->tampilTblPeneliti();
    $data=array();
    $no =1;
    foreach ($listdata->result() as $field) { 
      $button = '<button type="button" class="btn btn-primary btn-rounded btn-sm" title="Pengembalian Form" onclick="addKembali('.$field->nopinjam.')" style="width:72px"><i class="fas fa-sync-alt"></i></button>';
      $hapus = '<button type="button" class="btn btn-danger btn-rounded btn-sm hapusPinjam" onclick="hapusPinjam('.$field->nopinjam.',\''.$field->nama.'\',\''.date('d F Y', strtotime($field->tglpinjam)).'\')" id="hapusPinjam" title="Hapus Peminjaman" style="width:72px"><i class="fa fa-trash"></i></button>';
      $info = '<a href="javascript:void(0);" onclick="tampilInfo('.$field->nik_peneliti.')" title="Informasi Peminjaman Kembali">
      <i class="fa fa-info-circle"></i>
      </a>';
      $data[] = array(
        $no,
        date_format(date_create($field->tglpinjam),'d/m/Y'),
        $field->nik_peneliti. ' ' . $info,
        $field->nama,
        $field->nama_lengkap,
        $field->status == 1? 'DIPINJAM' : 'KEMBALI',
        $button.'.'.$hapus

      );
      $no++;

    }

    $output = array(
      "data"            => $data
    );
    echo json_encode($output);
  }
  public function modalDetailPeneliti() {
    $id = $this->input->post('ID');  
    $judul = $this->ModelPeminjaman->listDetailPeneliti($id);

    if ($judul->num_rows() > 0) {
        $row = $judul->row_array();
        $data = [
            'nik'     => !empty($row['nik']) ? $row['nik'] : '',
            'nama'    => !empty($row['nama']) ? $row['nama'] : '',
            'alamat'  => !empty($row['alamat']) ? $row['alamat'] : '',
            'notelp'  => !empty($row['notelp']) ? $row['notelp'] : '',
            'email'   => !empty($row['email']) ? $row['email'] : '',
            'petugas' => !empty($row['nama_lengkap']) ? $row['nama_lengkap'] : '',
            // 'nopinjam'  => !empty($row['nopinjam']) ? $row['nopinjam'] : '',
            'status' => !empty($row['status']) ? $row['status'] : '',
        ];

        $msg = [
            'sukses' => $this->load->view('Peminjaman/IndexPenelitiDetail', $data, true)
        ];
    } else {
        // Jika tidak ada data
        $msg = [
            'sukses' => '<p>Data tidak ditemukan.</p>'
        ];
    }

    echo json_encode($msg);    
  }
  public function tblDetailPeneliti(){
    $id = $this->input->post('id');  
    // var_dump($id);
    // exit();
    $listdata = $this->ModelPeminjaman->listDetPeneliti($id);
    $data=array();
    $no =1;
    foreach ($listdata->result() as $field) {
      $data[] = array(
        $no,
        date_format(date_create($field->tglpinjam),'d/m/Y'),
        date_format(date_create($field->tglkembali),'d/m/Y H:i:s'),
        $field->nomr,
        $field->petugas,
        $field->nama_lengkap        
        );
        
      $no++;
    }

    $output = array(
      "data"            => $data
    );
    echo json_encode($output);
  }
  public function modalPenelitiKembali() {
    $id = $this->input->post('id'); 
    $judul = $this->ModelPeminjaman->listDetailPinjamPeneliti($id);

    $data = []; // Inisialisasi $data terlebih dahulu

    if ($judul) { // Cukup memeriksa $judul
        $row = $judul->row_array();
        $data = [
            'nik'      => !empty($row['nik']) ? $row['nik'] : '',
            'nama'     => !empty($row['nama']) ? $row['nama'] : '',
            'nopinjam' => !empty($row['nopinjam']) ? $row['nopinjam'] : '',
            'tglpinjam'=> !empty($row['tglpinjam']) ? date_format(date_create($row['tglpinjam']),'d F Y'): '',
        ];
    }

    // Pastikan view masih bisa di-load meski $data kosong
    $msg = [
        'sukses' => $this->load->view('Peminjaman/IndexPenelitiKembali', $data, true)
    ];

    echo json_encode($msg);    
  }
  public function getPenelitiPinjam() {
    $no = $this->input->post('nopinjam'); 
    $listresult = $this->ModelPeminjaman->listPenelitiPinjam($no); 
    $data = array();
    $no = 1;
    foreach ($listresult as $field) {
        if (is_array($field)) {
            if ($field['status'] == '1') {
                $status = 'belum';
                $cek = '<input type="checkbox" data-id="'.$field['id_detpinjam'].'" value="'.$field['status'].'" class="cek_done">';
            } else {
                $status = 'kembali';
                $cek = '<input type="checkbox" data-id="'.$field['id_detpinjam'].'" value="'.$field['status'].'" class="cek_done" checked>';
            }
            $tglKembali = !empty($field['tglkembali']) ? date_format(date_create($field['tglkembali']), 'd/m/Y H:i:s') : " ";

           
            $data[] = array(
                $no,
                $field['nomr'],
                $field['namaPasien'],
                $status,
                $tglKembali,
                empty($field['olehK']) ? "" : $field['nama_lengkap'],
                $cek,
            );
            $no++;
        }
    }

    $output = array(
        "data" => $data
    );

    echo json_encode($output); 
  }  
  public function UpdateCek(){
    $this->db->trans_start();
    $id = $this->input->post('iddet');
    $value =$this->input->post('value');

    if($value==1){
    $dataUpdate = array (            
        'tglkembali'                       =>date('Y-m-d H:i:s'),
        'olehK'                            =>$this->session->userdata('id'), 
        'status'                           => 2,            
        );
    } else if($value==2){
    $dataUpdate = array (
        'status'                           => 1,
        'olehK'                            =>NULL,
        'tglkembali'                       =>NULL,
        );
    }
        $this->db->where('id_detpinjam', $id);
        $this->db->update('db_rekammedis.tb_peminjaman_detail', $dataUpdate);

    if ($this->db->trans_status() === false) {
    $this->db->trans_rollback();
    $result = array('status' => 'failed');
    } else {
    $this->db->trans_commit();
    $result = array('status' => 'success');
    }
    echo json_encode($result);
  } 
  public function updatePeminjamanStatus() {
    $this->db->trans_start();

    $nopinjam = $this->input->post('nopinjam');

    $this->db->set('status', 2);
    $this->db->where('nopinjam', $nopinjam); 
    $this->db->update('db_rekammedis.tb_peminjaman');

    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
    } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
    }

    echo json_encode($result);
  }
  public function hapusPenelitiPinjam(){
    $id = $this->input->post('id');
    $this->db->set('status', 0);
    $this->db->where('nopinjam', $id); 
    $this->db->update('db_rekammedis.tb_peminjaman'); 

    $this->db->set('status', 0);
    $this->db->where('nopinjam', $id); 
    $this->db->update('db_rekammedis.tb_peminjaman_detail'); 

    $response = array('status' => 'success');
    echo json_encode($response);
  }

  public function tblHistoryPinjamPeneliti(){
    $listdata = $this->ModelPeminjaman->tampilHistoryTblPeneliti();
    $data=array();
    $no =1;
    $STATUS ='KEMBALI';
    foreach ($listdata->result() as $field) { 
      $info = '<a href="javascript:void(0);" onclick="tampilInfoHis('.$field->nik.')" title="Informasi Peminjaman">
      <i class="fa fa-info-circle"></i>
      </a>';
      $data[] = array(
        $no,
        $field->nik. ' ' . $info,
        $field->nama,
        $field->nomr,
        $STATUS,
       

      );
      $no++;

    }

    $output = array(
      "data"            => $data
    );
    echo json_encode($output);
  }
  public function modalDetailPenelitiHis() {
    $id = $this->input->post('ID');  
    $judul = $this->ModelPeminjaman->listDetailPeneliti($id);

    if ($judul->num_rows() > 0) {
        $row = $judul->row_array();
        $tblHisData = $this->ModelPeminjaman->listDetPeneliti($id);
        $tblHis = [];
        if ($tblHisData->num_rows() > 0) {
            $tblHis = $tblHisData->result_array();
        }
        $data = [
            'nik'     => !empty($row['nik']) ? $row['nik'] : '',
            'nama'    => !empty($row['nama']) ? $row['nama'] : '',
            'alamat'  => !empty($row['alamat']) ? $row['alamat'] : '',
            'notelp'  => !empty($row['notelp']) ? $row['notelp'] : '',
            'email'   => !empty($row['email']) ? $row['email'] : '',
            'status'  => !empty($row['status']) ? $row['status'] : '',
            'tblHis'  => $tblHis,
        ];

        $msg = [
            'sukses' => $this->load->view('Peminjaman/IndexPenelitiDetailHis', $data, true)
        ];
    } else {
        // Jika tidak ada data
        $msg = [
            'sukses' => '<p>Data tidak ditemukan.</p>'
        ];
    }

    echo json_encode($msg);    
  }

  public function getHistoryPinjam(){
    $start = $this->input->post('start'); 
    $length = $this->input->post('length'); 
    $search = $this->input->post('search')['value']; 

    $listdata = $this->ModelPeminjaman->dataHistoryPinjam($start, $length, $search);
    // $QUERY = $this->db->last_query();
    // print_r($QUERY);
    // exit();
    $data=array();
    $no =1;
    foreach ($listdata['data'] as $field) {
      $data[] = array(
        $no,
        $field->TGL_KEMBALI,
        $field->NOMR,
        $field->NAMAPASIEN,
        $field->NAMADOKTER,
        $field->NAMARUANG,
        $field->NAMACONTER !== NULL ? $field->NAMACONTER : '-',
        $field->nama_lengkap,
        $field->DESKRIPSI !== NULL ? $field->DESKRIPSI : '-'
      );
      $no++;

    }

    $output = array(
      "draw" => $this->input->post('draw'),
      "recordsTotal" => $listdata['recordsTotal'], 
      "recordsFiltered" => $listdata['recordsFiltered'], 
      "data"            => $data
    );
    echo json_encode($output);
  }

  // public function listPerjanjian(){
  //   $term = $this->input->post('term');
  //   $result = $this->ModelPeminjaman->pemiByPerjanjian($term);
  //   $data = array();
  //   foreach ($result as $row) {
  //       $sub_array = array();
  //       $sub_array['id'] = $row['TANGGAL'];
  //       $sub_array['text'] = date('d-m-Y', strtotime($row['TANGGAL']));
  //       $data[] = $sub_array;
  //   }
  //   echo json_encode($data); 
  // }
  public function addPinjamPerjanjian(){
    $this->db->trans_begin();    
    $tgl_tarik = date('Y-m-d H:i:s');
    $TANGGAL = $this->input->post('TANGGAL');
    $PETUGAS = $this->input->post('petugas');
    $getData = $this->ModelPeminjaman->TarikDataPerjanjian($TANGGAL)->result_array();

    if (empty($getData)) {
      // Jika data yang ditarik kosong, batalkan proses dan kirimkan pesan
      $result = array('status' => 'failed', 'message' => 'Tidak ada data perjanjian untuk tanggal tersebut.');
      echo json_encode($result);
      return;
  }
    $dataPerjanjian = array();
    

    foreach ($getData as $row) {
      $this->db->where('ID_PERJANJIAN', $row['ID']);
      $exists = $this->db->get('db_rekammedis.tb_pendistribusian_perjanjian')->num_rows();

      if ($exists > 0) {
          $result = array('status' => 'failed', 'message' => 'Data perjanjian dengan ID ' . $row['ID'] . ' sudah ada.');
          echo json_encode($result);
          return;
      }
        $dataPerjanjian[] = array(
            'ID_PERJANJIAN' => $row['ID'],
            // 'TGL_PERJANJIAN' => $row['TANGGAL'],
            'ID_CONTER' => NULL,
            'ID_PETUGAS' => $PETUGAS,
            'TGL_INPUT' => $tgl_tarik,
            'JUMLAH_RM' => NULL,
            'STATUS' => 1,

        );
    }

    $this->db->insert_batch('db_rekammedis.tb_pendistribusian_perjanjian', $dataPerjanjian);
    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed','message' => 'Proses penyimpanan data gagal, silakan coba lagi');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success', 'message' => 'Proses penyimpanan data berhasil dilakukan');
    }    
  echo json_encode($result);

  }
  public function tabelData(){  
      $getPinjam = $this->ModelPeminjaman->tblPinjamPerjanjian();
      $data = [
          'getPinjam' => $getPinjam,
      ];
      $table_html = $this->load->view('Peminjaman/IndexPinjamTabel', $data, true);
      $msg = [
          'data' => $table_html
      ];
      echo json_encode($msg);
  }
  public function modalDetail(){
    $id = $this->input->post('ID');  
    $judul = $this->ModelPeminjaman->getModalDetail($id);
    // $detail = $this->ModelPeminjaman->getDetailPemindahan($id);

    if ($judul->num_rows() > 0) {
      $row = $judul->row_array();
      $data = [
        'ID'           => $row['IDPINJAM'],
        'TANGGAL'      => $row['TANGGAL'],
        'NAMAP'        => $row['nama_lengkap'],
        'NOMR'         => $row['NOMR'],
        'NAMAPASIEN'   => $row['NAMAPASIEN'],
        'TGL_INPUT'    => $row['TGL_INPUT'],
        'has_trans'    => $row['has_trans'],
        // 'detail'       => $detail->result_array() 
      ];
    }  
    $msg=[
      'sukses'=>$this->load->view('Peminjaman/IndexPinjamDetail', $data, true)
    ];
    echo json_encode($msg);    

  }

  public function tblTransfer(){
    $id = $this->input->post('id');
    $listdata = $this->ModelPeminjaman->dataTblTrans($id);
    $data=array();
    $no =1;
    foreach ($listdata->result() as $field) {
      $data[] = array(
        $no,
        date_format(date_create($field->TGL_TRANS),'d-m-Y H:i:s'),
        $field->nama_lengkap,
        $field->NAMARUANG,
        $field->CONTER ? $field->CONTER : '-',
        $field->KETERANGAN
        );
        
      $no++;
    }

    $output = array(
      "data"            => $data
    );
    echo json_encode($output);

  }


  
  public function simpanUpdate(){
    $this->db->trans_begin();

    $id = $this->input->post('ID');
                
    $data_insert = array (      
      'ID_CONTER'       => $this->input->post('IDCOnter'),
      'JUMLAH_RM'       => $this->input->post('JUMRM'),
    );
    $this->db->where('ID', $id);
    $this->db->update('db_rekammedis.tb_pendistribusian_perjanjian', $data_insert);

    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
      } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
      }

    echo json_encode($result);
  }
  
  public function update_data(){
    $id = $this->input->post('id');
    $column_name = $this->input->post('column');
    $value = $this->input->post('value');

    $update_data = array($column_name => $value);
    $this->db->where('db_rekammedis.tb_pendistribusian_perjanjian.ID', $id);
    $this->db->update('db_rekammedis.tb_pendistribusian_perjanjian', $update_data);

    echo json_encode(array('success' => true));
  }
  public function modalKembaliPerjanjian(){
    $id = $this->input->post('id');  
    $judul = $this->ModelPeminjaman->getModalKembaliPerjanjian($id);
    if ($judul->num_rows() > 0) {
      $row = $judul->row_array();
      $conter = ($row['ID_CONTER'] !== NULL) ? $row['NAMACONTER'] : '-';
      $data = [
        'ID'           => $row['ID'],
        'NORM'         => $row['NOMR'],
        'NAMAPASIEN'   => $row['NAMAPASIEN'],
        'NAMADOKTER'   => $row['NAMADOKTER'],
        'TGL'          => $row['TGL_INPUT'],
        'RUANGAN'      => $row['NAMARUANG'],
        'CONTER'       => $conter 
      ];
    }  
    $msg=[
      'sukses'=>$this->load->view('Peminjaman/indexPinjamKembali', $data, true)
    ];
    echo json_encode($msg);    
  }
  public function getListLembar()
	{
    $formulir = $this->input->post('id');
    $listCeklis = $this->ModelPeminjaman->listlembar($formulir);  
    echo json_encode($listCeklis);
    // $nomr = $this->input->post('NOMR'); 
    //   $term = $this->input->post('term');
    //   $result = $this->ModelReview->getTglDatangOptions($nomr,$term);
    //   $data = array();
    //   foreach ($result as $row) {
    //       $sub_array = array();
    //       $sub_array['id'] = $row['NOMOR'];
    //       $sub_array['text'] = date('d-F-Y H:i:s', strtotime($row['TANGGAL']));
    //       $data[] = $sub_array;
    //   }
    //   echo json_encode($data); 
	}
  public function simpanKembali(){
    $this->db->trans_begin();
    $tglcreate = date('Y-m-d H:i:s');

    $id = $this->input->post('ID');
    $status = $this->input->post('status');

   

  if ($status == '1') {
    $data_trans = array(
        'ID_PERPEN' => $id,
        'TGL_TRANS' => $tglcreate,
        'ID_PETUGAS' => $this->session->userdata('id'),
        'ID_RUANGAN' => $this->input->post('ruang'),
        'ID_CONTER' => $this->input->post('conter') ? $this->input->post('conter') : null,
        'KETERANGAN' => $this->input->post('ket')
    );
    $this->db->insert('db_rekammedis.tb_pendistribusian_perjanjian_trans', $data_trans);

  } else if ($status == '2') {
      $data_edit = array(
        'STATUS' => 2,
        'ID_KEMBALI' => $this->session->userdata('id'),
        'TGL_KEMBALI' => $tglcreate,
        'JENIS_FORMULIR'=> $this->input->post('formulir')
      );
      $this->db->where('ID', $id);
      $this->db->update('db_rekammedis.tb_pendistribusian_perjanjian', $data_edit);

      $lembar_array = $this->input->post('lembar');
      if (!empty($lembar_array) && is_array($lembar_array)) {
          foreach ($lembar_array as $lembar) {
              if (!empty($lembar)) {
                  $data_detail_insert = array(
                      'ID_PERPEN' => $id,
                      'ID_LEMBAR' => $lembar
                  );
                  $this->db->insert('db_rekammedis.tb_pendistribusian_perjanjian_det', $data_detail_insert);
              }
          }
      }
  }


    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
    } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
    }

    echo json_encode($result);
 }


  // public function addPinjamPerjanjian2(){
  //   $listdata = $this->modelPeminjaman->tblPinjamPerjanjian();
  //   $data=array();
  //   $no =1;
  //   foreach ($listdata->result() as $field) {
  //     if ($field->STATUS == 1) {
  //       $button = '<button type="button" class="btn btn-primary btn-rounded btn-sm" title="Checklist Form" onclick="addRev('.$field->ID.')" style="width:72px"><i class="fas fa-tasks"></i></i></button>';         
  //     } elseif ($field->STATUS == 2) {
  //       $button = '<button type="button" class="btn btn-primary btn-rounded btn-sm" title="edit data" onclick="editRev('.$field->ID.')" style="width:72px"><i class="fa fa-edit"></i></button>';
  //       // $edit = '<button type="button" class="btn btn-primary btn-rounded btn-sm" title="edit data" onclick="editRev('.$field->ID.')" style="width:72px"><i class="fa fa-edit"></i></button>';
  //     } 
  //     // $hapus = '<button type="button" class="btn btn-danger btn-rounded btn-sm hapusKuan" href="javaScript:;" id="hapusKuan" title="hapusRev" data="'.$field->ID.'" style="width:72px"><i class="fa fa-trash"></i></button>';
  //     $hapus = '<button type="button" class="btn btn-danger btn-rounded btn-sm hapusReview" onclick="hapusReview('.$field->ID.',\''.date('d F Y H:i:s', strtotime($field->TGL_MASUK)).'\',\''.date('d F Y H:i:s', strtotime($field->TGL_KELUAR)).'\')" id="hapusReview" title="hapusRev" style="width:72px"><i class="fa fa-trash"></i></button>';

  //     $tgl_masuk = date('d-F-Y H:i:s', strtotime($field->TGL_MASUK));
  //     $tgl_keluar = date('d-F-Y H:i:s', strtotime($field->TGL_KELUAR));
  //     $tgl_input = date('d-F-Y H:i:s', strtotime($field->TGL_INPUT));
  //     $tgl_rev = ($field->TGL_REVIEW != null) ? date('d-F-Y H:i:s', strtotime($field->TGL_REVIEW)) : " - ";
  //     $data[] = array(
  //       $no,
  //       $tgl_input,
  //       $field->NAMA_INPUT,
  //       $field->NORM,
  //       $field->NAMAPASIEN,
  //       $tgl_masuk,
  //       $tgl_keluar,
  //       $field->NAMADOK,
  //       $field->STATUS_REVIEW,
  //       $tgl_rev,
  //       $button.'.'.$hapus
  //       );
        
  //     $no++;
  //   }

  //   $output = array(
  //     "data"            => $data
  //   );
  //   echo json_encode($output);
  // }


  // -BATAS PEMINJAMAN PERJANJIAN sini

  public function index()
  {
    $this->load->view('Peminjaman/Index');
  }
  public function indexAwal(){
    $data=array(      
      'isi'            => 'Peminjaman/IndexHome'
    );
    $this->load->view('Layout/Wrapper',$data);

  }

  public function index_inaktif()
  {
    $data=array(      
      // 'listkat'        => $this->ModelInput->tampilKategori(),
      // 'listperusahaan' => $this->ModelInput->listPerusahaan(),
      // 'listno'         => $this->ModelInput->listnoPks(),
      'isi'            => 'Peminjaman/Inaktif'
    );
    $this->load->view('Layout/Wrapper',$data);
  }
  public function CekNomrPinjam()
  {
      $nomr = $this->input->post('NOMR');    
      $cekmr = $this->ModelPeminjaman->cekNomrP($nomr);
      
      if ($cekmr->num_rows() > 0) {
          $result = $cekmr->row_array();
          $row = array(
              'nomr' => $result['NORM'],
              'namapasien' => $result['NAMAPASIEN'],
              'namadokter' => $result['NAMADOKTER'],
              'tgl' => $result['TANGGAL'],
              'nip' => $result['NIP'],
              'ruangan' => $result['DESKRIPSI'],
              'idruang' => $result['IDRUANG'],
          );
      } else {
          $row = array(
              'nomr' => '',
              'namapasien' => 'Data tidak ditemukan',
              'namadokter' => 'Data tidak ditemukan',
              'ruangan' => 'Data tidak ditemukan',
              'tgl' => 'Data tidak ditemukan',              
          );
      }
  
      echo json_encode($row);
  }
  public function list_conter(){
    $result = $this->ModelPeminjaman->getConter(); 
    $data = array();
      foreach ($result as $row) {
          $sub_array = array();
          $sub_array['id'] = $row['ID'];
          $sub_array['text'] = $row['DESKRIPSI'];
          $data[] = $sub_array;
      }
      
    echo json_encode($data); 
  }
  
  public function modalKembali(){
    $id = $this->input->post('id');  
    $judul = $this->ModelPeminjaman->getModalKembali($id);
    if ($judul->num_rows() > 0) {
      $row = $judul->row_array();
      $conter = ($row['CONTER'] !== NULL) ? $row['CONTER'] : '-';
      $data = [
        'ID'           => $row['ID'],
        'NORM'         => $row['NOMR'],
        'NAMAPASIEN'   => $row['NAMAPASIEN'],
        'NAMADOKTER'   => $row['NAMADOKTER'],
        'TGL'          => $row['TANGGAL'],
        'RUANGAN'      => $row['RUANGAN'],
        'CONTER'       => $conter 
      ];
    }  
    $msg=[
      'sukses'=>$this->load->view('Peminjaman/index_modal', $data, true)
    ];
    echo json_encode($msg);
  }


  public function getListdata(){
    $listdata = $this->ModelPeminjaman->tampildata();
    $data=array();
    $no =1;
    foreach ($listdata->result() as $field) {
      // $tgl = date('d-F-Y H:i:s', strtotime($field->TANGGAL));
      // if ($field->STATUS == 1) {
        $button = '<button type="button" class="btn btn-primary btn-rounded btn-sm" title="Pengembalian Form" onclick="addKembali('.$field->IDPINJAM.')" style="width:72px"><i class="fas fa-sync-alt"></i></button>';
        $hapus = '<button type="button" class="btn btn-danger btn-rounded btn-sm hapusPinjam" onclick="hapusPinjam('.$field->IDPINJAM.',\''.$field->NOMR.'\',\''.date('d F Y H:i:s', strtotime($field->TANGGAL)).'\')" id="hapusPinjam" title="Hapus Peminjaman" style="width:72px"><i class="fa fa-trash"></i></button>';
      // } 
      // elseif ($field->STATUS == 2) {
        // $button = '<button type="button" class="btn btn-primary btn-rounded btn-sm" title="Edit Pengembalian" onclick="editKembali('.$field->IDPINJAM.')" style="width:72px"><i class="fa fa-edit"></i></button>';
        // $edit = '<button type="button" class="btn btn-primary btn-rounded btn-sm" title="edit data" onclick="editChecklist('.$field->ID.')" style="width:72px"><i class="fa fa-edit"></i></button>';
        // $hapus = '<button type="button" class="btn btn-danger btn-rounded btn-sm hapusKuan" onclick="hapusKuan('.$field->IDPINJAM.',\''.date('d F Y H:i:s', strtotime($field->NOMR)).'\',\''.date('d F Y H:i:s', strtotime($field->TANGGAL)).'\')" id="hapusKuan" title="hapusKuan" style="width:72px"><i class="fa fa-trash"></i></button>';
      // } 
      $data[] = array(
        $no,
        date_format(date_create($field->TANGGAL),'d-m-Y H:i:s'),
        $field->NOMR,
        $field->NAMAPASIEN,
        $field->NAMADOKTER,
        $field->DESKRIPSI,
        $field->CON ?: '-',
        $field->JUMLAH_RM,
        $field->nama_lengkap,
        $field->STATUS == 1? 'DIPINJAM' : 'KEMBALI',
        $button.'.'.$hapus

      );
      $no++;

    }

    $output = array(
      // "draw"            => $draw,
      // "recordsTotal"    => $listdata->num_rows(),
      // "recordsFiltered" => $listdata->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }
  public function hapusPinjam(){
    $id = $this->input->post('id');
    $this->db->set('status', 0);
    $this->db->where('ID', $id); 
    $this->db->update('db_rekammedis.tb_pendistribusian'); 

    // $this->db->set('status', 0);
    // $this->db->where('ID_ANALISIS', $id); 
    // $this->db->update('db_rekammedis.tb_analisis_detail'); 

    $response = array('status' => 'success');
    echo json_encode($response);
  }

  // public function dataInaktif()
  // {
  //   // $draw   = intval($this->input->POST("draw"));
  //   // $start  = intval($this->input->POST("start"));
  //   // $length = intval($this->input->POST("length"));

  //   $button = '<div class="btn-group">
  //                 <button type="button" class="btn btn-light dropdown-toggle waves-effect" data-toggle="dropdown" aria-expanded="false">
  //                     <i class="mdi mdi-folder font-18 vertical-middle"></i>
  //                     <i class="mdi mdi-chevron-down"></i>
  //                 </button>
  //                 <div class="dropdown-menu">
  //                     <a class="dropdown-item" href="javascript: void(0);">Detail</a>
  //                     <a class="dropdown-item" href="javascript: void(0);">Edit</a>
  //                     <a class="dropdown-item" href="javascript: void(0);">Hapus</a>
  //                 </div>
  //             </div>';



  //   $listdata = $this->ModelPeminjaman->dataListInaktif();
    
  //   $data=array();
  //   $no =1;
  //   foreach ($listdata->result() as $field) {

  //     $edit = '<button type="button" class="btn btn-primary btn-rounded btn-sm" title="edit data" onclick="editAss('.$field->ID.')" style="width:72px"><i class="fa fa-edit"></i> </button>';
  //     $button = '<button type="button" class="btn btn-primary btn-rounded btn-sm" data-toggle="modal" data-target="#modal_inaktif" data-id="'.$field->ID.'" style="width:72px"><i class="fas fa-edit"></i></button>';
  //     $hapus = '<button type="button" class="btn btn-danger btn-rounded btn-sm hapus_inaktif" href="javaScript:;" id="hapus_inaktif" title="hapus_inaktif" data="'.$field->ID.'" style="width:72px"><i class="fa fa-trash"></i></button>';
  //     $hapus2 ='<button type="button" class="btn btn-danger btn-rounded btn-sm" class="hapus_input" href="javaScript:;" data="'.$field->ID.'" id="hapus_ass"><i class="fa fa-trash"></i></button>';
  //     $edit2 = '<a href="#modalcek" class="btn btn-sm btn-primary" data-toggle="modal" data-id="'.$field->ID.'"><i class="fas fa-search"></i> Lihat</a>';

  //     $data[] = array(
  //       $no,

  //       $field->NOMR,
  //       $field->NAMA_PASIEN,
  //       $field->NOBOX,
  //       $field->TGL_PINJAM,
  //       $field->JUMLAH_RM,
  //       $field->PETUGAS,
  //       $field->NOBOX_KEMBALI,
  //       $field->KETERANGAN,
  //       $field->TGL_KEMBALI,
  //       ($field->JENIS_RM == "1") ? "Hidup Inaktif" : "Meninggal",
  //       $button.' '.$hapus
  //       );
        
  //     $no++;

  //   }

  //   $output = array(
  //     // "draw"            => $draw,
  //     // "recordsTotal"    => $listdata->num_rows(),
  //     // "recordsFiltered" => $listdata->num_rows(),
  //     "data"            => $data
  //   );
  //   echo json_encode($output);
  // }

  public function dataInaktif()
{
    $start = $this->input->post('start');
    $length = $this->input->post('length');
    $searchValue = $this->input->post('search')['value']; 
    $listdata = $this->ModelPeminjaman->dataListInaktif($start, $length, $searchValue);

    $data = array();
    $no = $start + 1;
    foreach ($listdata as $field) {
        $button = '<button type="button" class="btn btn-primary btn-rounded btn-sm" data-toggle="modal" data-target="#modal_inaktif" data-id="' . $field->ID . '" style="width:72px"><i class="fas fa-edit"></i></button>';
        $hapus = '<button type="button" class="btn btn-danger btn-rounded btn-sm hapus_inaktif" href="javaScript:;" id="hapus_inaktif" title="hapus_inaktif" data="' . $field->ID . '" style="width:72px"><i class="fa fa-trash"></i></button>';

        $data[] = array(
            $no,
            $field->NOMR,
            $field->NAMA_PASIEN,
            $field->NOBOX,
            $field->TGL_PINJAM,
            $field->JUMLAH_RM,
            $field->PETUGAS,
            $field->NOBOX_KEMBALI,
            $field->KETERANGAN,
            $field->TGL_KEMBALI,
            ($field->JENIS_RM == "1") ? "Hidup Inaktif" : "Meninggal",
            $button . ' ' . $hapus
        );
        $no++;
    }

    $output = array(
        "draw" => intval($this->input->post('draw')),
        "recordsTotal" => $this->ModelPeminjaman->countAll(),
        "recordsFiltered" => $this->ModelPeminjaman->countFiltered($searchValue),
        "data" => $data
    );
    echo json_encode($output);
}

  //

  public function simpanInaktif()
{
    $this->db->trans_begin();

    $post = $this->input->post();
    $nomr = $this->input->post('NOMR_INAKTIF');
    $cekdata = $this->ModelPeminjaman->cekDataInaktif($nomr)->row_array();

    if ($cekdata) {
        // Jika data sudah ada
        $result = array('status' => 'exists');
    } else {
        // Jika data belum ada, lakukan penyimpanan
        $tglcreate = date('Y-m-d H:i:s');
        $data_insert = array(
            'NOMR' => $this->input->post('NOMR_INAKTIF'),
            'NOBOX' => $this->input->post('NO_BOX_INAKTIF'),
            'TGL_PINJAM' => $tglcreate,
            'JUMLAH_RM' => $this->input->post('JUMLAH_RM_INAKTIF'),
            'ID_PETUGAS' => $this->input->post('PETUGAS_INAKTIF'),
            'NOBOX_KEMBALI' => $this->input->post('NO_BOX_PENGEMBALIAN'),
            'KETERANGAN' => $this->input->post('KETERANGAN_INAKTIF'),
            'TGL_KEMBALI' => $this->input->post('TANGGAL_PENGEMBALIAN'),
            'JENIS_RM' => $this->input->post('JENIS_INAKTIF'),
        );

        $this->db->insert('db_rekammedis.tb_inaktif', $data_insert);

        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
        } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
        }
    }

    echo json_encode($result);
}


  public function simpanModalInaktif()
  {
    $this->db->trans_begin();

    $post = $this->input->post();
    // echo "<pre>";print_r($post);exit();
    $id = $this->input->post('ID_INAKTIF_MODAL');
    $tglcreate = date('Y-m-d H:i:s');
                 
    $data_insert = array (

      'NOBOX'       => $this->input->post('NO_BOX_INAKTIF_MODAL'),
      'JUMLAH_RM'       => $this->input->post('JUMLAH_RM_INAKTIF_MODAL'),
      'ID_PETUGAS'       => $this->input->post('PETUGAS_INAKTIF_MODAL'),
      'NOBOX_KEMBALI'       => $this->input->post('NO_BOX_PENGEMBALIAN_MODAL'),
      'KETERANGAN'       => $this->input->post('KETERANGAN_INAKTIF_MODAL'),
      'TGL_KEMBALI'       => $this->input->post('TANGGAL_PENGEMBALIAN_MODAL'),
      'JENIS_RM'       => $this->input->post('JENIS_INAKTIF_MODAL'), 

    );
   // echo "<pre>";print_r($data_insert);exit();
    $this->db->where('ID', $id);
    $this->db->update('db_rekammedis.tb_inaktif', $data_insert);

    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
      } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
      }
 
    echo json_encode($result);
    
  }

    public function list_jumlah()
    {
        $result = $this->ModelPeminjaman->list_Jumlah();
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row['ID'];
            $sub_array['text'] = $row['DESKRIPSI'];
            $data[] = $sub_array;
        }
        echo json_encode($data);
    }

        public function list_petugas()
    {
        $result = $this->ModelPeminjaman->list_Petugas();
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row['id'];
            $sub_array['text'] = $row['nama_lengkap'];
            $data[] = $sub_array;
        }
        echo json_encode($data);
    }

public function list_ruangan()
    {
        $result = $this->ModelCoderAdmin->list_Ruangan();
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row['ID_SIMPEL'];
            $sub_array['text'] = $row['DESKRIPSI'];
            $data[] = $sub_array;
        }
        echo json_encode($data);
    }

public function get_dokter()
    {
        $result = $this->ModelPeminjaman->list_dokter();
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row['ID'];
            $sub_array['text'] = $row['nama_dokter'];
            $data[] = $sub_array;
        }
        echo json_encode($data);
    }

    public function list_filing(){
        $result = $this->ModelPeminjaman->list_Filing();
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row['id'];
            $sub_array['text'] = $row['nama_lengkap'];
            $data[] = $sub_array;
        }
        echo json_encode($data);
    }

  public function CekNomr()
{
    $nomr = $this->input->post('NOMR');

    $cekmr = $this->ModelPeminjaman->cek_nomr($nomr);
    
    if ($cekmr->num_rows() > 0) {
        $result = $cekmr->row_array();
        $row = array(
            'nomr' => $result['NORM'],
            'nama' => $result['NAMA']
        );
    } else {
        $row = array(
            'nomr' => '',
            'nama' => 'Data tidak ditemukan'
        );
    }

    echo json_encode($row);
}

public function hapusInaktif()
{
  $this->db->trans_begin();
  $post = $this->input->post();
  $id =$post['ID'];


  $dataUpdate = array (
    'STATUS'  => 0,
  );

    // echo "<pre>";print_r($id);exit();
  $this->db->where('db_rekammedis.tb_inaktif.ID', $id);
  $this->db->update('db_rekammedis.tb_inaktif', $dataUpdate);

  if ($this->db->trans_status() === false) {
    $this->db->trans_rollback();
    $result = array('status' => 'failed');
  } else {
    $this->db->trans_commit();
    $result = array('status' => 'success');
  }

  echo json_encode($result);
}


function modalInaktif()
  {
    $id = $this->input->post('id');
    $dataInaktif = $this->ModelPeminjaman->getDataInaktif($id)->row_array();
  
    $data = array(
      'id' => $id,
      'dataInaktif' => $dataInaktif,
    );
    $this->load->view('Peminjaman/modal_inaktif', $data);
  }
  public function simpanPeminjaman(){
    $this->db->trans_begin();

    // $post = $this->input->post();
    $tglcreate = date('Y-m-d H:i:s');    
    $nomr = $this->input->post('NOMR');
    $idRuang = $this->input->post('IDRUANG');
    $tanggal = $this->input->post('TANGGAL');

   
    $this->db->where('NOMR', $nomr);
    $this->db->where('TANGGAL', $tanggal);
    $this->db->where('STATUS', 1);
    $existingData = $this->db->get('db_rekammedis.tb_pendistribusian')->row();

    if ($existingData) {
        $result = array('status' => 'failed', 'message' => 'File sudah dipinjam pada tanggal ini.');
    } else {
        $data_insert = array(
            'NOMR'           => $nomr,
            'ID_RUANGAN'     => $idRuang,
            'ID_RUANGAN_DET' => $this->input->post('conter'),
            'TANGGAL'        => $tanggal,
            'JUMLAH_RM'      => $this->input->post('jumlahrm'),
            'ID_DOKTER'      => $this->input->post('NIP'),
            'ID_PETUGAS'     => $this->input->post('petugas'),
            'OLEH'           => $this->session->userdata('id'),
            'STATUS'         => 1,
        );

        $this->db->insert('db_rekammedis.tb_pendistribusian', $data_insert);

        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed', 'message' => 'Data Gagal Disimpan');
        } else {
            $this->db->trans_commit();
            $result = array('status' => 'success','message' => 'Data Berhasil Disimpan');
        }
    }

    echo json_encode($result);
  }


// public function simpanPeminjaman()
//   {
//     $this->db->trans_begin();

//     $post = $this->input->post();
//     // echo "<pre>";print_r($post);exit();
//     $tglcreate = date('Y-m-d H:i:s');
                 
//     $data_insert = array (
//       'NOMR'       => $this->input->post('NOMR'),
//       // 'NAMAPASIEN'         => $this->input->post('NAMA'),
//       'ID_RUANGAN'       => $this->input->post('IDRUANG'),
//       'ID_RUANGAN_DET'       => $this->input->post('conter'),
//       'TANGGAL'       => $this->input->post('TANGGAL'),
//       'JUMLAH_RM'       => $this->input->post('jumlahrm'),
//       'ID_DOKTER'       => $this->input->post('NIP'),
//       'ID_PETUGAS'       => $this->input->post('petugas'),
//       'OLEH'          => $this->session->userdata('id'),
//       'STATUS'  => 1,
//     );
//    // echo "<pre>";print_r($data_insert);exit();
//     $this->db->insert('db_rekammedis.tb_pendistribusian', $data_insert);

//     if ($this->db->trans_status() === false) {
//         $this->db->trans_rollback();
//         $result = array('status' => 'failed');
//       } else {
//         $this->db->trans_commit();
//         $result = array('status' => 'success');
//       }
 
//     echo json_encode($result);
    
//   }
    

}
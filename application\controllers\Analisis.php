<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Analisis extends CI_Controller {
    
    public function __construct()
    {
    parent::__construct();
    if($this->session->userdata('logged_in') == FALSE ){
        redirect('login');  
    }
    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('ModelAnalisis'));
    $this->load->model(array('ModelReview'));
    }

    public function indexAwal()
    {
      $data=array(      
        'isi'            => 'Analisis/indexKuantitatifAwal'
      );
      $this->load->view('Layout/Wrapper',$data);
    }
    public function indexAwalNilaiGuna()
    {
      $data=array(      
        'isi'            => 'Analisis/indexNilaiGunaAwal'
      );
      $this->load->view('Layout/Wrapper',$data);
    }
    public function index()
    {
      $this->load->view('Analisis/indexKuantitatif');
    }

    public function indexNG()
    {
      $this->load->view('Analisis/indexNilaiGuna');
    }
    public function indexUploadf()
    {
      $this->load->view('Analisis/IndexUploadf');
    }
    public function indexEditf($id_guna = null) {
      if ($id_guna === null) {
          // Redirect atau tampilkan error jika ID tidak disediakan
          redirect('nilaiguna');
          return;
      }

      $data_guna = $this->ModelAnalisis->getDataGunaById($id_guna);

      if (!$data_guna) {
          // Data tidak ditemukan, redirect atau tampilkan error
          $this->session->set_flashdata('error_message', 'Data rekam medis tidak ditemukan.');
          redirect('nilaiguna');
          return;
      }

      // Ambil info pasien (NOMR, NAMA)
      $pasien_info = $this->ModelAnalisis->cekNomrNG($data_guna['NORM'])->row_array();
      if ($pasien_info) {
          $data_guna['NAMA'] = $pasien_info['NAMA'];
          // Ambil daftar tanggal terdaftar berdasarkan NOMR
          $data_guna['TERDAFTAR_LIST'] = $this->ModelAnalisis->getTanggalDaftarByJenis($data_guna['NORM'], ($data_guna['JENIS'] == 1 ? 'rawat_jalan' : 'rawat_inap'));
      } else {
          $data_guna['NAMA'] = 'Nama tidak ditemukan';
          $data_guna['TERDAFTAR_LIST'] = [];
      }

      // Ambil detail file yang sudah diupload untuk ID_GUNA ini
      $data_guna['FILES'] = $this->ModelAnalisis->getDetailFilesByIdGuna($id_guna);

      // Tentukan folder identifier yang sama dengan logika saat upload
      $folderIdentifier = '';
      if (!empty($data_guna['TANGGAL'])) { // Jika menggunakan tanggal manual
          $folderIdentifier = date('ymd', strtotime($data_guna['TANGGAL']));
      } else if (!empty($data_guna['NOPEN'])) { // Jika menggunakan NOPEN
          $folderIdentifier = $data_guna['NOPEN'];
      }
      $data_guna['FOLDER_IDENTIFIER'] = $folderIdentifier;

      $data = [
          'data_guna' => $data_guna,
          'isi'            => 'Analisis/indexEditf'
      ];
      // $this->load->view('Analisis/indexEditf', $data);
      $this->load->view('Layout/Wrapper',$data);

  }
  public function indexHistory()
    {
      $this->load->view('Analisis/indexKuantitatifHistory');
    }


    public function index_kualitatifAwal()
    {
      $data=array(      
        'isi'            => 'Analisis/indexKualitatifAwal'
      );
      $this->load->view('Layout/Wrapper',$data);
    }

    public function index_kualitatif()
    {
      $this->load->view('Analisis/indexKualitatif');
    }
    public function index_kualitatifHistory(){
      $this->load->view('Analisis/indexKualitatifHistory');
    }
    public function CekNomr()
    {
        $nomr = $this->input->post('NOMR');    
        $cekmr = $this->ModelAnalisis->cekNomr($nomr);
        
        if ($cekmr->num_rows() > 0) {
            $result = $cekmr->row_array();
            $row = array(
                'nomr' => $result['NORM'],
                'nama' => $result['NAMAPASIEN'],
                'umur' => $result['UMUR'],
                'jk' => $result['JK']
            );
        } else {
            $row = array(
                'nomr' => '',
                'nama' => 'Data tidak ditemukan',
                'umur' => 'Data tidak ditemukan',
                'jk' => 'Data tidak ditemukan'
            );
        }
    
        echo json_encode($row);
    }

    public function CekNomrNG()
{
    $nomr = $this->input->post('NOMR');    
    $cekmr = $this->ModelAnalisis->cekNomrNG($nomr, true); // kirim param true utk situasional group by NOPEN

    if ($cekmr->num_rows() > 0) {
        $data = $cekmr->result_array();
        $terdaftar = array_map(function($row) {
            return [
                'tanggal' => $row['TERDAFTAR'],
                'nopen' => $row['NOPEN']
            ];
        }, $data);

        $result = $data[0]; // Ambil data utama dari baris pertama
        $row = [
            'nomr' => $result['NORM'],
            'nama' => $result['NAMA'],
            'tanggal_lahir' => $result['TANGGAL_LAHIR'],
            'terdaftar' => $terdaftar,
            'nopen' => $result['NOPEN'],
            'jk_id' => $result['JENIS_KELAMIN'],
            'jk' => $result['JK_DESKRIPSI'],
            'alamat' => $result['ALAMAT'],
            'nik' => $result['NIK'],
            'kontak' => $result['NOMOR_KONTAK'],
            'agama' => $result['AGAMA_DESKRIPSI'],
            'agama_id' => $result['AGAMA'],
            'pekerjaan' => $result['KERJA_DESKRIPSI'],
            'pekerjaan_id' => $result['PEKERJAAN'],
            'status' => $result['STATUS_DESKRIPSI'],
            'status_id' => $result['STATUS'],
            'kewarganegaraan_id' => $result['KEWARGANEGARAAN'],
            'kewarganegaraan' => $result['WARGA_DESKRIPSI'],
            'suku_id' => $result['SUKUBANGSA'],
            'suku' => $result['SB_DESKRIPSI']
            ];
        } else {
            $row = ['nama' => 'Data tidak ditemukan', 'terdaftar' => []];
        }

        echo json_encode($row);
    }

    public function getTanggalDaftarByJenis() {
      $nomr = $this->input->post('NOMR');
      $jenisRawat = $this->input->post('jenis_rawat');
      $result = $this->ModelAnalisis->getTanggalDaftarByJenis($nomr, $jenisRawat);
      $data = array();
      foreach ($result as $row) {
          $sub_array = array();
          $sub_array['id'] = $row['NOMOR'];
          $sub_array['text'] = $row['TANGGAL'];
          $data[] = $sub_array;
      }
      echo json_encode($data);
  }

  public function cekDuplikatData(): void
  {
      // Pastikan model sudah di-load
      $this->load->model('ModelAnalisis');
      $norm = $this->input->post('NORM');
      $jenis_rawat = $this->input->post('jenis_rawat');
      $nopen = $this->input->post('tanggal_daftar');
      $tanggal_manual = $this->input->post('tanggal_daftar_manual');

      $is_duplicate = false;
      
      // Cek berdasarkan input tanggal manual
      if (!empty($tanggal_manual)) {
          $jenis = ($jenis_rawat == 'rawat_jalan') ? 1 : 2;
          $is_duplicate = $this->ModelAnalisis->cekDuplikatManual($norm, $jenis, $tanggal_manual);
      } 
      // Cek berdasarkan NOPEN dari dropdown
      else if (!empty($nopen)) {
          $is_duplicate = $this->ModelAnalisis->cekDuplikatNopen($nopen);
      }
      header('Content-Type: application/json');
      if ($is_duplicate) {
          echo json_encode(['status' => 'exists']);
      } else {
          echo json_encode(['status' => 'not_exists']);
      }
  }

  public function cekKombinasiDuplikat()
    {
        // Mengatur header output sebagai JSON
        header('Content-Type: application/json');
        $norm = $this->input->post('NOMR');
        $jenis_rawat = $this->input->post('jenis_rawat');
        $tanggal_manual = $this->input->post('tanggal_daftar_manual');
        // Validasi input dasar
        if (empty($norm) || empty($jenis_rawat) || empty($tanggal_manual)) {
            echo json_encode(['duplicate' => false]);
            return;
        }
        // Konversi jenis_rawat ke format integer yang disimpan di DB (1: jalan, 2: inap)
        $jenis = ($jenis_rawat == 'rawat_jalan') ? 1 : 2;
        // Panggil model untuk memeriksa duplikasi
        $isDuplicate = $this->ModelAnalisis->cekDuplikatManual($norm, $jenis, $tanggal_manual);
        // Kirim respons JSON
        echo json_encode(['duplicate' => $isDuplicate]);
    }
    
    public function tabelData(){
      $getUploadf = $this->ModelAnalisis->getTblUploadf();
      $data = [
          'getUploadf' => $getUploadf,
      ];
      $table_html = $this->load->view('Analisis/IndexUploadfTabel', $data, true);
      $msg = [
          'data' => $table_html
      ];
      echo json_encode($msg);
    }

    public function simpanDataUpload()
    {
        $this->load->helper('file');
        $this->load->database();
        $this->load->library('session');
    
        $norm = $this->input->post('NOMR');
        $jenis_rawat = $this->input->post('jenis_rawat');
        $jenis = ($jenis_rawat == 'rawat_jalan') ? 1 : 2;
        $nopen = $this->input->post('tanggal_daftar');
        $tanggal_manual = $this->input->post('tanggal_daftar_manual');
    
        // --- VALIDASI DUPLIKAT SERVER-SIDE (FALLBACK) ---
        $is_duplicate = false;
        if (!empty($tanggal_manual)) { // Mode manual
            $is_duplicate = $this->ModelAnalisis->cekDuplikatManual($norm, $jenis, $tanggal_manual);
        } else if (!empty($nopen)) { // Mode dropdown (NOPEN)
            $is_duplicate = $this->ModelAnalisis->cekDuplikatNopen($nopen);
        }
    
        if ($is_duplicate) {
            $this->session->set_flashdata('error_message', 'Gagal menyimpan! Data untuk No MR, Jenis Rawat, dan Tanggal yang sama sudah ada.');
            redirect('nilaiguna');
            return;
        }
        
        $dataGuna = [
            'NORM' => $norm,
            'JENIS' => $jenis,
            'NOPEN' => $tanggal_manual ? null : $nopen,
            'TANGGAL' => $tanggal_manual ? $tanggal_manual : null,
            'STATUS' => 1
        ];
        $this->db->insert('db_rekammedis.tb_guna', $dataGuna);
        $id_guna = $this->db->insert_id();
    
        // Definisikan array mapping
        $fileMapping = [
            'dokumen_r2mk'                   => 1,
            'dokumen_summary_rawat_jalan'    => 2,
            'dokumen_assesment_rawat_jalan'  => 3,
            'dokumen_assesment_rawat_inap'   => 4,
            'dokumen_resume_radioterapi'     => 5,
            'dokumen_consent_bedah'          => 6,
            'dokumen_consent_diagnostik'     => 7,
            'dokumen_protokol_kemo'          => 8,
            'dokumen_laporan_operasi'        => 9,
            'dokumen_prosedur_diagnostik'    => 10,
            'dokumen_resume_medis'           => 11,
            'dokumen_kematian'               => 12,
            'dokumen_penunjang'              => 13
        ];
    
        // Buat folder berdasarkan NORM dan ID GUNA (lebih unik)
        $folderIdentifier = '';
        if (!empty($tanggal_manual)) {
            // Jika input manual, format tanggal menjadi YYMMDD. Contoh: 2025-06-17 -> 250617
            $folderIdentifier = date('ymd', strtotime($tanggal_manual));
        } else {
            // Jika dari dropdown, gunakan NOPEN sebagai nama folder
            $folderIdentifier = $nopen;
        }
        
        // Fallback jika identifier kosong (seharusnya tidak terjadi karena validasi)
        if (empty($folderIdentifier)) {
            log_message('error', 'Folder identifier kosong untuk NORM: ' . $norm);
            $this->session->set_flashdata('error_message', 'Terjadi kesalahan: Folder tujuan tidak dapat ditentukan.');
            redirect('nilaiguna');
            return;
        }
    
        // Buat path folder upload
        $uploadPath = '../berkasnilaiguna/' . $norm . '/' . $folderIdentifier . '/';
        if (!is_dir($uploadPath)) {
            mkdir($uploadPath, 0755, true);
        }

        foreach ($fileMapping as $fieldName => $jenisFileId) {
            // Cek file menggunakan $fieldName (key dari array)
            if (!empty($_FILES[$fieldName]['name'])) {
                $config['upload_path']   = $uploadPath;
                $config['allowed_types'] = 'pdf|jpg|jpeg|png|doc|docx|xls|xlsx|txt';
                // Ganti nama file untuk menghindari karakter aneh
                $safe_filename = time() . '_' . preg_replace("/[^a-zA-Z0-9.]/", "_", $_FILES[$fieldName]['name']);
                $config['file_name']     = $safe_filename;
                $config['overwrite']     = false;
                $config['max_size']      = 10240; // 10MB
                
                $this->load->library('upload', $config);
                $this->upload->initialize($config); // Re-initialize untuk setiap file
    
                // Lakukan upload menggunakan $fieldName
                if ($this->upload->do_upload($fieldName)) {
                    $uploadData = $this->upload->data();
                    $dataDetail = [
                        'ID_GUNA'    => $id_guna,
                        'JENIS_FILE' => $jenisFileId, // Masukkan ID jenis file ($jenisFileId)
                        'LOKASI'     => $uploadPath,
                        'NAMA_FILE'  => $uploadData['file_name'],
                        'STATUS'     => 1
                    ];
                    $this->db->insert('db_rekammedis.tb_guna_detail', $dataDetail);
                } else {
                    log_message('error', 'Upload gagal untuk ' . $fieldName . ': ' . $this->upload->display_errors());
                }
            }
        }
    
        $this->session->set_flashdata('success_message', 'Data berhasil disimpan.');
        redirect('nilaiguna');
    }

    public function updateDataUtama() {
      header('Content-Type: application/json');
      $id_guna = $this->input->post('id_guna');
      $norm = $this->input->post('NOMR');
      $jenis_rawat = $this->input->post('jenis_rawat');
      $jenis = ($jenis_rawat == 'rawat_jalan') ? 1 : 2;
      $nopen = $this->input->post('tanggal_daftar');
      $tanggal_manual = $this->input->post('tanggal_daftar_manual');

      // Validasi input
      if (empty($id_guna) || empty($norm) || empty($jenis_rawat) || (empty($nopen) && empty($tanggal_manual))) {
          echo json_encode(['status' => 'error', 'message' => 'Data tidak lengkap.']);
          return;
      }

      // Cek duplikasi hanya jika NOMR, Jenis Rawat, dan Tanggal diubah (atau untuk memastikan tidak ada duplikat baru)
      $is_duplicate = false;
      if (!empty($tanggal_manual)) {
          $is_duplicate = $this->ModelAnalisis->cekDuplikatManualUntukUpdate($id_guna, $norm, $jenis, $tanggal_manual);
      } else if (!empty($nopen)) {
          $is_duplicate = $this->ModelAnalisis->cekDuplikatNopenUntukUpdate($id_guna, $nopen);
      }

      if ($is_duplicate) {
          echo json_encode(['status' => 'error', 'message' => 'Gagal menyimpan! Data untuk No MR, Jenis Rawat, dan Tanggal yang sama sudah ada.']);
          return;
      }

      $data_update = [
          'NORM' => $norm,
          'JENIS' => $jenis,
          'NOPEN' => $tanggal_manual ? null : $nopen,
          'TANGGAL' => $tanggal_manual ? $tanggal_manual : null,
      ];

      $this->db->where('ID', $id_guna);
      if ($this->db->update('db_rekammedis.tb_guna', $data_update)) {
          echo json_encode(['status' => 'success', 'message' => 'Data utama berhasil diperbarui.']);
      } else {
          echo json_encode(['status' => 'error', 'message' => 'Gagal memperbarui data utama.']);
      }
  }

  public function uploadFileDetail() {
    header('Content-Type: application/json');
    $id_guna = $this->input->post('id_guna');
    $jenis_file_id = $this->input->post('jenis_file_id');
    $fieldName = $this->input->post('field_name'); // Nama field dari form (contoh: dokumen_r2mk)

    if (empty($id_guna) || empty($jenis_file_id) || empty($fieldName) || empty($_FILES[$fieldName]['name'])) {
        echo json_encode(['status' => 'error', 'message' => 'Data atau file tidak lengkap.']);
        return;
    }

    // Ambil data utama guna untuk mendapatkan NORM dan Tanggal/NOPEN untuk path folder
    $data_guna = $this->ModelAnalisis->getDataGunaById($id_guna);
    if (!$data_guna) {
        echo json_encode(['status' => 'error', 'message' => 'Data nilai guna tidak ditemukan.']);
        return;
    }

    // Tentukan folder identifier yang sama dengan logika saat upload
    $folderIdentifier = '';
    if (!empty($data_guna['TANGGAL'])) { // Jika menggunakan tanggal manual
        $folderIdentifier = date('ymd', strtotime($data_guna['TANGGAL']));
    } else if (!empty($data_guna['NOPEN'])) { // Jika menggunakan NOPEN
        $folderIdentifier = $data_guna['NOPEN'];
    }

    if (empty($folderIdentifier)) {
        echo json_encode(['status' => 'error', 'message' => 'Terjadi kesalahan: Folder tujuan tidak dapat ditentukan.']);
        return;
    }

    $uploadPath = '../berkasnilaiguna/' . $data_guna['NORM'] . '/' . $folderIdentifier . '/';
    if (!is_dir($uploadPath)) {
        mkdir($uploadPath, 0755, true);
    }

    $config['upload_path']   = $uploadPath;
    $config['allowed_types'] = 'pdf|jpg|jpeg|png|doc|docx|xls|xlsx|txt';
    $safe_filename = time() . '_' . preg_replace("/[^a-zA-Z0-9.]/", "_", $_FILES[$fieldName]['name']);
    $config['file_name']     = $safe_filename;
    $config['overwrite']     = false;
    $config['max_size']      = 10240; // 10MB

    $this->load->library('upload', $config);
    $this->upload->initialize($config);

    if ($this->upload->do_upload($fieldName)) {
        $uploadData = $this->upload->data();
        $newFileName = $uploadData['file_name'];

        // Cek apakah file dengan JENIS_FILE yang sama sudah ada
        $existingFile = $this->ModelAnalisis->getExistingFileDetail($id_guna, $jenis_file_id);

        if ($existingFile) {
            // Hapus file lama jika ada
            $oldFilePath = $existingFile['LOKASI'] . $existingFile['NAMA_FILE'];
            if (file_exists($oldFilePath)) {
                unlink($oldFilePath);
            }
            // Update record di tb_guna_detail
            $data_update_detail = [
                'LOKASI'    => $uploadPath,
                'NAMA_FILE' => $newFileName,
                'STATUS'    => 1 // Pastikan status aktif
            ];
            $this->db->where('ID_GUNA', $id_guna);
            $this->db->where('JENIS_FILE', $jenis_file_id);
            $this->db->update('db_rekammedis.tb_guna_detail', $data_update_detail);
        } else {
            // Insert record baru di tb_guna_detail
            $data_insert_detail = [
                'ID_GUNA'    => $id_guna,
                'JENIS_FILE' => $jenis_file_id,
                'LOKASI'     => $uploadPath,
                'NAMA_FILE'  => $newFileName,
                'STATUS'     => 1
            ];
            $this->db->insert('db_rekammedis.tb_guna_detail', $data_insert_detail);
        }

        echo json_encode([
            'status' => 'success',
            'message' => 'File berhasil diupload/diperbarui.',
            'new_file_name' => $newFileName,
            'new_file_path' => base_url('../berkasnilaiguna/' . $data_guna['NORM'] . '/' . $folderIdentifier . '/' . $newFileName)
        ]);
    } else {
        echo json_encode([
            'status' => 'error',
            'message' => $this->upload->display_errors('', ''),
            'error_code' => $this->upload->display_errors()
        ]);
    }
}

//   public function modalEditUploadf() {
//     $this->load->model('ModelAnalisis');
//     $id_guna = $this->input->post('id');
//     $uploadData = $this->ModelAnalisis->getEditUploadf($id_guna);

//     if ($uploadData) {
//         $data = array(
//             'ID' => $uploadData['ID'],
//             'NORM' => $uploadData['NORM'],
//             'NAMAPASIEN' => $uploadData['NAMAPASIEN'],
//             'JENIS' => $uploadData['JENIS'],
//             'TANGGAL_RAWAT' => $uploadData['TANGGAL_RAWAT'],
//         );
//         $msg['sukses'] = $this->load->view('Analisis/IndexUploadfEdit', $data, true);
//     } else {
//         $msg['error'] = 'Data tidak ditemukan.';
//     }
//     echo json_encode($msg);
// }



//   public function hapusUploadf(){
//     $this->db->trans_start();
//     $id = $this->input->post('id');  
    
//     // Update tb_guna status
//     $this->db->set('STATUS', 0);         
//     $this->db->where('ID', $id);
//     $this->db->update('db_rekammedis.tb_guna');
    
//     // Update tb_guna_detail status
//     $this->db->set('STATUS', 0);          
//     $this->db->where('ID_GUNA', $id);
//     $this->db->update('db_rekammedis.tb_guna_detail');
    
//     if ($this->db->trans_status() === false) {
//         $this->db->trans_rollback();
//         $result = array('status' => 'failed');
//     } else {
//         $this->db->trans_commit();
//         $result = array('status' => 'success');
//     }
//     echo json_encode($result);
// }

    public function getReferensi() {
      $jenis = $this->input->get('jenis');
      $query = $this->input->get('q');
      $result = $this->ModelAnalisis->getReferensiByJenis($jenis, $query);
  
      $data = array();
      foreach ($result as $row) {
        $data[] = [
          'id' => $row['ID'],
          'text' => $row['DESKRIPSI']
        ];
      }
  
      echo json_encode($data);
    }
  
    public function getNegara() {
      $query = $this->input->get('n');
      $result = $this->ModelAnalisis->getListNegara($query);
  
      $data = array();
      foreach ($result as $row) {
        $data[] = [
          'id' => $row['ID'],
          'text' => $row['DESKRIPSI']
        ];
      }
  
      echo json_encode($data);
    }

    public function updatePasien()
{
    $data = $this->input->post();

    $this->db->trans_start();

    // Update master.pasien
    $this->db->where('NORM', $data['NOMR']);
    $this->db->update('master.pasien', [
        'NAMA' => $data['nama'],
        'TANGGAL_LAHIR' => date('Y-m-d', strtotime($data['tanggal_lahir'])),
        'ALAMAT' => $data['alamat'],
        'JENIS_KELAMIN' => $data['jk_id'],
        'AGAMA' => $data['agama_id'],
        'PEKERJAAN' => $data['pekerjaan_id'],
        'STATUS' => $data['status_id'],
        'KEWARGANEGARAAN' => $data['kewarganegaraan_id'],
        'SUKUBANGSA' => $data['suku_id']
    ]);

    // Update master.kartu_identitas_pasien
    $this->db->where('NORM', $data['NOMR']);
    $this->db->update('master.kartu_identitas_pasien', [
        'NOMOR' => $data['nik']
    ]);

    // Update master.kontak_pasien
    $this->db->where('NORM', $data['NOMR']);
    $this->db->update('master.kontak_pasien', [
        'NOMOR' => $data['kontak']
    ]);

    $this->db->trans_complete();

    if ($this->db->trans_status() === FALSE) {
        echo json_encode(['success' => false, 'message' => 'Transaksi gagal']);
    } else {
        echo json_encode(['success' => true]);
    }
}

    // public function updatePasien() {
    //   $data = $this->input->post();
  
    //   $updated = $this->ModelAnalisis->updateDataPasien($data);
    //   if ($updated) {
    //       echo json_encode(['status' => true, 'message' => 'Data berhasil diperbarui.']);
    //   } else {
    //       echo json_encode(['status' => false, 'message' => 'Gagal memperbarui data.']);
    //   }
  // }
  public function modalTambahNG(){
    $add = $this->load->view('Analisis/IndexUploadfAdd','',true);
    $msg = [
        'data' => $add
    ];
    echo json_encode($msg);      
  }
  
    public function getTglDatangOptions() {
      $nomr = $this->input->post('NOMR'); 
      $term = $this->input->post('term');
      // error_log("Nilai nomr: " . $nomr);
      $result = $this->ModelAnalisis->getTglDatangOptions($nomr,$term); 
      $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row['NOMOR'];
            $sub_array['text'] = date('d-m-Y H:i:s', strtotime($row['TANGGAL']));
            $data[] = $sub_array;
        }
      echo json_encode($data); 
    }

    public function autocomplate(){
      $id= $this->input->post('tgl');    
      $cektgl = $this->ModelAnalisis->cekTgl($id);
      
      if ($cektgl->num_rows() > 0) {
          $result = $cektgl->row_array();
          $tglAwal = date('d-m-Y H:i:s', strtotime($result['MASUK']));
          $tglPulang = date('d-m-Y H:i:s', strtotime($result['KELUAR']));
          $row = array(
              'idK' => $result['NOMOR'],
              'nama' => $result['NAMA'],
              'tglAwal' => $tglAwal,
              'tglPulang' => $tglPulang,
              'iddok' => $result['DOKTER']
          );
      } else {
          $row = array(
              'idk' => '',
              'nama' => 'Data tidak ditemukan',
              'tglAwal' => 'Data tidak ditemukan',
              'tglPulang' => 'Data tidak ditemukan'
          );
      }
  
      echo json_encode($row);
      
    }
    
    public function simpanKuantitatif(){
      $this->db->trans_begin();
      $tglcreate = date('Y-m-d H:i:s');      
      
      // Data untuk analisis
      $data_insert_analisis = array(
          'ID_INPUT' => $this->session->userdata('id'),
          'TGL_INPUT' => $tglcreate,
          'TGL_MASUK' => date('Y-m-d H:i:s', strtotime($this->input->post('tglAwal'))),
          'TGL_KELUAR' => date('Y-m-d H:i:s', strtotime($this->input->post('tglPulang'))),
          'NOPEN' => $this->input->post('idtglDatang'),
          'NORM' => $this->input->post('NOMR'),
          'ID_DOKTER' => $this->input->post('IDDOK'),
          'STATUS' => 1,
      );
  
      $this->db->insert('db_rekammedis.tb_analisis', $data_insert_analisis);
      $id_detail_analisis = $this->db->insert_id();
  
      $jenis = 1;
      $getDataAnalisis = $this->ModelAnalisis->formAnalisis($jenis)->result_array();
      $data_insert_detail_analisis = array();
      foreach ($getDataAnalisis as $row) {
          $data_insert_detail_analisis[] = array(
              'ID_ANALISIS' => $id_detail_analisis,
              'ID_FORM' => $row['ID'],
          );
      }
      $data_insert_detail_analisis = array_unique($data_insert_detail_analisis, SORT_REGULAR);
      if (!empty($data_insert_detail_analisis)) {
          $this->db->insert_batch('db_rekammedis.tb_analisis_detail', $data_insert_detail_analisis);
      }

      // Data untuk review
      $data_insert_review = array(
        'ID_INPUT' => $this->session->userdata('id'),
        'TGL_INPUT' => $tglcreate,
        'ID_ANALISIS' => $id_detail_analisis,
        'TGL_MASUK' => date('Y-m-d H:i:s', strtotime($this->input->post('tglAwal'))),
        'TGL_KELUAR' => date('Y-m-d H:i:s', strtotime($this->input->post('tglPulang'))),
        'NOPEN' => $this->input->post('idtglDatang'),
        'NORM' => $this->input->post('NOMR'),
        'ID_DOKTER' => $this->input->post('IDDOK'),
        'STATUS' => 1,
    );

    $this->db->insert('db_rekammedis.tb_review', $data_insert_review);
    $id_detail_review = $this->db->insert_id();

    $getDataReview = $this->ModelReview->formReview()->result_array();
    $data_insert_detail_review = array();
    foreach ($getDataReview as $row) {
        $data_insert_detail_review[] = array(
            'ID_REVIEW' => $id_detail_review,
            'ID_FORM' => $row['ID'],
        );
    }
    $data_insert_detail_review = array_unique($data_insert_detail_review, SORT_REGULAR);
    if (!empty($data_insert_detail_review)) {
        $this->db->insert_batch('db_rekammedis.tb_review_detail', $data_insert_detail_review);
    }

  
      // Commit atau rollback transaksi
      if ($this->db->trans_status() === false) {
          $this->db->trans_rollback();
          $result = array('status' => 'failed');
      } else {
          $this->db->trans_commit();
          $result = array('status' => 'success');
      }
  
      echo json_encode($result);
  }
  
    public function tblKuan(){
      $user = $this->session->userdata('id');
      $listdata = $this->ModelAnalisis->dataTblKUan($user);
      $data=array();
      $no =1;
      foreach ($listdata->result() as $field) {
        // if ($field->STATUS == 1) {
          $button = '<button type="button" class="btn btn-primary btn-rounded btn-sm" title="Checklist Form" onclick="editKuan('.$field->ID.')" style="width:72px"><i class="fas fa-tasks"></i></i></button>';
          $hapus = '<button type="button" class="btn btn-danger btn-rounded btn-sm hapusKuan" onclick="hapusKuan('.$field->ID.',\''.$field->NORM.'\',\''.date('d F Y H:i:s', strtotime($field->TGL_MASUK)).'\')" id="hapusKuan" title="hapusKuan" style="width:72px"><i class="fa fa-trash"></i></button>';
        // }
        //  elseif ($field->STATUS == 2) {
        //   $button = '<button type="button" class="btn btn-primary btn-rounded btn-sm" title="edit data" onclick="editChecklist('.$field->ID.')" style="width:72px"><i class="fa fa-edit"></i></button>';
        //   // $edit = '<button type="button" class="btn btn-primary btn-rounded btn-sm" title="edit data" onclick="editChecklist('.$field->ID.')" style="width:72px"><i class="fa fa-edit"></i></button>';
        //   $hapus = '<button type="button" class="btn btn-danger btn-rounded btn-sm hapusKuan" onclick="hapusKuan('.$field->ID.',\''.date('d F Y H:i:s', strtotime($field->TGL_MASUK)).'\',\''.date('d F Y H:i:s', strtotime($field->TGL_KELUAR)).'\')" id="hapusKuan" title="hapusKuan" style="width:72px"><i class="fa fa-trash"></i></button>';
        // } 
       
        // $tgl_masuk = date('d-F-Y H:i:s', strtotime($field->TGL_MASUK));
        // $tgl_keluar = date('d-F-Y H:i:s', strtotime($field->TGL_KELUAR));
        // $tgl_input = date('d-F-Y H:i:s', strtotime($field->TGL_INPUT));
        // $tgl_lisis = ($field->TGL_ANALISIS != null) ? date('d-F-Y H:i:s', strtotime($field->TGL_ANALISIS)) : " - ";
        $data[] = array(
          $no,
          date_format(date_create($field->TGL_INPUT),'d-m-Y H:i:s'),
          $field->NAMA_INPUT,
          $field->NORM,
          $field->NAMAPASIEN,
          date_format(date_create($field->TGL_MASUK),'d-m-Y H:i:s'),
          date_format(date_create($field->TGL_KELUAR),'d-m-Y H:i:s'),
          $field->NAMADOK,
          // $field->STATUS_ANALISIS,
          $field->STATUS == 1 ? 'BELUM DIANALISIS' : 'SUDAH DIANALISIS',
          $field->STATUS_REVIEW == 1 ? 'BELUM REVIEW' : '',
          // $tgl_lisis,
          $button.' '.$hapus
          );
          
        $no++;
      }

      $output = array(
        "data"            => $data
      );
      echo json_encode($output);

    }
    // public function getHistoryAnalisis(){
    //   $user = $this->session->userdata('id');
    //   $listdata = $this->ModelAnalisis->dataTblKUanHistory($user);
    //   $data=array();
    //   $no =1;
    //   foreach ($listdata->result() as $field) {
      
    //     $button = '<button type="button" class="btn btn-primary btn-rounded btn-sm" title="edit data" onclick="editChecklist('.$field->ID.')" style="width:72px"><i class="fa fa-edit"></i></button>';
    //     $hapus = '<button type="button" class="btn btn-danger btn-rounded btn-sm hapusKuan" onclick="hapusKuan('.$field->ID.',\''.date('d F Y H:i:s', strtotime($field->TGL_MASUK)).'\',\''.date('d F Y H:i:s', strtotime($field->TGL_KELUAR)).'\')" id="hapusKuan" title="hapusKuan" style="width:72px"><i class="fa fa-trash"></i></button>';
    
    //     $tgl_lisis = ($field->TGL_ANALISIS != null) ? date('d-F-Y H:i:s', strtotime($field->TGL_ANALISIS)) : " - ";
    //     $data[] = array(
    //       $no,
    //       date_format(date_create($field->TGL_INPUT),'d-m-Y H:i:s'),
    //       $field->NAMA_INPUT,
    //       $field->NORM,
    //       $field->NAMAPASIEN,
    //       date_format(date_create($field->TGL_MASUK),'d-m-Y H:i:s'),
    //       date_format(date_create($field->TGL_KELUAR),'d-m-Y H:i:s'),
    //       $field->NAMADOK,
    //       // $field->STATUS_ANALISIS,
    //       $field->STATUS == 1 ? 'BELUM DIANALISIS' : 'SUDAH DIANALISIS',
    //       $tgl_lisis,
    //       $button.''. $hapus
    //       );
          
    //     $no++;
    //   }

    //   $output = array(
    //     "data"            => $data
    //   );
    //   echo json_encode($output);
    // }
  //  
  
  public function getHistoryAnalisis() {
    $user = $this->session->userdata('id');
    $listdata = $this->ModelAnalisis->dataTblKUanHistory($user);
    
    $data = array();
    $no = 1;
    
    foreach ($listdata->result() as $field) {
        $button = '<button type="button" class="btn btn-primary btn-rounded btn-sm" title="edit data" onclick="editChecklist('.$field->ID.')" style="width:72px"><i class="fa fa-edit"></i></button>';
        $hapus = '<button type="button" class="btn btn-danger btn-rounded btn-sm hapusKuan" onclick="hapusKuan('.$field->ID.',\''.date('d F Y H:i:s', strtotime($field->TGL_MASUK)).'\',\''.date('d F Y H:i:s', strtotime($field->TGL_KELUAR)).'\')" id="hapusKuan" title="hapusKuan" style="width:72px"><i class="fa fa-trash"></i></button>';
        
        $data[] = array(
            $no++,
            date_format(date_create($field->TGL_INPUT),'d-m-Y H:i:s'),
            $field->NAMA_INPUT,
            $field->NORM,
            $field->NAMAPASIEN,
            date_format(date_create($field->TGL_MASUK),'d-m-Y H:i:s'),
            date_format(date_create($field->TGL_KELUAR),'d-m-Y H:i:s'),
            $field->NAMADOK,
            $field->STATUS == 1 ? 'BELUM DIANALISIS' : 'SUDAH DIANALISIS',
            $field->TGL_ANALISIS ? date_format(date_create($field->TGL_ANALISIS),'d-m-Y H:i:s') : '-',
            $button.' '.$hapus
        );
    }

    echo json_encode(array('data' => $data));
  }
  
  // public function modalEditKuan() {
  //   $id = $this->input->post('id');
  //   $ja = 1;
  //   $ambildata = $this->ModelAnalisis->modalKuan($id);
    
  //   if ($ambildata->num_rows() > 0) {
  //     $row = $ambildata->row_array();
      
  //     $data = [
  //         'ID'      => $row['ID'],
  //         'TGL'     => $row['TGL_INPUT'],
  //         'NAMAPAS' => $row['NAMAPASIEN'],
  //         'NORM'    => $row['NORM'],
  //         'NAMADOK' => $row['NAMADOK'],
  //         'FORM_DATA' => []
  //     ];

  //     $form_data = $this->ModelAnalisis->getAnalisis($id, $ja)->result_array();

  //     foreach ($form_data as $form) {
  //         $jenis = $form['JENIS'];
  //         $judul_id = $form['ID_JUDUL'];
  //         $subjudul_id = $form['ID_SUBJUDUL'];

  //         if (!isset($data['FORM_DATA'][$jenis])) {
  //             $data['FORM_DATA'][$jenis] = [];
  //         }
      
  //         // Cek apakah judul sudah ada dalam FORM_DATA[JENIS]
  //         if (!isset($data['FORM_DATA'][$jenis][$judul_id])) {
  //             $data['FORM_DATA'][$jenis][$judul_id] = [
  //                 'JUDUL' => $form['JUDUL'] ?: ' ',
  //                 'SUBJUDULS' => []
  //             ];
  //         }
      
  //         // Cek apakah subjudul sudah ada dalam FORM_DATA[JENIS][JUDUL]
  //         if (!isset($data['FORM_DATA'][$jenis][$judul_id]['SUBJUDULS'][$subjudul_id])) {
  //             $data['FORM_DATA'][$jenis][$judul_id]['SUBJUDULS'][$subjudul_id] = [
  //                 'SUBJUDUL' => $form['SUBJUDUL'] ?: ' ',
  //                 'LABELS' => []
  //             ];
  //         }
      
  //         // Cek apakah label sudah ada dalam FORM_DATA[JENIS][JUDUL][SUBJUDUL]
  //         $label_id = $form['IDFORM'];
  //         if (!isset($data['FORM_DATA'][$jenis][$judul_id]['SUBJUDULS'][$subjudul_id]['LABELS'][$label_id])) {
  //             $data['FORM_DATA'][$jenis][$judul_id]['SUBJUDULS'][$subjudul_id]['LABELS'][$label_id] = [
  //                 'IDFORM' => $form['IDFORM'],
  //                 'NAMA' => $form['NAMA'],
  //                 'OPTIONS' => []
  //             ];
  //         }
      
  //         // Tambahkan opsi (checkbox) dalam label
  //         $data['FORM_DATA'][$jenis][$judul_id]['SUBJUDULS'][$subjudul_id]['LABELS'][$label_id]['OPTIONS'][] = [
  //             'IDISI' => $form['IDISI'],
  //             'DESKRIPSI' => $form['DESKRIPSI'],
  //             'NILAI' => $form['NILAI'],
  //             'ID_JAWAB' => $form['ID_JAWAB']
  //         ];
  //     }

      
  //     $msg = [
  //         'sukses' => $this->load->view('Analisis/indexKuantitatif_modal', $data, true)
  //     ];

  //     echo json_encode($msg);
  //     }
  //   }

  public function modalEditKuan() {
    $id = $this->input->post('id');
    $ja = 1;
    $ambildata = $this->ModelAnalisis->modalKuan($id);
    
    if ($ambildata->num_rows() > 0) {
        $row = $ambildata->row_array();
        
        $data = [
            'ID'      => $row['ID'],
            'TGL'     => $row['TGL_INPUT'],
            'NAMAPAS' => $row['NAMAPASIEN'],
            'NORM'    => $row['NORM'],
            'NAMADOK' => $row['NAMADOK'],
            'FORM_DATA' => [
                'analisis' => [],
                'review' => []
            ]
        ];

        // Mengambil data Analisis dan mengelompokkan berdasarkan JENIS
        $form_data = $this->ModelAnalisis->getAnalisis($id, $ja)->result_array();
        foreach ($form_data as $form) {
            $jenis = $form['JENIS'];
            if (!isset($data['FORM_DATA']['analisis'][$jenis])) {
                $data['FORM_DATA']['analisis'][$jenis] = [];
            }
            $this->prosesFormData($data['FORM_DATA']['analisis'][$jenis], $form);
        }

        // Mengambil data Review dan mengelompokkan berdasarkan JENIS
        $form_data2 = $this->ModelAnalisis->getReview($id)->result_array();
        foreach ($form_data2 as $form) {
            $jenis = $form['JENIS'];
            if (!isset($data['FORM_DATA']['review'][$jenis])) {
                $data['FORM_DATA']['review'][$jenis] = [];
            }
            $this->prosesFormData($data['FORM_DATA']['review'][$jenis], $form);
        }

        $msg = [
            'sukses' => $this->load->view('Analisis/indexKuantitatif_modal', $data, true)
        ];

        echo json_encode($msg);
    }
  }

    private function prosesFormData(&$target, $form) {
      $judul_id = $form['ID_JUDUL'];
      $subjudul_id = $form['ID_SUBJUDUL'];
      $label_id = $form['IDFORM'];

      if (!isset($target[$judul_id])) {
          $target[$judul_id] = [
              'JUDUL' => $form['JUDUL'] ?: ' ',
              'SUBJUDULS' => []
          ];
      }

      if (!isset($target[$judul_id]['SUBJUDULS'][$subjudul_id])) {
          $target[$judul_id]['SUBJUDULS'][$subjudul_id] = [
              'SUBJUDUL' => $form['SUBJUDUL'] ?: ' ',
              'LABELS' => []
          ];
      }

      if (!isset($target[$judul_id]['SUBJUDULS'][$subjudul_id]['LABELS'][$label_id])) {
          $target[$judul_id]['SUBJUDULS'][$subjudul_id]['LABELS'][$label_id] = [
              'IDFORM' => $form['IDFORM'],
              'NAMA' => $form['NAMA'],
              'OPTIONS' => []
          ];
      }

      $target[$judul_id]['SUBJUDULS'][$subjudul_id]['LABELS'][$label_id]['OPTIONS'][] = [
          'IDISI' => $form['IDISI'],
          'DESKRIPSI' => $form['DESKRIPSI'],
          'NILAI' => $form['NILAI'],
          'ID_JAWAB' => $form['ID_JAWAB'],
          'IDUPDATE' => $form['IDUPDATE']  
      ];
    }


    public function simpanKual() {
      $this->db->trans_begin();
      $id = $this->input->post('id');
      $type = $this->input->post('type');
      
      if ($type === 'radio') {
          $idIsi = $this->input->post('idIsi');
          $nilai = $this->input->post('nilai');
          if (!empty($id) && !empty($idIsi) && !empty($nilai)) {
              $dataUpdate = array(
                  'ID_JAWAB' => $idIsi,
                  'STATUS' => $nilai,
              );
  
              $this->db->where('ID', $id);
              $this->db->update('db_rekammedis.tb_analisis_detail', $dataUpdate);
  
          } else {
              $this->db->trans_rollback();
              $result = array('status' => 'failed', 'message' => 'Data radio tidak valid');
              echo json_encode($result);
              return;
          }
  
      } elseif ($type === 'select') {
          $selectedValue = $this->input->post('selectedValue');
          if (!empty($id) && !empty($selectedValue)) {
              $dataUpdate = array(
                  'ID_JAWAB' => $selectedValue,
              );
  
              $this->db->where('ID', $id);
              $this->db->update('db_rekammedis.tb_analisis_detail', $dataUpdate);
  
          } else {
              $this->db->trans_rollback();
              $result = array('status' => 'failed', 'message' => 'Data select tidak valid');
              echo json_encode($result);
              return;
          }
      } else {
          $this->db->trans_rollback();
          $result = array('status' => 'failed', 'message' => 'Tipe data tidak dikenali');
          echo json_encode($result);
          return;
      }
  
      if ($this->db->trans_status() === FALSE) {
          $this->db->trans_rollback();
          $result = array('status' => 'failed');
      } else {
          $this->db->trans_commit();
          $result = array('status' => 'success');
      }
      echo json_encode($result);
    }

    public function simpanKuan() {
      $this->db->trans_begin();
      
      $id = $this->input->post('id');
      $idIsi = $this->input->post('idIsi');
      $nilai = $this->input->post('nilai');
      $type = $this->input->post('type');
  
      if (empty($id) || empty($idIsi) || empty($nilai) || empty($type)) {
          $this->db->trans_rollback();
          echo json_encode(['status' => 'failed', 'message' => 'Data tidak valid']);
          return;
      }
  
      $table = ($type === 'analisis') ? 'db_rekammedis.tb_analisis_detail' : 'db_rekammedis.tb_review_detail';
  
      $dataUpdate = [
          'ID_JAWAB' => $idIsi,
          'STATUS' => $nilai
      ];
  
      $this->db->where('ID', $id);
      $this->db->update($table, $dataUpdate);
  
      if ($this->db->trans_status() === FALSE) {
          $this->db->trans_rollback();
          echo json_encode(['status' => 'failed']);
      } else {
          $this->db->trans_commit();
          echo json_encode(['status' => 'success']);
      }
    }
  
  
    
    public function listCariDokter(){
      $result = $this->ModelAnalisis->listDokter();
      $data = array();
      foreach ($result as $row) {
          $sub_array = array();
          $sub_array['id'] = $row['NIP'];
          $sub_array['text'] = $row['NAMADOK']; 
          $data[] = $sub_array;
      }
        // var_dump($data);
      echo json_encode($data);
    }
    public function saveCheckKuan() {       
      $ID = $this->input->post('ID');
      $user = $this->session->userdata('id');
      $tgl  = date('Y-m-d H:i:s');
      // var_dump($user);
      // $this->db->trans_begin();
      $dataUpdate = array(
        'ID_INPUT' => $user,
        'STATUS' => 2,
        'TGL_ANALISIS' => $tgl,
      );

      $this->db->where('ID', $ID);
      $this->db->update('db_rekammedis.tb_analisis', $dataUpdate);       
      if ($this->db->affected_rows() > 0) {
          $response['status'] = 'success';
      } else {
          $response['status'] = 'error';
      }
      echo json_encode($response);

    }
    public function modalEditKuanCheck(){
      $id = $this->input->post('id');
      $ambildata = $this->ModelAnalisis->modalKuan($id);
      $ja = 1;
      if($ambildata->num_rows()>0){
        $row=$ambildata->row_array();
        $data=[
          'ID'    => $row['ID'],
          'TGL'   =>$row['TGL_ANALISIS'],
          'NAMAPAS'  =>$row['NAMAPASIEN'],
          'NORM'  =>$row['NORM'],
          'NAMADOK'  =>$row['NAMADOK'],
          'FORM_DATA' => [
            'analisis' => [],
            'review' => []
            ]
        ];

        // Mengambil data Analisis dan mengelompokkan berdasarkan JENIS
        $form_data = $this->ModelAnalisis->getAnalisis($id, $ja)->result_array();
        foreach ($form_data as $form) {
            $jenis = $form['JENIS'];
            if (!isset($data['FORM_DATA']['analisis'][$jenis])) {
                $data['FORM_DATA']['analisis'][$jenis] = [];
            }
            $this->prosesFormData($data['FORM_DATA']['analisis'][$jenis], $form);
        }

        // Mengambil data Review dan mengelompokkan berdasarkan JENIS
        $form_data2 = $this->ModelAnalisis->getReview($id)->result_array();
        foreach ($form_data2 as $form) {
            $jenis = $form['JENIS'];
            if (!isset($data['FORM_DATA']['review'][$jenis])) {
                $data['FORM_DATA']['review'][$jenis] = [];
            }
            $this->prosesFormData($data['FORM_DATA']['review'][$jenis], $form);
        }

      
        $msg=[
          'sukses'=>$this->load->view('Analisis/indexKuantitatif_modal_check', $data, true)
        ];
        echo json_encode($msg);
      } 

    }

    public function saveEdit() {
      $dataToSend = $this->input->post('dataToSend');
      // var_dump($dataToSend);
      foreach ($dataToSend as $data) {
        $id = $data['id'];
        $selectedId = $data['selectedId'];
        $selectedNilai = $data['selectedNilai'];
  
        $dataUpdate = array(
            'ID_JAWAB' => $selectedId,
            'STATUS' => $selectedNilai,
        );
  
        $this->db->where('ID', $id);
        $this->db->update('db_rekammedis.tb_analisis_detail', $dataUpdate); 
  
        if ($this->db->affected_rows() <= 0) {
            $response = array('status' => 'error');
            echo json_encode($response);
        }
      }
      $response = array('status' => 'success');
      echo json_encode($response);
    }
    public function hapusKuan(){
      $id = $this->input->post('id');
      $this->db->set('status', 0);
      $this->db->where('ID', $id); 
      $this->db->update('db_rekammedis.tb_analisis'); 
  
      $this->db->set('status', 0);
      $this->db->where('ID_ANALISIS', $id); 
      $this->db->update('db_rekammedis.tb_analisis_detail'); 

      $this->db->set('status', 0);
      $this->db->where('ID_ANALISIS', $id); 
      $this->db->update('db_rekammedis.tb_review'); 

      // Ambil ID REVIEW
      $this->db->select('ID');
      $this->db->where('ID_ANALISIS', $id);
      $query = $this->db->get('db_rekammedis.tb_review');
      
      if ($query->num_rows() > 0) {
          $review = $query->row();
          $ID = $review->ID;  
          $this->db->set('status', 0);
          $this->db->where('ID_REVIEW', $ID);  
          $this->db->update('db_rekammedis.tb_review_detail');
      }


      $response = array('status' => 'success');
      echo json_encode($response);
    }
    // end Kuantitatif
    
    
    
    public function getTglDatangOptionsKual() {
      $nomr = $this->input->post('NOMR'); 
      $term = $this->input->post('term');
      // error_log("Nilai nomr: " . $nomr);
      $result = $this->ModelAnalisis->getTglDatangOptionsK($nomr,$term); 
      $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row['NOMOR'];
            $sub_array['text'] = date('d-m-Y H:i:s', strtotime($row['TANGGAL']));
            $data[] = $sub_array;
        }
      echo json_encode($data); 
    }
    public function simpanKualitatif(){
      $this->db->trans_begin();
      $tglcreate = date('Y-m-d H:i:s');
                   
      $data_insert = array (
        'ID_INPUT'          => $this->session->userdata('id'),
        'TGL_INPUT'         => $tglcreate,
        'TGL_MASUK'         => date('Y-m-d H:i:s', strtotime($this->input->post('tglAwal'))),
        'TGL_KELUAR'        => date('Y-m-d H:i:s', strtotime($this->input->post('tglPulang'))),
        'NOPEN'             => $this->input->post('idtglDatang'),
        'NORM'              => $this->input->post('NOMR'),
        'ID_DOKTER'              => $this->input->post('IDDOK'),
        'STATUS'            => 3,        
      );
  
      $this->db->insert('db_rekammedis.tb_analisis', $data_insert);
      $id_detail = $this->db->insert_id();
      $jenis = 2;

      $getData = $this->ModelAnalisis->formAnalisis($jenis)->result_array();
      $data_insert_detail = array();
      foreach ($getData as $row) {
          $data_insert_detail[] = array(
              'ID_ANALISIS' => $id_detail,
              'ID_FORM'   => $row['ID'],
          );
      }
      $data_insert_detail = array_unique($data_insert_detail, SORT_REGULAR);
      
      if (!empty($data_insert_detail)) {
          $this->db->insert_batch('db_rekammedis.tb_analisis_detail', $data_insert_detail);
      }
  
      if ($this->db->trans_status() === false) {
          $this->db->trans_rollback();
          $result = array('status' => 'failed');
        } else {
          $this->db->trans_commit();
          $result = array('status' => 'success');
        }
   
      echo json_encode($result);
  
    }

    public function tblKual(){
      $user = $this->session->userdata('id');
      $listdata = $this->ModelAnalisis->dataTblKUal($user);
      $data=array();
      $no =1;
      foreach ($listdata->result() as $field) {
        // if ($field->STATUS == 3) {
          $button = '<button type="button" class="btn btn-primary btn-rounded btn-sm" title="Checklist Form" onclick="editKual('.$field->ID.')" style="width:72px"><i class="fas fa-tasks"></i></button>';
          $hapus = '<button type="button" class="btn btn-danger btn-rounded btn-sm hapusKual" onclick="hapusKual('.$field->ID.',\''.$field->NORM.'\',\''.date('d F Y H:i:s', strtotime($field->TGL_MASUK)).'\')" id="hapusKual" title="Hapus Analisis Kualitatif" style="width:72px"><i class="fa fa-trash"></i></button>';
        // } 
        // elseif ($field->STATUS == 4) {
        //   $button = '<button type="button" class="btn btn-primary btn-rounded btn-sm" title="edit data" onclick="editChecklist2('.$field->ID.')" style="width:72px"><i class="fa fa-edit"></i></button>';
        //   // $edit = '<button type="button" class="btn btn-primary btn-rounded btn-sm" title="edit data" onclick="editChecklist('.$field->ID.')" style="width:72px"><i class="fa fa-edit"></i></button>';
        //   $hapus = '<button type="button" class="btn btn-danger btn-rounded btn-sm hapusKual" onclick="hapusKual('.$field->ID.',\''.date('d F Y H:i:s', strtotime($field->TGL_MASUK)).'\',\''.date('d F Y H:i:s', strtotime($field->TGL_KELUAR)).'\')" id="hapusKual" title="hapusKuan" style="width:72px"><i class="fa fa-trash"></i></button>';
        // } 
        
        $tgl_masuk = date('d-F-Y H:i:s', strtotime($field->TGL_MASUK));
        $tgl_keluar = date('d-F-Y H:i:s', strtotime($field->TGL_KELUAR));
        $data[] = array(
          $no,
          $field->TGL_INPUT,
          $field->NAMA_INPUT,
          $field->NORM,
          $field->NAMAPASIEN,
          $tgl_masuk,
          $tgl_keluar,
          $field->NAMADOK,
          // $field->STATUS_ANALISIS,
          $field->STATUS == 3 ? 'BELUM DIANALISIS' : '',
          $button.'.'.$hapus
          );
          
        $no++;
      }

      $output = array(
        "data"            => $data
      );
      echo json_encode($output);
    }
    public function hapusKual(){
      $id = $this->input->post('id');
      $this->db->set('status', 0);
      $this->db->where('ID', $id); 
      $this->db->update('db_rekammedis.tb_analisis'); 

      $this->db->set('status', 0);
      $this->db->where('ID_ANALISIS', $id); 
      $this->db->update('db_rekammedis.tb_analisis_detail'); 
      
      $response = array('status' => 'success');
      echo json_encode($response);
    }
    public function getHistoryAnalisisKual() {
      $user = $this->session->userdata('id');
  
      $start = $this->input->post('start'); 
      $length = $this->input->post('length'); 
      $search = $this->input->post('search')['value']; 

      $listdata = $this->ModelAnalisis->dataTblKUalHistory($user, $start, $length, $search);
      
      $data = array();
      $no = $start + 1; 
  
      foreach ($listdata['data'] as $field) {
          $button = '<button type="button" class="btn btn-primary btn-rounded btn-sm" title="edit data" onclick="editChecklist2('.$field->ID.')" style="width:72px"><i class="fa fa-edit"></i></button>';
          $hapus = '<button type="button" class="btn btn-danger btn-rounded btn-sm hapusKual" onclick="hapusKual('.$field->ID.',\''.date('d F Y H:i:s', strtotime($field->TGL_MASUK)).'\',\''.date('d F Y H:i:s', strtotime($field->TGL_KELUAR)).'\')" id="hapusKual" title="hapusKual" style="width:72px"><i class="fa fa-trash"></i></button>';
  
          $tgl_masuk = date('d-F-Y H:i:s', strtotime($field->TGL_MASUK));
          $tgl_keluar = date('d-F-Y H:i:s', strtotime($field->TGL_KELUAR));

          $jumlah_status_null = $field->JUMLAH_STATUS_NULL;
          $status_text = $field->STATUS == 3 ? '' : 'SUDAH DIANALISIS';
          if ($jumlah_status_null !== null && $jumlah_status_null > 0) {
              $status_text .=   '<span style="
                display: inline-block;
                background-color: red;
                color: white;
                border-radius: 50%;
                padding: 3px 8px;
                font-size: 9px;
                font-weight: bold;
                margin-left: 5px;
            ">' . $jumlah_status_null . '</span>';
              $status_text = '<span style="color:red;">' . $status_text . '</span>';
          }
  
          $data[] = array(
              $no,
              $field->TGL_INPUT,
              $field->NAMA_INPUT,
              $field->NORM,
              $field->NAMAPASIEN,
              $tgl_masuk,
              $tgl_keluar,
              $field->NAMADOK,
              $status_text,          
              $field->TGL_ANALISIS,
              $button . $hapus
          );
  
          $no++;
      }
  
      $output = array(
          "draw" => $this->input->post('draw'),
          "recordsTotal" => $listdata['recordsTotal'],
          "recordsFiltered" => $listdata['recordsFiltered'],
          "data" => $data,
      );
  
      echo json_encode($output);
   }
  

    public function modalEditKual(){
      $id = $this->input->post('id');
      $ambildata = $this->ModelAnalisis->modalKuan($id);
      if($ambildata->num_rows()>0){
        $row=$ambildata->row_array();
        $data=[
          'ID'    => $row['ID'],
          'TGL'   =>$row['TGL_INPUT'],
          'NAMAPAS'  =>$row['NAMAPASIEN'],
          'NORM'  =>$row['NORM'],
          'NAMADOK'  =>$row['NAMADOK']

        ];
      }

      $data['form_j27'] = $this->ModelAnalisis->getFormsByJenis(27,$id);
      $data['form_j28'] = $this->ModelAnalisis->getFormsByJenis(28,$id);
      $data['form_j29'] = $this->ModelAnalisis->getFormsByJenis(29,$id);
      $data['form_j30'] = $this->ModelAnalisis->getFormsByJenis(30,$id);
      $data['form_j31'] = $this->ModelAnalisis->getFormsByJenis(31,$id);
      $data['form_j32'] = $this->ModelAnalisis->getFormsByJenis(32,$id);
      $data['form_j33'] = $this->ModelAnalisis->getFormsByJenis(33,$id);
      $data['form_j34'] = $this->ModelAnalisis->getFormsByJenis(34,$id);
      $data['form_j35'] = $this->ModelAnalisis->getFormsByJenis(35,$id);
      $data['form_j36'] = $this->ModelAnalisis->getFormsByJenis(36,$id);
      $data['form_j37'] = $this->ModelAnalisis->getFormsByJenis(37,$id);
      $data['form_j38'] = $this->ModelAnalisis->getFormsByJenis(38,$id);
      $data['form_j39'] = $this->ModelAnalisis->getFormsByJenis(39,$id);
      // $data['form_j40'] = $this->ModelAnalisis->getFormsByJenis(40,$id);

      $msg=[
        'sukses'=>$this->load->view('Analisis/indexKualitatif_modal', $data, true)
      ];
      echo json_encode($msg);    
    }
    public function saveCheckKual(){
      $ID = $this->input->post('ID');
      $user = $this->session->userdata('id');
      $tgl  = date('Y-m-d H:i:s');
      
      $this->load->database();
      $dataUpdate = array(
        'ID_INPUT' => $user,
        'STATUS' => 4,
        'TGL_ANALISIS' => $tgl,
      );

      $this->db->where('ID', $ID);
      $this->db->update('db_rekammedis.tb_analisis', $dataUpdate);
      // $this->db->set('STATUS', 4);
      // $this->db->set('TGL_ANALISIS', 'NOW()', false);
      // $this->db->where('ID', $ID);
      // $this->db->update('db_rekammedis.tb_analisis');

      
      if ($this->db->affected_rows() > 0) {
          $response['status'] = 'success';
      } else {
          $response['status'] = 'error';
      }
      echo json_encode($response);
    }

    public function modalEditKualCheck(){
      $id = $this->input->post('id');
      $ambildata = $this->ModelAnalisis->modalKuan($id);
      if($ambildata->num_rows()>0){
        $row=$ambildata->row_array();
        $data=[
          'ID'    => $row['ID'],
          'TGL'   =>$row['TGL_ANALISIS'],
          'NAMAPAS'  =>$row['NAMAPASIEN'],
          'NORM'  =>$row['NORM'],
          'NAMADOK'  =>$row['NAMADOK']

        ];
      }

      $data['form_j27'] = $this->ModelAnalisis->getFormsByJenis(27,$id);
      $data['form_j28'] = $this->ModelAnalisis->getFormsByJenis(28,$id);
      $data['form_j29'] = $this->ModelAnalisis->getFormsByJenis(29,$id);
      $data['form_j30'] = $this->ModelAnalisis->getFormsByJenis(30,$id);
      $data['form_j31'] = $this->ModelAnalisis->getFormsByJenis(31,$id);
      $data['form_j32'] = $this->ModelAnalisis->getFormsByJenis(32,$id);
      $data['form_j33'] = $this->ModelAnalisis->getFormsByJenis(33,$id);
      $data['form_j34'] = $this->ModelAnalisis->getFormsByJenis(34,$id);
      $data['form_j35'] = $this->ModelAnalisis->getFormsByJenis(35,$id);
      $data['form_j36'] = $this->ModelAnalisis->getFormsByJenis(36,$id);
      $data['form_j37'] = $this->ModelAnalisis->getFormsByJenis(37,$id);
      $data['form_j38'] = $this->ModelAnalisis->getFormsByJenis(38,$id);
      $data['form_j39'] = $this->ModelAnalisis->getFormsByJenis(39,$id);
      // $data['form_j40'] = $this->ModelAnalisis->getFormsByJenis(40,$id);
      
      
      // for ($V = 1; $V <= 14; $V++) {
      //     $data['label' . $V] = $this->ModelAnalisis->getFormsByV($V, $id);
      // }

      

      // $data['selectOption'] = $this->ModelAnalisis->getOptionByJenis(1);
     
      
      
      $msg=[
        'sukses'=>$this->load->view('Analisis/indexKualitatif_modal_Check', $data, true)
      ];
      echo json_encode($msg);

    }
    public function saveEditKual() {
      $dataToSend = $this->input->post('dataToSend');
      var_dump($dataToSend);
      foreach ($dataToSend as $data) {
        $id = $data['id'];
        $selectedId = $data['selectedId'];
        $selectedNilai = $data['selectedNilai'];
  
        $dataUpdate = array(
            'ID_JAWAB' => $selectedId,
            'STATUS' => $selectedNilai,
        );
  
        $this->db->where('ID', $id);
        $this->db->update('db_rekammedis.tb_analisis_detail', $dataUpdate); 
  
        if ($this->db->affected_rows() <= 0) {
            $response = array('status' => 'error');
            echo json_encode($response);
        }
      }
      $response = array('status' => 'success');
      echo json_encode($response);
    }
    
  

}
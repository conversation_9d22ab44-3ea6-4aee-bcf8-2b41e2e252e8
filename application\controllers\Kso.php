<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Kso extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if($this->session->userdata('logged_in') == FALSE ){
      redirect('login');  
    }
    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('ModelKso'));
  }

  public function index()
  {
    $data=array(      
      'listkat'        => $this->ModelKso->tampilKategori(),
      'listperusahaan' => $this->ModelKso->listPerusahaan(),
      'listunit'       => $this->ModelKso->listUnit(),
      'listno'         => $this->ModelKso->listnoPks(),

      'isi'            => 'Kso/Index'
    );
    $this->load->view('Layout/Wrapper',$data, FALSE);
  }

public function getlistKso()
  {
    $draw   = intval($this->input->POST("draw"));
    $start  = intval($this->input->POST("start"));
    $length = intval($this->input->POST("length"));

    $listkso = $this->ModelKso->datalistKso();
    $data=array();
    $no =1;
    foreach ($listkso->result() as $field) {

      $proses = '<button type="button" class="btn btn-info btn-sm" title="proses data" onclick="proseskso('.$field->ID_INPUT_PKS.')" style="width:72px"><i class="fa fa-file"></i> Proses</button>';

      $edit = '<button type="button" class="btn btn-warning btn-sm" title="edit data" onclick="editkso('.$field->ID_INPUT_PKS.')" style="width:72px"><i class="fa fa-edit"></i> Edit</button>';

      $hapus = '<button type="button" class="btn btn-danger btn-sm hapusKso" href="javaScript:;" id="hapusKso" title="hapus data" data="'.$field->ID_INPUT_PKS.'" style="width:72px"><i class="fa fa-trash"></i> Hapus</button>';

      $button = '<button type="button" class="btn btn-success btn-sm lihatFile" href="javascript:;" file="'.$field->FILE.'" style="width:72px"><i class="fa fa-eye"></i> Lihat</button>';

      $upload = '<button type="button" class="btn btn-primary btn-sm" title="edit data" onclick="uploadkso('.$field->ID_INPUT_PKS.')" style="width:72px"><i class="fa fa-upload"></i> Upload</button>';

      // $upload = '<button type="button" class="btn btn-warning btn-sm uploadFile" href="javascript:;" file="'.$field->FILE.'"><i class="fa fa-upload"></i> Upload</button>';     

      // $button = '<a class="btn btn-success lihatFile" href="javascript:;" file="'.$field->FILE.'"> <i class="fa fa-eye"></i> Lihat</a>';

      $file = '<div class="input-group-btn">
        <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown">
        <i class="fa fa-file"></i> File
        <span class="fa fa-caret-down"></span></button>
        <ul class="dropdown-menu">
        <li><a class="lihatFile" href="javascript:;" file="'.$field->FILE.'"> Lihat File</a></li>
        <li><a onclick="uploadkso('.$field->ID_INPUT_PKS.')" href="javascript:;"> Upload File</a></li>
        </ul>
        </div>';

      $aksi = '<div class="input-group-btn">
        <button type="button" class="btn btn-warning dropdown-toggle" data-toggle="dropdown">
        <i class="fa fa-edit"></i> Aksi
        <span class="fa fa-caret-down"></span></button>
        <ul class="dropdown-menu">
        <li><a onclick="proseskso('.$field->ID_INPUT_PKS.')" href="javascript:;"> Proses</a></li>
        <li><a onclick="editkso('.$field->ID_INPUT_PKS.')" href="javascript:;"> Edit</a></li>
        <li><a class="hapusKso" href="javaScript:;" data="'.$field->ID_INPUT_PKS.'"> Hapus</a></li>
        </ul>
        </div>';

      $tgl = date_create(date('Y-m-d'));
      $due = date_create($field->DUE_DATE);
      $diff = date_diff($tgl, $due);
      $sisa2 = $diff->format("%R%a");
      $proses = $field->PRO_STATUS;

      if ($proses == 3) {
        $sisa3 = '<span class="badge bg-green"> Selesai </span>';
      } elseif ($sisa2 > 0) {
        if ($sisa2 <= 10) {
          $sisa3 = '<span class="badge bg-yellow">' . $sisa2 . '</span>';
        } else {
          $sisa3 = $sisa2;
        }
      } else {
        $sisa3 = '<span class="badge bg-red">' . $sisa2 . '</span>';
      }

      
      switch ($field->PRO_STATUS) {
        case '1':
          $pro = '<span class="badge bg-aqua">Proses</span>';
          break;
        
        case '2':
          $pro = '<span class="badge bg-yellow">Pending</span>';
          break;

        case '3':
          $pro = '<span class="badge bg-green">Selesai</span>';
          break;

        case '4':
          $pro = '<span class="badge bg-red">Cancel</span>';
          break;

        case '5':
          $pro = '<span class="badge bg-purple">Putus</span>';
          break;
      }

      if($field->NO_PKS_INDUK == ""){
        $add = $field->NO_PKS;
      } else {
        $add = $field->NO_PKS.'<br>'.'add '.$field->NO_PKS_INDUK;
      }
      

      $data[] = array(
        $no,
        $field->PERUSAHAAN, 
        // $field->NO_PKS,
        $add,
        date_indo($field->TGL_BERLAKU),
        date_indo($field->TGL_BERAKHIR),
        $field->KSO,
        $field->UNIT,
        $field->KETERANGAN,
        $field->DUE_DATE,
        $sisa3,
        $pro,
        $file,
        $aksi
      );
      $no++;
    }

    $output = array(
      "draw"            => $draw,
      "recordsTotal"    => $listkso->num_rows(),
      "recordsFiltered" => $listkso->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }

public function simpanKso()
  {
    $post = $this->input->post();
    // echo "<pre>";print_r($post);exit();
    $this->db->trans_begin();

    $tglcreate = date('Y-m-d H:i:s');
                 
    $data_insert = array (
      'ID_PERUSAHAAN'       => $this->input->post('PERUSAHAAN'),
      'ID_KATEGORI'         => 11,
      'NO_PKS'              => $this->input->post('NO_PKS'), 
      'TGL_BERLAKU'         => $this->input->post('TGL_BERLAKU'),
      'TGL_BERAKHIR'        => $this->input->post('TGL_BERAKHIR'),
      'KSO'                 => $this->input->post('KSO'),
      'ID_UNIT'             => $this->input->post('UNIT'),
      'KETERANGAN'          => $this->input->post('KETERANGAN'),
      'KODE_PRODUKSI'       => $this->input->post('KODE_PRODUKSI'),
      'DUE_DATE'            => $this->input->post('DUE_DATE'),
      // 'FILE'             => $this->input->post('FILE_PKS'),
      // 'FILE'                => $berkas['file_name'],
      'CREATED_BY'          => $this->session->userdata('id'),
      'CREATED_DATE'        => $tglcreate,
      'STATUS'              => 1
    );
   // echo "<pre>";print_r($data_insert);exit();

    $this->db->insert('db_pks.tb_input_pks', $data_insert);

    $id_input_pks = $this->db->insert_id();

    $data_insert_proses = array(
      'ID_INPUT_PKS' => $id_input_pks, // Gunakan ID_INPUT_PKS yang sama
      'TGL_PROSES' => $this->input->post('TGL_PROSES'),
      'KET_PROSES' => $this->input->post('PROSES_PERTAMA'),
      'STATUS' => 1
    );

    $this->db->insert('db_pks.tb_proses_pks', $data_insert_proses);

    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
      } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
      }
 
    echo json_encode($result);
    
  }

  public function simpanKsoAdendum()
  {
    $post = $this->input->post();
    // echo "<pre>";print_r($post);exit();
    $this->db->trans_begin();

    $tglcreate = date('Y-m-d H:i:s');
                 
    $data_insert = array (
      'ID_PERUSAHAAN'       => $this->input->post('PERUSAHAAN_ADENDUM'),
      'ID_KATEGORI'         => 11,
      'NO_PKS'              => $this->input->post('NO_PKS_ADENDUM'), 
      'TGL_BERLAKU'         => $this->input->post('TGL_BERLAKU_ADENDUM'),
      'TGL_BERAKHIR'        => $this->input->post('TGL_BERAKHIR_ADENDUM'),
      'KSO'                 => $this->input->post('KSO_ADENDUM'),
      'ID_UNIT'             => $this->input->post('UNIT_ADENDUM'),
      'KETERANGAN'          => $this->input->post('KETERANGAN_ADENDUM'),
      'KODE_PRODUKSI'       => $this->input->post('KODE_PRODUKSI_ADENDUM'),
      'DUE_DATE'            => $this->input->post('DUE_DATE_ADENDUM'),
      // 'FILE'             => $this->input->post('FILE_PKS'),
      // 'FILE'                => $berkas['file_name'],
      'CREATED_BY'          => $this->session->userdata('id'),
      'CREATED_DATE'        => $tglcreate,
      'STATUS'              => 1
    );
   // echo "<pre>";print_r($data_insert);exit();

    $this->db->insert('db_pks.tb_input_pks', $data_insert);

    $id_input_pks = $this->db->insert_id();

    $data_insert_proses = array(
      'ID_INPUT_PKS' => $id_input_pks, // Gunakan ID_INPUT_PKS yang sama
      'TGL_PROSES' => $this->input->post('TGL_PROSES'),
      'KET_PROSES' => $this->input->post('PROSES_PERTAMA'),
      'STATUS' => 1
    );

    $this->db->insert('db_pks.tb_proses_pks', $data_insert_proses);

    $data_insert_adendum = array(
      'ID_INPUT_PKS' => $id_input_pks, // Gunakan ID_INPUT_PKS yang sama
      'NO_PKS_INDUK' => $this->input->post('NO_PKS_INDUK'),
      'NO_PKS_ADENDUM' => $this->input->post('NO_PKS_ADENDUM'),
      'STATUS' => 1
    );

    $this->db->insert('db_pks.tb_adendum', $data_insert_adendum);

    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
      } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
      }
 
    echo json_encode($result);
    
  }

function getUnit()
    {
      $result = $this->ModelKso->listSatker()->result_array();
      $data = array();
      foreach ($result as $row) {
          $sub_array = array();
          $sub_array['id'] = $row['ID'];
          $sub_array['text'] = $row['UNIT'];
          $data[] = $sub_array;
      }
      // $output = array(
      //     "item" -> $data
      // );
      echo json_encode($data);
    }

public function modalUploadkso()
  {
    if($this->input->is_ajax_request()==true){
      $id = $this->input->post('id');
      $ambildata = $this->ModelKso->editlistKso($id);
      if($ambildata->num_rows()>0){
        $row=$ambildata->row_array();
        $data=[
          
          // 'listkat'           => $this->ModelPerusahaan->tampilKategori(),
          'ID_INPUT_PKS'        => $row['ID_INPUT_PKS'],
          // 'ID_PROSES'           => $row['ID_PROSES'],
          'ID_KATEGORI'         => $row['ID_KATEGORI'],
          'ID_PERUSAHAAN'       => $row['ID_PERUSAHAAN'],
          'NO_PKS'              => $row['NO_PKS'],
          'PERUSAHAAN'          => $row['PERUSAHAAN'],
          'KATEGORI'            => $row['KATEGORI'],

        ];
      }
      $msg=[
        'sukses'=>$this->load->view('Kso/modal_upload_kso', $data, true)
      ];
      echo json_encode($msg);
    }
  }

public function UploadKso()
{
    $post = $this->input->post();
    $this->db->trans_begin();
    $id = $this->input->post('ID_INPUT_PKS');
    $tgledit = date('Y-m-d H:i:s');

    // Cek apakah ada file yang diunggah
    if(isset($_FILES['FILE_KSO']) && $_FILES['FILE_KSO']['name'] != "") {
        $config['upload_path'] = FCPATH .'/berkas/';
        $config['allowed_types'] = 'pdf';
        $config['max_size']     = '3000';
        $config['file_name'] = date('YmdHis').$this->input->post('ID_PERUSAHAAN').$this->input->post('ID_KATEGORI').'.pdf';

        $this->upload->initialize($config);
        
        if($this->upload->do_upload('FILE_KSO')) {
            // Jika file berhasil diunggah, tambahkan nama file baru ke dalam data yang akan dimasukkan
            $berkas = $this->upload->data();
            $data_insert['FILE'] = $berkas['file_name'];
        } else {
            // Jika gagal unggah file, atur pesan kesalahan dan kembalikan respon
            $error = array('error' => $this->upload->display_errors());
            echo json_encode(array('status' => 'failed', 'error' => $error));
            $this->db->trans_rollback();
            return;
        }
    }

    // Lakukan pembaruan di database
    $this->db->where('ID_INPUT_PKS', $id);
    $this->db->update('db_pks.tb_input_pks', $data_insert);

    // Periksa status transaksi dan kirimkan respons sesuai dengan itu
    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
    } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
    }

    echo json_encode($result);
}

 public function modalProseskso()
  {
    if($this->input->is_ajax_request()==true){
      $id = $this->input->post('id');
      $ambildata = $this->ModelKso->editlistKso($id);
      if($ambildata->num_rows()>0){
        $row=$ambildata->row_array();
        $data=[
          
          // 'listkat'           => $this->ModelPerusahaan->tampilKategori(),
          'ID_INPUT_PKS'        => $row['ID_INPUT_PKS'],
          'ID_PROSES'           => $row['ID_PROSES'],
          'ID_PERUSAHAAN'       => $row['ID_PERUSAHAAN'],
          'NO_PKS'              => $row['NO_PKS'],
          'PERUSAHAAN'          => $row['PERUSAHAAN'],
          'KATEGORI'            => $row['KATEGORI'],
          'TGL_PROSES'          => $row['TGL_PROSES'],
          'KET_PROSES'          => $row['KET_PROSES'],
          'TGL_PENDING'          => $row['TGL_PENDING'],
          'KET_PENDING'          => $row['KET_PENDING'],
          'TGL_SELESAI'          => $row['TGL_SELESAI'],
          'KET_SELESAI'          => $row['KET_SELESAI'],
          'TGL_CANCEL'          => $row['TGL_CANCEL'],
          'KET_CANCEL'          => $row['KET_CANCEL'],
          'TGL_PUTUS'          => $row['TGL_PUTUS'],
          'KET_PUTUS'          => $row['KET_PUTUS'],

        ];
      }
      $msg=[
        'sukses'=>$this->load->view('Kso/modal_proses_kso', $data, true)
      ];
      echo json_encode($msg);
    }
  }

public function ProsesKso()
  {
    $post = $this->input->post();
    $this->db->trans_begin();
    $id = $this->input->post('ID_PROSES');
    $tgledit = date('Y-m-d H:i:s');

    if($this->input->post('TGL_PUTUS')!=""){
      $status = 5;
    } elseif ($this->input->post('TGL_CANCEL')!=""){
      $status = 4;
    } elseif ($this->input->post('TGL_SELESAI')!=""){
      $status = 3;
    } elseif ($this->input->post('TGL_PENDING')!="") {
      $status = 2;
    }
                
        $data_insert = array (
        'TGL_PENDING'         => $this->input->post('TGL_PENDING') !="" ? $this->input->post('TGL_PENDING') : NULL, 
        'KET_PENDING'         => $this->input->post('KET_PENDING') !="" ? $this->input->post('KET_PENDING') : NULL,
        'TGL_SELESAI'         => $this->input->post('TGL_SELESAI') !="" ? $this->input->post('TGL_SELESAI') : NULL,
        'KET_SELESAI'         => $this->input->post('KET_SELESAI') !="" ? $this->input->post('KET_SELESAI') : NULL,
        'TGL_CANCEL'          => $this->input->post('TGL_CANCEL')  !="" ? $this->input->post('TGL_CANCEL') : NULL,
        'KET_CANCEL'          => $this->input->post('KET_CANCEL') !="" ? $this->input->post('KET_CANCEL') : NULL,
        'TGL_PUTUS'           => $this->input->post('TGL_PUTUS')  !="" ? $this->input->post('TGL_PUTUS') : NULL,
        'KET_PUTUS'           => $this->input->post('KET_PUTUS') !="" ? $this->input->post('KET_PUTUS') : NULL,
        'STATUS'  => $status,
      );
        // echo "<pre>";print_r($data_insert);  

    $this->db->where('db_pks.tb_proses_pks.ID_PROSES', $id);
    $this->db->update('db_pks.tb_proses_pks', $data_insert);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }
      echo json_encode($result);
  }

public function EditKso()
{
    $post = $this->input->post();
    $this->db->trans_begin();
    $id = $this->input->post('ID');
    $tgledit = date('Y-m-d H:i:s');
    $data_insert = array(
        'NO_PKS'        => $this->input->post('NO_PKS_EDIT'), 
        'TGL_BERLAKU'   => $this->input->post('TGL_BERLAKU_EDIT'),
        'TGL_BERAKHIR'  => $this->input->post('TGL_BERAKHIR_EDIT'),
        'KETERANGAN'    => $this->input->post('KETERANGAN_EDIT'),
        'KODE_PRODUKSI' => $this->input->post('KODE_PRODUKSI_EDIT'),
        'DUE_DATE'      => $this->input->post('DUE_DATE_EDIT'),
        'KSO'           => $this->input->post('KSO_EDIT'),
        'ID_UNIT'      => $this->input->post('UNIT_EDIT'),
        'UPDATED_BY'    => $this->session->userdata('id'),
        'UPDATED_DATE'  => $tgledit,
    );

    // Cek apakah ada file yang diunggah
    // if(isset($_FILES['FILE_PKS_EDIT']) && $_FILES['FILE_PKS_EDIT']['name'] != "") {
    //     $config['upload_path'] = FCPATH .'/berkas/';
    //     $config['allowed_types'] = 'pdf';
    //     $config['max_size']     = '3000';
    //     $config['file_name'] = date('YmdHis').$this->input->post('ID_PERUSAHAAN').'.pdf';

    //     $this->upload->initialize($config);
        
    //     if($this->upload->do_upload('FILE_PKS_EDIT')) {
    //         // Jika file berhasil diunggah, tambahkan nama file baru ke dalam data yang akan dimasukkan
    //         $berkas = $this->upload->data();
    //         $data_insert['FILE'] = $berkas['file_name'];
    //     } else {
    //         // Jika gagal unggah file, atur pesan kesalahan dan kembalikan respon
    //         $error = array('error' => $this->upload->display_errors());
    //         echo json_encode(array('status' => 'failed', 'error' => $error));
    //         $this->db->trans_rollback();
    //         return;
    //     }
    // }

    // Lakukan pembaruan di database
    $this->db->where('ID_INPUT_PKS', $id);
    $this->db->update('db_pks.tb_input_pks', $data_insert);

    // Periksa status transaksi dan kirimkan respons sesuai dengan itu
    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
    } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
    }

    echo json_encode($result);
}

public function modalEditkso()
  {
    if($this->input->is_ajax_request()==true){
      $id = $this->input->post('id');
      $ambildata = $this->ModelKso->editlistKso($id);
      if($ambildata->num_rows()>0){
        $row=$ambildata->row_array();
        $data=[
          
          // 'listkat'           => $this->ModelPerusahaan->tampilKategori(),
          'ID_INPUT_PKS'        => $row['ID_INPUT_PKS'],
          'ID_PERUSAHAAN'       => $row['ID_PERUSAHAAN'],
          'NO_PKS'              => $row['NO_PKS'], 
          'TGL_BERLAKU'         => $row['TGL_BERLAKU'],
          'TGL_BERAKHIR'        => $row['TGL_BERAKHIR'],
          'ID_UNIT'             => $row['ID_UNIT'],
          'UNIT'                => $row['UNIT'],
          'KSO'                 => $row['KSO'],
          'KETERANGAN'          => $row['KETERANGAN'],
          'KODE_PRODUKSI'       => $row['KODE_PRODUKSI'],
          'DUE_DATE'            => $row['DUE_DATE'],
          // 'FILE'                => $row['FILE'],
          'PERUSAHAAN'          => $row['PERUSAHAAN'],
          'KATEGORI'            => $row['KATEGORI'],
          'TGL_PROSES'          => $row['TGL_PROSES'],
          'KET_PROSES'          => $row['KET_PROSES'],
          'listunit'            => $this->ModelKso->listUnit(),

        ];
      }
      $msg=[
        'sukses'=>$this->load->view('Kso/modal_edit_kso', $data, true)
      ];
      echo json_encode($msg);
    }
  }

public function HapusKso()
    {
    $this->db->trans_begin();
    $post = $this->input->post();
    $id =$post['ID'];

    if($post['PAR']='deactivated')
    {
      $dataUpdate = array (
          'STATUS'                           => 0,
        );
    }
    // echo "<pre>";print_r($id);exit();
        $this->db->where('db_pks.tb_input_pks.ID_INPUT_PKS', $id);
        $this->db->update('db_pks.tb_input_pks', $dataUpdate);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
    }

































public function simpanInputXXX()
  {
    $post = $this->input->post();
    // echo "<pre>";print_r($post);exit();
    $this->db->trans_begin();

    $tglcreate = date('Y-m-d H:i:s');

    $config['upload_path'] = FCPATH .'/berkas/';
    $config['allowed_types'] = 'pdf';
    $config['max_size']     = '3000';
    $config['file_name'] = date('YmdHis').$this->input->post('PERUSAHAAN').'.pdf';

    $this->upload->initialize($config);
        
    $berkas = $this->upload->data();

    // $now = time();
    // $this->load->library('upload', $config);

    if($this->upload->do_upload('FILE_PKS')){
    //   $berkas = $this->upload->data();
                  
      $data_insert = array (
        'ID_PERUSAHAAN'       => $this->input->post('PERUSAHAAN'),
        'NO_PKS'              => $this->input->post('NO_PKS'), 
        'TGL_BERLAKU'         => $this->input->post('TGL_BERLAKU'),
        'TGL_BERAKHIR'        => $this->input->post('TGL_BERAKHIR'),
        'KETERANGAN'          => $this->input->post('KETERANGAN'),
        'KODE_PRODUKSI'       => $this->input->post('KODE_PRODUKSI'),
        'DUE_DATE'            => $this->input->post('DUE_DATE'),
        // 'FILE'             => $this->input->post('FILE_PKS'),
        'FILE'                => $berkas['file_name'],
        'CREATED_BY'          => $this->session->userdata('id'),
        'CREATED_DATE'        => $tglcreate,
        'STATUS'              => 1
      );
   // echo "<pre>";print_r($data_insert);exit();

    $this->db->insert('db_pks.tb_input_pks', $data_insert);

    $id_input_pks = $this->db->insert_id();

    $data_insert_proses = array(
      'ID_INPUT_PKS' => $id_input_pks, // Gunakan ID_INPUT_PKS yang sama
      'TGL_PROSES' => $this->input->post('TGL_PROSES'),
      'KET_PROSES' => $this->input->post('PROSES_PERTAMA'),
      'STATUS' => 1
    );

    $this->db->insert('db_pks.tb_proses_pks', $data_insert_proses);

      if ($this->db->trans_status() === false) {
          $this->db->trans_rollback();
          $result = array('status' => 'failed');
      } else {
          $this->db->trans_commit();
          $result = array('status' => 'success');
      }

    } else {
      //   $result = array('status' => 'noupload');
    }
    
    echo json_encode($result);
    
  } 




  public function UpdateInputXXX()
  {
    $post = $this->input->post();
    $this->db->trans_begin();
    $id = $this->input->post('ID');
    $tgledit = date('Y-m-d H:i:s');

    if($_FILES['FILE_PKS_EDIT']['name'] == ""){

      $data_insert = array (
        'NO_PKS'              => $this->input->post('NO_PKS_EDIT'), 
        'TGL_BERLAKU'         => $this->input->post('TGL_BERLAKU_EDIT'),
        'TGL_BERAKHIR'        => $this->input->post('TGL_BERAKHIR_EDIT'),
        'KETERANGAN'          => $this->input->post('KETERANGAN_EDIT'),
        'KODE_PRODUKSI'       => $this->input->post('KODE_PRODUKSI_EDIT'),
        'DUE_DATE'            => $this->input->post('DUE_DATE_EDIT'),
        'UPDATED_BY'          => $this->session->userdata('id'),
        'UPDATED_DATE'        => $tgledit,
      );
    // echo "<pre>";print_r($data_insert);

    } else {

      $config['upload_path'] = FCPATH .'/berkas/';
      $config['allowed_types'] = 'pdf';
      $config['max_size']     = '3000';
      $config['file_name'] = date('YmdHis').$this->input->post('ID_PERUSAHAAN').'.pdf';

      $this->upload->initialize($config);
      $berkas = $this->upload->data();

      if($this->upload->do_upload('FILE_PKS_EDIT')){

        $data_insert = array (
          'NO_PKS'              => $this->input->post('NO_PKS_EDIT'), 
          'TGL_BERLAKU'         => $this->input->post('TGL_BERLAKU_EDIT'),
          'TGL_BERAKHIR'        => $this->input->post('TGL_BERAKHIR_EDIT'),
          'KETERANGAN'          => $this->input->post('KETERANGAN_EDIT'),
          'KODE_PRODUKSI'       => $this->input->post('KODE_PRODUKSI_EDIT'),
          'DUE_DATE'            => $this->input->post('DUE_DATE_EDIT'),
          'FILE'                => $berkas['file_name'],
          'UPDATED_BY'          => $this->session->userdata('id'),
          'UPDATED_DATE'        => $tgledit,
        );
        // echo "<pre>";print_r($data_insert);
    

        $this->db->where('db_pks.tb_input_pks.ID_INPUT_PKS', $id);
        $this->db->update('db_pks.tb_input_pks', $data_insert);

        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
        } else {
          $this->db->trans_commit();
          $result = array('status' => 'success');
        }
      }
      echo json_encode($result);
    }
  }



  

 


public function tampilPerusahaan()
  {
    $this->load->view('ModelInput/getKategori');
  }

  function getdatakategori()
  {
    $id_perusahaan = $this->input->post('perusahaan');

    $getdatakat = $this->ModelInput->getdatakat($id_perusahaan);

    echo json_encode($getdatakat);
    // var_dump($getdatakat);
  } 














  public function modalTambahPerusahaan()
  {
    $this->load->view('Perusahaan/modal_tambah_perusahaan');
  }

  public function getKategori()
  {
    $result = $this->ModelPerusahaan->listKategori()->result_array();
    $data = array();
    foreach ($result as $row) 
    {
      $sub_array = array();
      $sub_array['id'] = $row['ID_KATEGORI'];
      $sub_array['text'] = $row['KATEGORI'];
      $data[] = $sub_array;
    }

    echo json_encode($data);
  }
   



    



    

}
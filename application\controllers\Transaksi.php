<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class <PERSON>aksi extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if($this->session->userdata('logged_in') == FALSE ){
      redirect('login');  
    }
    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('ModelAnggaran'));
  }

  public function index()
  {
    $data=array(      
      'satuan'    => $this->ModelAnggaran->satuan(),
      'program'   => $this->ModelAnggaran->program(),
      'isi'       => 'Transaksi/Index'
    );
    $this->load->view('Layout/Wrapper',$data, FALSE);
  }

  function pilih_data()
  {
    $data = array(
      'PROGRAM'       => $this->input->post('PROGRAM'),
      'KODE'          => $this->input->post('KODE'),
      'RINCIAN'       => $this->input->post('RINCIAN'),
      'VOLUME'        => $this->input->post('VOLUME'),
      'SATUAN'        => $this->input->post('SATUAN'),
      'HARGASATUAN'   => $this->input->post('HARGASATUAN'),
      'OLEH'          => $this->session->userdata("id"),
    );
    // echo "<pre>";print_r($data);exit();
    $this->db->insert('anggaran.anggarantemp',$data);
    echo $this->tampil_data();
  }

  function tampil_data()
  {
    $output = '';
    $no = 0;
    $data_input = $this->ModelAnggaran->Tampil_data_temp();
    foreach ($data_input as $data) {
      $no++;
      $output .= '
      <tr>
      <td>' . $no++ . '</td>

      <td>' . $data['RINCIAN'] . '</td>
      <td>' . $data['VOLUME'] . '</td>
      <td>' . $data['SATUANNYA'] . '</td>
      <td>' . $data['HARGASATUAN'] . '</td>
      <td><button type="button" id="' . $data['ID'] . '" class="hapus_cart btn btn-danger btn-sm"><i class="fa fa-ban"></i></button></td>
      </tr>
      ';
    }
    return $output;
  }

  function list_data()
  {
    echo $this->tampil_data();
  }

  function hapus_data()
  {
    $id = $this->input->post('row_id');
    $this->db->where('anggaran.anggarantemp.ID',$id);
    $this->db->delete('anggaran.anggarantemp');
    echo $this->tampil_data();
  }

  function simpan_final()
  {
    $data_tmp = $this->ModelAnggaran->tampilkan_temp()->result_array();
    foreach ($data_tmp as $a) 
      $data=array(
        'PROGRAM'      =>$a['PROGRAM'],
        'KODE'         =>$a['KODE'],
        'OLEH'         =>$a['OLEH'],
      );
    $this->db->insert('anggaran.transaksi', $data);
    $insert_id = $this->db->insert_id();
    foreach ($data_tmp as $a) {
      $data2=array(
        'IDPROGRAM'   =>$insert_id,
        'RINCIAN'     =>$a['RINCIAN'],
        'VOLUME'      =>$a['VOLUME'],
        'SATUAN'      =>$a['SATUAN'],
        'HARGASATUAN' =>$a['HARGASATUAN'],
      );
    // echo "<pre>";print_r($data2);exit();
      $this->db->insert('anggaran.transaksidetil', $data2);
    }
    $id = $this->session->userdata('id');
    $this->ModelAnggaran->Hapus_tempo_all($id);
    redirect('Transaksi');
  }

  public function listanggaran()
  {
    $dataanggaran = $this->ModelAnggaran->listanggaran();
    $data=array();
    $no =1;
    foreach ($dataanggaran as  $field) {

      $tombolproses= "<button type=\"button\" class=\"btn btn-success btn-sm\" title=\"Lihat File\" onclick=\"inputrealisasi('".$field->ID."')\">
      <i class=\"fa fa-edit\"></i>
      <span> Input Realisasi</span>
      </button>";

      $row = array();
      $row[] = $no++;
      $row[] = $field->TGLINPUT;
      $row[] = $field->PROGRAM;
      $row[] = number_format($field->TOTAL, 0, ",", ".");
      $row[] = $tombolproses;
      $data[] = $row; 
    }

    if ($dataanggaran) {
      echo json_encode(array('data'=> $data));
    }else{
      echo json_encode(array('data'=>0));
    }
  }

  public function hislistanggaran()
  {
    $hislistanggaran = $this->ModelAnggaran->hislistanggaran();
    $data=array();
    $no =1;
    foreach ($hislistanggaran as  $field) {

      $tombolproses= "<button type=\"button\" class=\"btn btn-success btn-sm\" title=\"Lihat File\" onclick=\"editrealisasi('".$field->ID."')\">
      <i class=\"fa fa-edit\"></i>
      <span> Edit</span>
      </button>";

      $row = array();
      $row[] = $no++;
      $row[] = $field->TGLINPUT;
      $row[] = $field->KODE;
      $row[] = $field->PROGRAM;
      $row[] = number_format($field->TOTAL, 0, ",", ".");
      $row[] = $tombolproses;
      $data[] = $row; 
    }

    if ($hislistanggaran) {
      echo json_encode(array('data'=> $data));
    }else{
      echo json_encode(array('data'=>0));
    }
  }

  public function ModalRealisasi()
  {
    if ($this->input->is_ajax_request() == true){

      $idkegiatan   = $this->input->POST('idkegiatan');
      $ambildata    = $this->ModelAnggaran->getrincian($idkegiatan);
      $header       = $this->ModelAnggaran->tampil_data($idkegiatan);

      $data     = array(
        'rincian' => $ambildata,
        'header'  => $header
      );

      $msg=[
        'sukses'=> $this->load->view('Transaksi/ModalRealisasi',$data,true)
      ];
      echo json_encode($msg);
    }
  }

  public function ModalEditRealisasi()
  {
    if ($this->input->is_ajax_request() == true){

      $idkegiatan   = $this->input->POST('idkegiatan');
      $ambildata    = $this->ModelAnggaran->datauntukedit($idkegiatan);
      $header       = $this->ModelAnggaran->tampil_data($idkegiatan);

      $data     = array(
        'rincian' => $ambildata,
        'header'  => $header
      );

      $msg=[
        'sukses'=> $this->load->view('Transaksi/ModalEditRealisasi',$data,true)
      ];
      echo json_encode($msg);
    }
  }

  public function simpanrealisasi()
  {
    $rules = $this->ModelAnggaran->rules;
    $this->form_validation->set_rules($rules);

    if ($this->form_validation->run() == TRUE) {
      $post = $this->input->post();
      $IDPROGRAM     = $this->input->post('IDPROGRAM');
      $this->db->trans_begin();
      for ($i = 0; $i < count($post['ID']); $i++) 
      {
        $data=array(
          'IDRINCIAN'       => $post['ID'][$i],
          'VOLREAUSULAN'    => $post['VOLREAUSULAN'][$i],
          'JUMREAUSULAN'    => $post['JUMREAUSULAN'][$i],
          'VOLREABAYAR'     => $post['VOLREABAYAR'][$i],
          'JUMREABAYAR'     => $post['JUMREABAYAR'][$i]);
        $this->db->insert('anggaran.realisasi',$data);
      }

      $final = array (
        'STATUS'   => 2,
      );

      $this->db->where('anggaran.transaksi.ID',$IDPROGRAM);
      $this->db->update('anggaran.transaksi',$final);

      if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
      } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
      }
    } else {
      $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
    }
    echo json_encode($result);
  }

  public function updaterealisasi()
  {
    $rules = $this->ModelAnggaran->rules;
    $this->form_validation->set_rules($rules);

    if ($this->form_validation->run() == TRUE) {
      $post           = $this->input->post();
      $idrealisasi    = $this->input->post('IDREALISASI');
      $this->db->trans_begin();
      $result = array();
      foreach ($idrealisasi as $key => $val) {
        $result[] = array(
          "ID"      => $idrealisasi[$key],
          "VOLREAUSULAN"  => $_POST['VOLREAUSULAN'][$key],
          "JUMREAUSULAN"  => $_POST['JUMREAUSULAN'][$key],
          "VOLREABAYAR"   => $_POST['VOLREABAYAR'][$key],
          "JUMREABAYAR"   => $_POST['JUMREABAYAR'][$key],
          "SISAVOLUME"    => $_POST['SISAVOLUME'][$key],
          "SISAANGGARAN"  => $_POST['SISAANGGARAN'][$key],
        );
      }
      $this->db->update_batch('anggaran.realisasi', $result, 'ID');

      if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
      } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
      }
    } else {
      $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
    }
    echo json_encode($result);
  }


}
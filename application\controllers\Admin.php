<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Admin extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if($this->session->userdata('logged_in') == FALSE ){
      redirect('login');  
    }
    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('ModelAdmin'));
  }

  public function index_jkn(){    
    $data=array(   
      'isi'            => 'CoderAdmin/IndexJkn'
    );
    $this->load->view('Layout/Wrapper',$data);
  }  
  public function dataJKN() { 
    $draw   = intval($this->input->POST("draw")); 
    $start  = intval($this->input->POST("start")); 
    $length = intval($this->input->POST("length")); 
 
    $pulang = $this->input->post('pulang'); 
    $jenis = $this->input->post('jenis'); 
 
    if ($jenis == 1) { 
        $listJKN = $this->ModelAdmin->datatablesJKN1($pulang); 
    } else { 
        $listJKN = $this->ModelAdmin->datatablesJKN2($pulang); 
    } 
 
    if ($listJKN) {  
        $data = array(); 
        $no = 1; 
         
        foreach ($listJKN->result() as $LJ) { 
            $data[] = array( 
                $LJ->NOSEP, 
                $LJ->NORM, 
                $LJ->NAMALENGKAP, 
                date('d/m/Y', strtotime($LJ->TANGGAL_PENDAFTARAN)), 
    			date('d/m/Y', strtotime($LJ->TANGGAL_PULANG)), 
                $LJ->TANGGAL_GROUPING, 
                $LJ->NOPEN, 
                $LJ->RUANGAN, 
                $LJ->RUANG_RAWAT ? $LJ->RUANG_RAWAT. ' (' .$LJ->LAMA_DI_IGD . ')' : '-', 
            ); 
        } 
 
        $output = array( 
            "draw"            => $draw, 
            "recordsTotal"    => $listJKN->num_rows(), 
            "recordsFiltered" => $listJKN->num_rows(), 
            "data"            => $data 
        ); 
        echo json_encode($output); 
    } else { 
        echo json_encode(array("draw" => $draw, "recordsTotal" => 0, "recordsFiltered" => 0, "data" => [])); 
    } 
}





    

}
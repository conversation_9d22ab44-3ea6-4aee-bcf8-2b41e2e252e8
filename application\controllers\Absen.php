<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class <PERSON><PERSON>en extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    // if($this->session->userdata('logged_in') == FALSE ){
    //   redirect('login');
    // }
    date_default_timezone_set("Asia/Bangkok");
    // $this->load->model(array('profileModel','MasterModel','ModelIjoc'));
  }

  public function index()
  {
    $data = array(
      'title'         => 'Data IJOC',
      // 'isi'           => 'Index',
    );
    $this->load->view('Index',$data);
  }

  public function listijoc()
  {
    $draw   = intval($this->input->get("draw"));
    $start  = intval($this->input->get("start"));
    $length = intval($this->input->get("length"));
    $lisijoc = $this->ModelIjoc->listijoc();
// echo "<pre>";print_r($listPegawai);exit();
    $data  = array();
    $no    =1;
    foreach($lisijoc->result() as $mp) {

     $edit = '<button type="button" class="btn btn-success btn-xs" title="edit data" onclick="btneditijoc('.$mp->ID.')">
     <i class="far fa-edit"></i> Edit
     </button>';
     $verif = '<button type="button" class="btn btn-info btn-xs verifijoc" title="Verif data" data-id="'.$mp->ID.'"><i class="far fa-check-square"></i> Verif</button>';

     $data[] = array(
      $no,
      $mp->NAMADOKTER,
      $mp->KETERANGAN_IJOC,
      $mp->JENISIJOC,
      $mp->RUPIAHNYA,
      $mp->TANGGAL,
      $edit.' '.$verif

    );
     $no++;
   }

   $output = array(
    "draw"            => $draw,
    "recordsTotal"    => $lisijoc->num_rows(),
    "recordsFiltered" => $lisijoc->num_rows(),
    "data"            => $data
  );
   echo json_encode($output);
 }

 public function simpan($param)
 {
  if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
    if ($param == 'tambah' || $param == 'ubah') {
      $post = $this->input->post();
        // echo "<pre>";print_r($post);exit();
      $this->db->trans_begin();

      $data_insert = array (
        'DOKTER'          => $this->input->post('DOKTER'), 
        'TGL_PRAKTIK'     => $this->input->post('TGL_PRAKTIK'),
        'RUANGAN'         => $this->input->post('RUANGAN'),
        'JADWAL'          => $this->input->post('JADWAL'),  
        'IDJADWAL'        => $this->input->post('IDJADWAL'), 
        'KETERANGAN'      => $this->input->post('KETERANGAN'),  
        'OLEH'            => $this->session->userdata("id")
      );
   // echo "<pre>";print_r($data_insert);exit();

      $this->db->insert('absendokter.kehadiran', $data_insert);

      if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
      } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
      }
      echo json_encode($result);
    }
  }
}

public function ModalEditijoc()
{
  if ($this->input->is_ajax_request() == true){
    $idijoc        = $this->input->POST('id',true);
    $dokter        = $this->MasterModel->listDr();
    $editor        = $this->MasterModel->listEditor();
    $ambildata     = $this->ModelIjoc->Ambildata($idijoc);
    if($ambildata->num_rows()>0){
      $row=$ambildata->row_array();
      $data=[   
       'ID'               => $row['ID'],
       'dokter'           => $dokter,
       'editor'           => $editor,
       'KETERANGAN_IJOC'  => $row['KETERANGAN_IJOC'],
       'JENIS_IJOC'       => $row['JENIS_IJOC'],
       'RUPIAH'           => $row['RUPIAH'],
       'ID_DOKTER'         => $row['ID_DOKTER'],
     ];
   }
   $msg=[
    'sukses'=> $this->load->view('Ijoc/ModalEditijoc',$data,true)
  ];
  echo json_encode($msg);
}
}

public function editijoc($param)
{
  if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
    if ($param == 'tambah' || $param == 'ubah') {
      $post = $this->input->post();

        // echo "<pre>";print_r($post);exit();
      $this->db->trans_begin();

      $dataedit = array (
       'ID_DOKTER'        => $this->input->post('DOKTER'), 
       'KETERANGAN_IJOC'  => $this->input->post('KETERANGAN_IJOC'),
       'JENIS_IJOC'       => $this->input->post('JENIS_IJOC'),
       'RUPIAH'           => $this->input->post('RUPIAH'),
     );

      $this->db->where('db_remunmedis.tb_ijoc.ID',$this->input->post('ID'));
      $this->db->update('db_remunmedis.tb_ijoc',$dataedit);

      if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
      } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
      }
      echo json_encode($result);
    }
  }
}

public function verifijoc($param)
{
  if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
    if ($param == 'tambah' || $param == 'ubah') {
      $post = $this->input->post();
      $id = $this->input->post('id');
        // echo "<pre>";print_r($post);exit();
      $this->db->trans_begin();
      $tglverif = date('Y-m-d H:i:s');
      $verif = array (
       'VERIF_OLEH'       => $this->session->userdata("id"),
       'TANGGAL_VERIF'    => $tglverif,  
       'STATUS_VERIF'     => 1, 
     );

      $this->db->where('db_remunmedis.tb_ijoc.ID',$id);
      $this->db->update('db_remunmedis.tb_ijoc',$verif);

      if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
      } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
      }
      echo json_encode($result);
    }
  }
}

function getrupiah()
{
  $id = $this->input->post('id');
  $data = $this->ModelIjoc->getrupiah($id);
  echo json_encode($data);
}

}
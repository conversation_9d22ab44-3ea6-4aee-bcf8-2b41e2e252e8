<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class CoderAdmin extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    $this->load->library('Excel');
    if($this->session->userdata('logged_in') == FALSE ){
      redirect('login');  
    }
    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('ModelCoderAdmin'));
    $this->db2= $this->load->database('dummy', true);
  }

  public function index()
  {
    $data=array(      
      // 'listkat'        => $this->ModelInput->tampilKategori(),
      // 'listperusahaan' => $this->ModelInput->listPerusahaan(),
      // 'listno'         => $this->ModelInput->listnoPks(),
      'isi'            => 'CoderAdmin/Index'
    );
    $this->load->view('Layout/Wrapper',$data);
  }

public function getListdata()
  {
    // $draw   = intval($this->input->POST("draw"));
    // $start  = intval($this->input->POST("start"));
    // $length = intval($this->input->POST("length"));

    // $button = '<div class="btn-group">
    //               <button type="button" class="btn btn-light dropdown-toggle waves-effect" data-toggle="dropdown" aria-expanded="false">
    //                   <i class="mdi mdi-folder font-18 vertical-middle"></i>
    //                   <i class="mdi mdi-chevron-down"></i>
    //               </button>
    //               <div class="dropdown-menu">
    //                   <a class="dropdown-item" href="javascript: void(0);">Detail</a>
    //                   <a class="dropdown-item" href="javascript: void(0);">Edit</a>
    //                   <a class="dropdown-item" href="javascript: void(0);">Hapus</a>
    //               </div>
    //           </div>';

    $listdata = $this->ModelCoderAdmin->dataList();
    $data=array();
    $no =1;
    foreach ($listdata->result() as $field) {
      // $hapus = '<button type="button" class="btn btn-danger btn-rounded btn-sm hapus" onclick="hapus(\'' . $field->ID . '\', \'' . $field->nama_lengkap . '\')" title="hapus tugas coder" style="width:72px"><i class="fa fa-trash"></i></button>';
      $disabled = ($field->JUMLAH == 0) ? 'disabled' : '';
      $hapus = '<button type="button" class="btn btn-danger btn-rounded btn-sm hapus" onclick="hapus(\'' . $field->ID . '\', \'' . $field->nama_lengkap . '\')" title="hapus tugas coder" style="width:72px" ' . $disabled . '><i class="fa fa-trash"></i></button>';
      $data[] = array(
        'DT_RowClass' => $field->row_class,
        $no,
        date_format(date_create($field->TANGGAL_TARIK),'d-m-Y'),
        date_format(date_create($field->TANGGAL_PENDAFTARAN),'d-m-Y'),
        $field->nama_lengkap,
        $field->DESKRIPSI,
        $field->JUMLAH,
        $hapus
      );
      $no++;

    }

    $output = array(
      // "draw"            => $draw,
      // "recordsTotal"    => $listdata->num_rows(),
      // "recordsFiltered" => $listdata->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }
  public function hapusTugas(){
    $id = $this->input->post('id');
    $this->db->set('status', 0);
    $this->db->where('ID', $id);
    $this->db->update('db_rekammedis.tb_coding');

    $this->db->set('status', 0);
    $this->db->where('ID_CODING', $id);
    $this->db->update('db_rekammedis.tb_coding_detail');
    $response = array('status' => 'success');
    echo json_encode($response);
  }

public function list_ruangan()
    {
        $result = $this->ModelCoderAdmin->list_Ruangan();
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row['ID'];
            $sub_array['text'] = $row['DESKRIPSI'];
            $data[] = $sub_array;
        }
        echo json_encode($data);
    }

public function list_coder()
    {
        $result = $this->ModelCoderAdmin->list_Coder();
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row['id'];
            $sub_array['text'] = $row['nama_lengkap'];
            $data[] = $sub_array;
        }
        echo json_encode($data);
    }

public function get_results($val, $qt)
  {
    $division = intdiv($val, $qt); // PHP <7: $division = ($val - ($val % $qt)) / $qt;
    $ret = array_fill(0, $qt, $division); // fill array with $qt equal values

    if($division != $val / $qt) // if not whole division, add remaning to lsat element
    { 
      $ret[count($ret)-1] = $ret[0] + ($val % $qt);
    }

    return $ret;
  }
   
public function TarikDataCoding()
{
  if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {

  $TANGGAL = $_POST['TANGGAL'];
  $tgl_awal = $TANGGAL.' '.'00:00:00';
  $tgl_akhir = $TANGGAL.' '.'23:59:59';
  $ruangan1 =$_POST['RUANGAN'];
  $ID_PETUGAS = $_POST['CODER'];
  $jmlpetugas = count($_POST['CODER']);

  
  $result=mysqli_query($conn1,$sql); 
              // echo "<pre>";print_r($result);exit();
  $JUMLAH = $result->num_rows;

  $hasil = get_results($JUMLAH, $jmlpetugas);

  $listdata = $this->ModelCoderAdmin->TarikData();
  $data=array();
  $no =1;
    foreach ($listdata->result() as $field) {

      $data[] = array(
        $no,
        $field->NORM, 
        $field->NAMA_PASIEN,
        // date_format($date,'Y-m-d'),
        $field->TANGGAL_PENDAFTARAN,
        $field->DESKRIPSI,
        $edit        
      );
      $no++;

    }

    $output = array(
      // "draw"            => $draw,
      // "recordsTotal"    => $listdata->num_rows(),
      // "recordsFiltered" => $listdata->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }
}

public function simpanTarik_X()
  { 
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {

    $post = $this->input->post();

    $this->db->trans_begin();

    // $par1 = 'tarik';
    $TANGGAL = $this->input->post('TANGGAL');
    // $tgl_tarik = date('Y-m-d');
    $tgl_awal = $TANGGAL.' '.'00:00:00';
    $tgl_akhir = $TANGGAL.' '.'23:59:59';
    $ruangan1 = $this->input->post('RUANGAN');
    $ID_PETUGAS = $this->input->post('CODER');
    $jmlpetugas = count($this->input->post('CODER'));

    $datatarik = $this->ModelCoderAdmin->TarikData($tgl_awal,$tgl_akhir,$ruangan1);
    $JUMLAH = $datatarik->num_rows();
    $hasil = $this->get_results($JUMLAH, $jmlpetugas);

    // $post = $this->input->post();
    // echo "<pre>";print_r($datatarik);
    // echo "<pre>";print_r($JUMLAH);
    // echo "<pre>";print_r($hasil);

    $no = 0;
    foreach($ID_PETUGAS as $key => $value){
      foreach($hasil as $keys => $values){
        if($key == $keys){
          if($keys == 0){
            $offset = 0;
          }else{
            $offset = $hasil[$keys-1]*$no;
          }

          $data_insert = array (
        // 'TANGGAL_TARIK' => $tgl_tarik,
        'TANGGAL_PENDAFTARAN' => $TANGGAL,
        'ID_PETUGAS' => $value,
        'ID_RUANGAN' => $ruangan1, 
        'JUMLAH' => $values,
        'STATUS' => 1
        );
        // echo "<pre>";print_r($data_insert);
        $this->db->insert('db_rekammedis.tb_coding', $data_insert);

        $idcoding = $this->db->insert_id();

        // $par2 = 'offset';
        $getData = $this->ModelCoderAdmin->TarikDataLagi($tgl_awal,$tgl_akhir,$ruangan1,$values,$offset)->result_array();
        $dataCodingDetail = array();
        $index = 0; // Set index array awal dengan 0
        foreach($getData as $row){ // Kita buat perulangan berdasarkan nis sampai data terakhir
        array_push($dataCodingDetail, array(
            'ID_CODING'           => $idcoding,
            'NORM'                => $row['NORM'],
            // 'STATUS'              => $row['STATUS'],
            // 'DONE_DATE'           => $row['DONE_DATE'],
            // 'DONE_BY'             => $row['DONE_BY'],
            'NAMA_PASIEN'         => $row['NAMA_PASIEN'],
            'NOPEN'               => $row['NOPEN'],
            'TANGGAL_PENDAFTARAN' => $row['TANGGAL_PENDAFTARAN'],
            'TANGGAL_LAYANAN'     => $row['TANGGAL_LAYANAN'],
            'CARABAYAR'           => $row['CARABAYAR'],
            'SEP'                 => $row['SEP'],
            'RUANGAN'             => $row['RUANGAN'],
            'PETUGAS'             => $row['PETUGAS'],
            'STATUS_TAGIHAN'      => $row['STATUS_TAGIHAN'],
            'INSTALASI'           => $row['INSTALASI'],
            'NO_SEP'              => $row['NO_SEP'],
            'PETUGASFINALLAYANAN' => $row['PETUGASFINALLAYANAN'],
            'DPJP'                => $row['DPJP'],
            'PETUGASTAGIHAN'      => $row['PETUGASTAGIHAN'],
            'PETUGASCOSTING'      => $row['PETUGASCOSTING'],
            'TANGGAL_COSTING'     => $row['TANGGAL_COSTING'],
            'TOTAL_COSTING'       => $row['TOTAL_COSTING'],            
            'IDTAGIHAN'           => $row['IDTAGIHAN'],
            'STATUS_COSTING'      => $row['STATUS_COSTING'],
            'HASIL_RAD'           => $row['HASIL_RAD'],
          ));
          $index++;

        }
        // echo "<pre>";print_r($dataCodingDetail);exit();
        $this->db->insert_batch('db_rekammedis.tb_coding_detail', $dataCodingDetail);
        }
      }
    }
    // exit();
                 
    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
      } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
      }
 
    echo json_encode($result);
    
  }
    
}
public function CekData()
{
    $post = $this->input->post();
    $this->db->trans_begin();

    $TANGGAL = $this->input->post('TANGGAL');
    $ruangan1 = $this->input->post('RUANGAN');

    // Cek apakah data sudah pernah ditarik
    $cek = $this->ModelCoderAdmin->cek_data($TANGGAL, $ruangan1);
    $cek_jumlah = $cek->num_rows();

    if ($cek_jumlah > 0) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed', 'message' => 'Data sudah pernah ditarik');
    } else {
        $tgl_tarik = date('Y-m-d');
        $tgl_awal = $TANGGAL . ' ' . '00:00:00';
        $tgl_akhir = $TANGGAL . ' ' . '23:59:59';
        $ID_PETUGAS = $this->input->post('CODER');
        $jmlpetugas = count($ID_PETUGAS);

        $datatarik = $this->ModelCoderAdmin->TarikData($tgl_awal, $tgl_akhir, $ruangan1);

        if ($datatarik->num_rows() == 0) {
            // Data kosong
            $this->db->trans_rollback();
            $result = array('status' => 'failed', 'message' => 'Data kosong');
        } else {
            $JUMLAH = $datatarik->num_rows();
            $hasil = $this->get_results($JUMLAH, $jmlpetugas);

            $no = 0;
            foreach ($ID_PETUGAS as $key => $value) {
                foreach ($hasil as $keys => $values) {
                    if ($key == $keys) {
                        $offset = ($keys == 0) ? 0 : $hasil[$keys - 1] * $no;

                        $data_insert = array(
                            'TANGGAL_TARIK' => $tgl_tarik,
                            'TANGGAL_PENDAFTARAN' => $TANGGAL,
                            'ID_PETUGAS' => $value,
                            'ID_RUANGAN' => $ruangan1,
                            'JUMLAH' => $values,
                            'STATUS' => 1
                        );
                        $this->db->trans_start();

                        $this->db->insert('db_rekammedis.tb_coding', $data_insert);
                        $idcoding = $this->db->insert_id();

                        $getData = $this->ModelCoderAdmin->TarikDataLagi($tgl_awal, $tgl_akhir, $ruangan1, $values, $offset)->result_array();
                        $dataCodingDetail = array();

                        foreach ($getData as $row) {
                            $dataCodingDetail[] = array(
                                'ID_CODING' => $idcoding,
                                'NORM' => $row['NORM'],
                                'ID_PETUGAS' => $value,
                                'NAMA_PASIEN' => $row['NAMA_PASIEN'],
                                'NOPEN' => $row['NOPEN'],
                                'TANGGAL_PENDAFTARAN' => $row['TANGGAL_PENDAFTARAN'],
                                'TANGGAL_LAYANAN' => $row['TANGGAL_LAYANAN'],                                
                                'CARABAYAR' => $row['CARABAYAR'],
                                'SEP' => $row['SEP'],
                                'RUANGAN' => $row['RUANGAN'],
                                'PETUGAS' => $row['PETUGAS'],
                                'STATUS_TAGIHAN' => $row['STATUS_TAGIHAN'],
                                'DPJP' => $row['DPJP'],
                                'PETUGASTAGIHAN' => $row['PETUGASTAGIHAN'],
                                'PETUGASCOSTING' => $row['PETUGASCOSTING'],
                                'TANGGAL_COSTING' => $row['TANGGAL_COSTING'],
                                'TOTAL_COSTING' => $row['TOTAL_COSTING'],
                                'IDTAGIHAN'           => $row['IDTAGIHAN'],
                                'STATUS_COSTING' => $row['STATUS_COSTING'],
                                'HASIL_RAD' => $row['HASIL_RAD'],
                            );
                        }

                        $this->db->insert_batch('db_rekammedis.tb_coding_detail', $dataCodingDetail);

                        $this->db->trans_complete(); // Menyelesaikan transaksi

                        if ($this->db->trans_status() === false) {
                            $this->db->trans_rollback();
                            $result = array('status' => 'failed', 'message' => 'Terjadi kesalahan saat menyimpan data');
                            break 2; // Keluar dari loop kedua jika ada kesalahan
                        }

                        $no++;
                    }
                }
            }

            if ($this->db->trans_status() !== false) {
                $this->db->trans_commit();
                $result = array('status' => 'success', 'message' => 'Data Berhasil Disimpan');
            }
        }
    }

    echo json_encode($result);
}


// public function CekData()
//   {
//     $post = $this->input->post();
//     // echo "<pre>";print_r($post);exit();
//     $this->db->trans_begin();

//     $TANGGAL = $this->input->post('TANGGAL');
//       $ruangan1 = $this->input->post('RUANGAN');

//       $cek = $this->ModelCoderAdmin->cek_data($TANGGAL,$ruangan1);
//       $cek_jumlah = $cek->num_rows();

//       // 
//       if ($cek_jumlah > 0) {
//         $this->db->trans_rollback();
//         $result = array('status' => 'failed');
//       }else {
//         $this->simpanTarik();

//         if ($this->db->trans_status() === false) {
//         $this->db->trans_rollback();
//         $result = array('status' => 'failed');
//         } else {
//         $this->db->trans_commit();
//         $result = array('status' => 'success');
//         }
//       }
//     echo json_encode($result);
//   }
// public function simpanTarik()//PAKAI INI
// {
//     if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {

//         $post = $this->input->post();

//         $tgl_tarik = date('Y-m-d');
//         $TANGGAL = $this->input->post('TANGGAL');
//         $tgl_awal = $TANGGAL . ' ' . '00:00:00';
//         $tgl_akhir = $TANGGAL . ' ' . '23:59:59';
//         $ruangan1 = $this->input->post('RUANGAN');
//         $ID_PETUGAS = $this->input->post('CODER');
//         $jmlpetugas = count($this->input->post('CODER'));

//         // $cek = $this->ModelCoderAdmin->cek_data($TANGGAL, $ruangan1);
//         // $cek_jumlah = $cek->num_rows();

//         // if ($cek_jumlah > 0) {
//         //     $this->session->set_flashdata('message', 'Data sudah ada!');
//         //     return array('status' => 'failed');
//         // } else {

//         $datatarik = $this->ModelCoderAdmin->TarikData($tgl_awal, $tgl_akhir, $ruangan1);
//         if ($datatarik->num_rows() == 0) {
//           $this->db->trans_rollback(); 
//           return array('status' => 'failed');
//         } else{

//         $JUMLAH = $datatarik->num_rows();
//         $hasil = $this->get_results($JUMLAH, $jmlpetugas);

//         $no = 0;
//         foreach ($ID_PETUGAS as $key => $value) {
//             foreach ($hasil as $keys => $values) {
//                 if ($key == $keys) {

//                     $offset = ($keys == 0) ? 0 : $hasil[$keys - 1] * $no;

//                     $data_insert = array(
//                         'TANGGAL_TARIK' => $tgl_tarik,
//                         'TANGGAL_PENDAFTARAN' => $TANGGAL,
//                         'ID_PETUGAS' => $value,
//                         'ID_RUANGAN' => $ruangan1,
//                         'JUMLAH' => $values,
//                         'STATUS' => 1
//                     );

//                     $this->db->trans_start(); 

//                     $this->db->insert('db_rekammedis.tb_coding', $data_insert);
//                     $idcoding = $this->db->insert_id();

//                     $getData = $this->ModelCoderAdmin->TarikDataLagi($tgl_awal, $tgl_akhir, $ruangan1, $values, $offset)->result_array();
//                     $dataCodingDetail = array();

//                     foreach ($getData as $row) {
//                         $dataCodingDetail[] = array(
//                             'ID_CODING' => $idcoding,
//                             'NORM' => $row['NORM'],
//                             'NAMA_PASIEN' => $row['NAMA_PASIEN'],
//                             'NOPEN' => $row['NOPEN'],
//                             'TANGGAL_PENDAFTARAN' => $row['TANGGAL_PENDAFTARAN'],
//                             'CARABAYAR' => $row['CARABAYAR'],
//                             'SEP' => $row['SEP'],
//                             'RUANGAN' => $row['RUANGAN'],
//                             'PETUGAS' => $row['PETUGAS'],
//                             'STATUS_TAGIHAN' => $row['STATUS_TAGIHAN'],
//                             'DPJP' => $row['DPJP'],
//                             'PETUGASTAGIHAN' => $row['PETUGASTAGIHAN'],
//                             'PETUGASCOSTING' => $row['PETUGASCOSTING'],
//                             'TANGGAL_COSTING' => $row['TANGGAL_COSTING'],
//                             'TOTAL_COSTING' => $row['TOTAL_COSTING'],
//                             'STATUS_COSTING' => $row['STATUS_COSTING'],
//                             'HASIL_RAD' => $row['HASIL_RAD'],
//                         );
//                     }

//                     $this->db->insert_batch('db_rekammedis.tb_coding_detail', $dataCodingDetail);

//                     $this->db->trans_complete(); // Menyelesaikan transaksi

//                     if ($this->db->trans_status() === false) {
//                       $this->db->trans_rollback();
//                       return array('status' => 'failed');
//                   }
//                     $no++;
//                 }
//             }
//         }

//         return array('status' => 'success');
//       }
//     }
// }
public function index_grouping(){    
  $data=array(   
    'isi'            => 'Coder/IndexGrouping'
  );
  $this->load->view('Layout/Wrapper',$data);
}  

public function historyCG() {
  $data = $this->input->post('data'); // c costing g grouping ,b batal costing, s semua
  $awalCG = $this->input->post('awalCG');
  $akhirCG = $this->input->post('akhirCG');

  if (!$data || !$awalCG || !$akhirCG) {
      $output = array(
          "data" => []
      );
      echo json_encode($output);
      return;
  }
  $result = $this->ModelCoderAdmin->getDataCG($data, $awalCG, $akhirCG);

  $dataArr = array();
  $no = 1;
  foreach ($result as $field) {
      // Membuat badge untuk HASIL_RAD
      $hasilRadBadge = '-'; // Default untuk case selain 'g'
      
      if ($data == 'g' && isset($field->HASIL_RAD)) {
          switch ($field->HASIL_RAD) {
              case '1':
                  $hasilRadBadge = '<span class="badge badge-warning">Sudah Ada Hasil</span>';
                  break;
              case '2':
                  $hasilRadBadge = '<span class="badge badge-success">Sudah Final</span>';
                  break;
              case '0':
                  $hasilRadBadge = '<span class="badge badge-secondary">Hasil Dihapus</span>';
                  break;
              case '3':
                  $hasilRadBadge = '<span class="badge badge-info">Ada Tindakan Radiologi</span>';
                  break;
              case '9':
                  $hasilRadBadge = '<span class="badge badge-dark">Tidak Ada Tindakan Radiologi</span>';
                  break;
              default:
                  $hasilRadBadge = '<span class="badge badge-danger">Tidak Ada Hasil</span>';
                  break;
          }
      } else {
          $hasilRadBadge = '-';
      }

      $dataArr[] = array(
          $no,
          $field->SEP,
          date_format(date_create($field->TANGGAL_LAYANAN), 'd/m/Y h:i:s'),
          $field->nama_ruangan,
          $field->NAMA_PASIEN . ' [' . $field->NORM . ']',
          $field->DPJP,
          $hasilRadBadge,
          $field->status
      );
      $no++;
  }

  $output = array(
      "data" => $dataArr
  );
  echo json_encode($output);
}
public function index_cekEklaim(){    
  $data=array(   
    'isi'            => 'CoderAdmin/IndexEklaim'
  );
  $this->load->view('Layout/Wrapper',$data);
} 



public function process() {
  if ($_FILES['file1']['name'] && $_FILES['file2']['name']) {
      $file1 = $_FILES['file1']['tmp_name'];
      $file2 = $_FILES['file2']['tmp_name'];

      // Load PHPExcel
      $objPHPExcel1 = PHPExcel_IOFactory::load($file1);
      $objPHPExcel2 = PHPExcel_IOFactory::load($file2);

      // Convert Excel to array
      $data1 = $objPHPExcel1->getActiveSheet()->toArray(null, true, true, true);
      $data2 = $objPHPExcel2->getActiveSheet()->toArray(null, true, true, true);

      $sep1 = [];
      $sep2 = [];

      // Sistem
      foreach ($data1 as $index => $row) {
          if ($index === 1) continue; 
          if (!empty($row['A'])) { 
              $pasien = (!empty($row['C']) ? $row['C'] : '') . 
                        ' [ ' . 
                        (!empty($row['B']) ? $row['B'] : '') . ' ]';
              $sep1[] = [
                  'sep' => $row['A'],
                  'pasien' => $pasien,
                  'tanggal_layanan' => $row['D'] 
              ];
          }
      }

      // Eklaim
      foreach ($data2 as $index => $row) {
          if ($index === 1) continue; 
          if (!empty($row['F'])) { 
              $pasien = (!empty($row['E']) ? $row['E'] : '') . 
                        ' [ ' . 
                        (!empty($row['D']) ? $row['D'] : ' ]');
              $sep2[] = [
                  'sep' => $row['F'],
                  'pasien' => $pasien,
                  'tanggal_layanan' => $row['C'] 
              ];
          }
      }

      $result = [];
      $no = 1;
      foreach ($sep1 as $item) {
          if (!in_array($item['sep'], array_column($sep2, 'sep'))) {
              $result[] = [
                  'no' => $no++,
                  'sep' => $item['sep'],
                  'pasien' => $item['pasien'],
                  'tanggal_layanan' => $item['tanggal_layanan'], 
                  'keterangan' => 'Ada di Sistem, Tidak ada di Eklaim',
                   'warna' => 'M'
              ];
          }
      }

      foreach ($sep2 as $item) {
          if (!in_array($item['sep'], array_column($sep1, 'sep'))) {
              $result[] = [
                  'no' => $no++,
                  'sep' => $item['sep'],
                  'pasien' => $item['pasien'],
                  'tanggal_layanan' => $item['tanggal_layanan'],
                  'keterangan' => 'Ada di Eklaim, Tidak ada di Sistem',
                  'warna' => 'K'
              ];
          }
      }

      
      // $sekarang = date('Y-m-d H:i:s');      
      $response = [
          'data' => $result,
          // 'tanggal_dibuat' => $sekarang
      ];

      header('Content-Type: application/json');
      echo json_encode($response);
  } else {
      show_error('Both files are required!', 400);
  }
}






//drama

// public function process() {
//   if ($_FILES['file1']['name'] && $_FILES['file2']['name']) {
//       // Load files
//       $file1 = $_FILES['file1']['tmp_name'];
//       $file2 = $_FILES['file2']['tmp_name'];

//       // Load PHPExcel
//       $objPHPExcel1 = PHPExcel_IOFactory::load($file1);
//       $objPHPExcel2 = PHPExcel_IOFactory::load($file2);

//       // Convert Excel to array
//       $data1 = $objPHPExcel1->getActiveSheet()->toArray(null, true, true, true);
//       $data2 = $objPHPExcel2->getActiveSheet()->toArray(null, true, true, true);

//       // Ambil data SEP, Nama, dan No RM
//       $sep1 = [];
//       $sep2 = [];

//       //sistem
//       foreach ($data1 as $index => $row) {
//           if ($index === 1) continue; 
//           if (!empty($row['A'])) { 
//               $pasien = (!empty($row['C']) ? $row['C'] : '') . 
//                         ' [ ' . 
//                         (!empty($row['B']) ? $row['B'] : '').' ]'; 
//               $sep1[] = [
//                   'sep' => $row['A'],
//                   'pasien' => $pasien
//               ];
//           }
//       }

//       //eklaim
//       foreach ($data2 as $index => $row) {
//           if ($index === 1) continue; 
//           if (!empty($row['F'])) { 
//               $pasien = (!empty($row['E']) ? $row['E'] : '') . 
//                         ' / ' . 
//                         (!empty($row['D']) ? $row['C'] : ''); 
//               $sep2[] = [
//                   'sep' => $row['F'],
//                   'pasien' => $pasien
//               ];
//           }
//       }

//       $result = [];
//       $no = 1;

//       // Data ada di Sistem tapi tidak di Eklaim
//       foreach ($sep1 as $item) {
//           if (!in_array($item['sep'], array_column($sep2, 'sep'))) {
//               $result[] = [
//                   'no' => $no++,
//                   'sep' => $item['sep'],
//                   'pasien' => $item['pasien'],
//                   'keterangan' => 'Ada di Sistem, Tidak ada di Eklaim'
//               ];
//           }
//       }

//       // Data ada di Eklaim tapi tidak di Sistem
//       foreach ($sep2 as $item) {
//           if (!in_array($item['sep'], array_column($sep1, 'sep'))) {
//               $result[] = [
//                   'no' => $no++,
//                   'sep' => $item['sep'],
//                   'pasien' => $item['pasien'],
//                   'keterangan' => 'Ada di Eklaim, Tidak ada di Sistem'
//               ];
//           }
//       }

//       // Kirim hasil ke frontend
//       header('Content-Type: application/json');
//       echo json_encode(['data' => $result]);
//   } else {
//       show_error('Both files are required!', 400);
//   }
// }


// public function uploadExcel()
// {
//     // Konfigurasi upload
//    $config['upload_path'] = FCPATH . 'berkas/eklaim/';
//     $config['allowed_types'] = 'xls|xlsx'; 
//     $config['max_size'] = '5000';

//     $this->load->library('upload', $config); 

//     // Proses upload file
//     if (!$this->upload->do_upload('file')) {
//         $result = array(
//             'status' => 'error',
//             'message' => $this->upload->display_errors() // Error upload
//         );
//     } else {
//         $dataupload = $this->upload->data(); 
//         $path = $this->upload->upload_path . $dataupload['file_name']; 
//         // $this->load->library('excel');

//         try {
//             $object = PHPExcel_IOFactory::load($path);

//             $temp_data = []; 
//             foreach ($object->getWorksheetIterator() as $worksheet) {
//                 $highestRow = $worksheet->getHighestRow(); // Baris terakhir
//                 for ($row = 2; $row <= $highestRow; $row++) {
//                     // Ambil data per kolom
//                     $tglmasuk = $worksheet->getCellByColumnAndRow(1, $row)->getValue();
//                     $tglkeluar = $worksheet->getCellByColumnAndRow(2, $row)->getValue();
//                     $norm = $worksheet->getCellByColumnAndRow(3, $row)->getValue();
//                     $nama = $worksheet->getCellByColumnAndRow(4, $row)->getValue();
//                     $nosep = $worksheet->getCellByColumnAndRow(5, $row)->getValue();
//                     $inacbg = $worksheet->getCellByColumnAndRow(6, $row)->getValue();
//                     $topup = $worksheet->getCellByColumnAndRow(7, $row)->getValue();
//                     $totaltarif = $worksheet->getCellByColumnAndRow(8, $row)->getValue();
//                     $tarifrs = $worksheet->getCellByColumnAndRow(9, $row)->getValue();
//                     $jenis = $worksheet->getCellByColumnAndRow(10, $row)->getValue();

//                     $existingData = $this->db->get_where('db_rekammedis.tb_eklaim', ['sep' => $sep])->row_array();
//                     if ($existingData) {
//                         $this->session->set_flashdata('error', 'Data dengan SEP ' . $sep . ' sudah ada, tidak dapat diupload ulang.');
//                         echo json_encode([
//                             'status' => 'error',
//                             'message' => 'Data dengan SEP ' . $sep . ' sudah ada, tidak dapat diupload ulang.'
//                         ]);
//                         return; 
//                     }
//                     $ubahtglMasuk = !empty($tglmasuk) && strtotime($tglmasuk) ? date("Y-m-d", strtotime($tglmasuk)) : null;
//                     $ubahtglPulang= !empty($tglkeluar) && strtotime($tglkeluar) ? date("Y-m-d", strtotime($tglkeluar)) : null;

//                     $temp_data[] = array(
//                         'tgl_masuk' => $ubahtglMasuk,
//                         'tgl_pulang' => $ubahtglPulang,
//                         'no_rm' => $norm,
//                         'nama_pasien' => $nama,
//                         'sep' => $nosep,
//                         'inacbg' => $inacbg,
//                         'topup' => $topup,
//                         'total_tarif' => $totaltarif,
//                         'tarif_rs' => $tarifrs,
//                         'jenis' => $jenis

//                     );
//                 }
//             }
//             $this->db->trans_begin(); 
//             $this->db->insert('db_rekammedis.tb_eklaim', $data);

//             if ($this->db->trans_status() === false) {
//                 $this->db->trans_rollback();
//                 $result = array(
//                     'status' => 'error',
//                     'message' => 'Terjadi kesalahan saat menyimpan data.'
//                 );
//             } else {
//                 $this->db->trans_commit(); 
//                 $result = array(
//                     'status' => 'success',
//                     'message' => 'Data berhasil diimport.'
//                 );
//             }
//         } catch (Exception $e) {
//             $result = array(
//                 'status' => 'error',
//                 'message' => 'Error: ' . $e->getMessage()
//             );
//         }

//         // Hapus file setelah selesai diproses
//         unlink($path);
//     }

//     echo json_encode($result); // Kembalikan hasil dalam format JSON
// }

// public function Import_excel2(){
//   $this->load->library('excel');

//   // Cek apakah file ada di form
//   if (!empty($_FILES['file']['tmp_name'])) {
//       // Mengambil file yang diupload
//       $file = $_FILES['file']['tmp_name'];

//       // Load file Excel
//       try {
//           $object = PHPExcel_IOFactory::load($file);
//       } catch (Exception $e) {
//           $result = array('status' => 'failed', 'message' => 'Error loading file: ' . $e->getMessage());
//           echo json_encode($result);
//           return;
//       }

//       // Proses data dari file Excel
//       $temp_data = [];
//       foreach ($object->getWorksheetIterator() as $worksheet) {
//           $highestRow = $worksheet->getHighestRow(); // Mendapatkan baris terakhir data
//           $highestColumn = $worksheet->getHighestColumn(); // Mendapatkan kolom terakhir data
          
//           // Proses setiap baris mulai dari baris ke-5 (mengabaikan 4 baris pertama)
//           for ($row = 5; $row <= $highestRow; $row++) {
//               // Ambil nilai sel berdasarkan kolom dan baris
//               $tglmasuk = $worksheet->getCellByColumnAndRow(1, $row)->getValue();
//               $tglkeluar = $worksheet->getCellByColumnAndRow(2, $row)->getValue();
//               $norm = $worksheet->getCellByColumnAndRow(3, $row)->getValue();
//               $nama = $worksheet->getCellByColumnAndRow(4, $row)->getValue();
//               $nosep = $worksheet->getCellByColumnAndRow(5, $row)->getValue();
//               $inacbg = $worksheet->getCellByColumnAndRow(6, $row)->getValue();
//               $topup = $worksheet->getCellByColumnAndRow(7, $row)->getValue();
//               $totaltarif = $worksheet->getCellByColumnAndRow(8, $row)->getValue();
//               $tarifrs = $worksheet->getCellByColumnAndRow(9, $row)->getValue();
//               $jenis = $worksheet->getCellByColumnAndRow(10, $row)->getValue();
              
//               // Ubah format tanggal menggunakan PHPExcel_Shared_Date
//               $date1 = PHPExcel_Shared_Date::ExcelToPHP($tglmasuk);
//               $ubahtgl1 = gmdate("Y-m-d", $date1);

//               $date2 = PHPExcel_Shared_Date::ExcelToPHP($tglkeluar);
//               $ubahtgl2 = gmdate("Y-m-d", $date2);

//               // Siapkan data untuk disimpan ke database
//               $temp_data[] = array(
//                   'TANGGAL_MASUK' => $ubahtgl1,
//                   'TANGGAL_KELUAR' => $ubahtgl2,
//                   'NORM' => $norm,
//                   'NAMA_PASIEN' => $nama,
//                   'NO_SEP_KLAIM' => $nosep,
//                   'INACBG' => $inacbg,
//                   'TOPUP' => $topup,
//                   'TOTAL_TARIF' => $totaltarif,
//                   'TARIF_RS' => $tarifrs,
//                   'JENIS' => $jenis
//               );
//           }
//       }
//       $this->db->trans_begin(); 
//       $this->db->insert('db_rekammedis.tb_eklaim', $data);
      
//       if ($this->db->trans_status() === false) {
//           $this->db->trans_rollback(); 
//           $result = array('status' => 'failed', 'message' => 'Terjadi kesalahan saat menyimpan data.');
//       } else {
//           $this->db->trans_commit(); 
//           $result = array('status' => 'success', 'message' => 'Data berhasil diimport.');
//       }

//       echo json_encode($result); // Kembalikan hasil dalam format JSON
//   } else {
//       $result = array('status' => 'failed', 'message' => 'Tidak ada file yang diupload.');
//       echo json_encode($result);
//   }
// }



// public function uploadExcelAjax()
// {
//     if (!empty($_FILES['file']['name'])) {
//         $this->load->library('upload');
        
//         // Konfigurasi upload file
//         $config['upload_path'] = FCPATH . '../rekam_medis/berkas/eklaim/';
//         $config['allowed_types'] = 'xls|xlsx';
//         $config['max_size'] = 2048; // Maksimal ukuran file 2MB
//         $this->upload->initialize($config);

//         if ($this->upload->do_upload('file')) {
//             $fileData = $this->upload->data();
//             $excelFilePath = $fileData['full_path']; // Path file yang di-upload
            
//             // Memuat library PHPExcel
//             require_once FCPATH . 'vendor/phpoffice/phpexcel/Classes/PHPExcel/IOFactory.php'; // Memastikan file PHPExcel IOFactory dapat ditemukan
            
//             // Alihkan ke file sementara tanpa menyimpannya
//             $object = PHPExcel_IOFactory::load($_FILES['file']['tmp_name']); // Memuat file Excel langsung dari input stream
//             $worksheet = $object->getActiveSheet(); // Mendapatkan worksheet aktif
//             $worksheet_arr = $worksheet->toArray(); // Mengonversi worksheet ke array

//             // Mengabaikan 4 baris pertama (sesuai dengan kebutuhan Anda)
//             $worksheet_arr = array_slice($worksheet_arr, 4);
            
//             // Menghapus baris terakhir (jumlah data)
//             array_pop($worksheet_arr);

//             // Memulai transaksi untuk memastikan data konsisten
//             $this->db->trans_begin();
//             foreach ($worksheet_arr as $row) {
//                 $sep = $row[5]; // Mengambil nilai SEP dari baris
//                 $existingData = $this->db->get_where('db_rekammedis.tb_eklaim', ['sep' => $sep])->row_array();
                
//                 // Mengecek apakah SEP sudah ada di database
//                 if ($existingData) {
//                     $this->session->set_flashdata('error', 'Data dengan SEP ' . $sep . ' sudah ada, tidak dapat diupload ulang.');
//                     echo json_encode(['status' => 'error', 'message' => 'Data dengan SEP ' . $sep . ' sudah ada, tidak dapat diupload ulang.']);
//                     return;
//                 }

//                 // Menyiapkan data untuk disimpan
//                 $data = [
//                     'tgl_masuk' => $row[1],
//                     'tgl_pulang' => $row[2],
//                     'no_rm' => $row[3],
//                     'nama_pasien' => $row[4],
//                     'sep' => $sep,
//                     'inacbg' => $row[6],
//                     'top_up' => $row[7],
//                     'total_tarif' => $row[8],
//                     'tarif_rs' => $row[9],
//                     'jenis' => $row[10],
//                 ];

//                 // Menyimpan data ke database
//                 if (!$this->db->insert('db_rekammedis.tb_eklaim', $data)) {
//                     $this->session->set_flashdata('error', 'Gagal menyimpan data ke database.');
//                     echo json_encode(['status' => 'error', 'message' => 'Gagal menyimpan data ke database.']);
//                     $this->db->trans_rollback();
//                     return;
//                 }
//             }

//             // Commit transaksi jika semuanya berhasil
//             $this->db->trans_commit();
//             echo json_encode(['status' => 'success', 'message' => 'Data berhasil diupload dan disimpan!']);
//         } else {
//             // Menampilkan error jika file gagal diupload
//             echo json_encode(['status' => 'error', 'message' => 'Gagal mengunggah file: ' . $this->upload->display_errors()]);
//         }
//     } else {
//         // Menampilkan pesan jika tidak ada file yang dipilih
//         echo json_encode(['status' => 'error', 'message' => 'Tidak ada file yang dipilih.']);
//     }
// }



















// public function uploadExcel() {
//   // Pastikan file Excel sudah di-upload
//   if (!empty($_FILES['file']['name'])) {
//       // Mendapatkan file upload
//       $fileData = $_FILES['file'];

//       // Path file sementara
//       $filePath = $fileData['tmp_name'];

//       // Menggunakan PhpSpreadsheet untuk membaca file Excel
//       $spreadsheet = IOFactory::load($filePath); // Menggunakan IOFactory untuk memuat file
//       $worksheet = $spreadsheet->getActiveSheet();
//       $worksheetArr = $worksheet->toArray(); // Mengkonversi worksheet ke array

//       // Hapus header atau baris pertama jika diperlukan
//       $worksheetArr = array_slice($worksheetArr, 4); // Mengabaikan 4 baris pertama

//       // Loop melalui data dan masukkan ke database
//       foreach ($worksheetArr as $row) {
//           $sep = $row[5];
//           $existingData = $this->db->get_where('db_rekammedis.tb_eklaim', ['sep' => $sep])->row_array();
          
//           if ($existingData) {
//               throw new \Exception('Data dengan SEP ' . $sep . ' sudah ada, tidak dapat diupload ulang.');
//           }

//           $data = [
//               'tgl_masuk' => $row[1], 
//               'tgl_pulang' => $row[2], 
//               'no_rm' => $row[3], 
//               'nama_pasien' => $row[4], 
//               'sep' => $sep, 
//               'inacbg' => $row[6], 
//               'top_up' => $row[7], 
//               'total_tarif' => $row[8], 
//               'tarif_rs' => $row[9],
//               'jenis' => $row[10], 
//           ];

//           // Menyimpan data ke database
//           if (!$this->db->insert('db_rekammedis.tb_eklaim', $data)) {
//               throw new \Exception('Gagal menyimpan data ke database.');
//           }
//       }
//       // Berhasil
//       $this->session->set_flashdata('success', 'Data berhasil diupload!');
//       redirect('path/to/your/next/view'); // Redirect ke halaman berikutnya
//   } else {
//       // Jika tidak ada file
//       $this->session->set_flashdata('error', 'Tidak ada file yang dipilih.');
//       redirect('path/to/your/next/view');
//   }
// }
















// public function uploadExcelAjax()
// {
//     if (!empty($_FILES['file']['name'])) {
//         $this->load->library('upload');
//         $config['upload_path'] = FCPATH . '../rekam_medis/berkas/eklaim/';
//         $config['allowed_types'] = 'xls|xlsx';
//         $config['max_size'] = 2048;
//         $this->upload->initialize($config);

//         if ($this->upload->do_upload('file')) {
//             $fileData = $this->upload->data();
//             $excelFilePath = $fileData['full_path']; 

//             // Load library PhpSpreadsheet
//             $this->load->library('excel');
//             $spreadsheet = IOFactory::load($excelFilePath); 
//             $worksheet = $spreadsheet->getActiveSheet(); 
//             $worksheet_arr = $worksheet->toArray();

//             $worksheet_arr = array_slice($worksheet_arr, 4);

//             $this->db->trans_begin();
//             foreach ($worksheet_arr as $row) {
//                 $sep = $row[5];
//                 $existingData = $this->db->get_where('db_rekammedis.tb_eklaim', ['sep' => $sep])->row_array();
                
//                 if ($existingData) {
//                     $this->session->set_flashdata('error', 'Data dengan SEP ' . $sep . ' sudah ada, tidak dapat diupload ulang.');
//                     echo json_encode(['status' => 'error', 'message' => 'Data dengan SEP ' . $sep . ' sudah ada, tidak dapat diupload ulang.']);
//                     return;
//                 }

//                 $data = [
//                     'tgl_masuk' => $row[1], 
//                     'tgl_pulang' => $row[2], 
//                     'no_rm' => $row[3], 
//                     'nama_pasien' => $row[4], 
//                     'sep' => $sep, 
//                     'inacbg' => $row[6], 
//                     'top_up' => $row[7], 
//                     'total_tarif' => $row[8], 
//                     'tarif_rs' => $row[9],
//                     'jenis' => $row[10], 
//                 ];

//                 if (!$this->db->insert('db_rekammedis.tb_eklaim', $data)) {
//                     $this->session->set_flashdata('error', 'Gagal menyimpan data ke database.');
//                     echo json_encode(['status' => 'error', 'message' => 'Gagal menyimpan data ke database.']);
//                     $this->db->trans_rollback();
//                     return;
//                 }
//             }
//             $this->db->trans_commit();
//             echo json_encode(['status' => 'success', 'message' => 'Data berhasil diupload dan disimpan!']);
//         } else {
//             echo json_encode(['status' => 'error', 'message' => 'Gagal mengunggah file: ' . $this->upload->display_errors()]);
//         }
//     } else {
//         echo json_encode(['status' => 'error', 'message' => 'Tidak ada file yang dipilih.']);
//     }
// }














// public function uploadExcel()
// {
//     // Pastikan file sudah di-upload
//     if (!empty($_FILES['file']['name'])) {
//         $this->load->library('upload');
//         $config['upload_path'] = FCPATH . '../rekam_medis/berkas/eklaim/';
//         $config['allowed_types'] = 'xls|xlsx'; 
//         $config['max_size'] = 2048;
//         $this->upload->initialize($config);

//         if ($this->upload->do_upload('file')) {
//             $fileData = $this->upload->data();
//             $excelFilePath = $fileData['full_path']; 

//             // Load library PhpSpreadsheet
//             $this->load->library('excel'); 
//             $spreadsheet = IOFactory::load($excelFilePath); 
//             $worksheet = $spreadsheet->getActiveSheet(); 
//             $worksheet_arr = $worksheet->toArray();

//             $worksheet_arr = array_slice($worksheet_arr, 4);

//             $this->db->trans_begin();
//             foreach ($worksheet_arr as $row) {
//                 $sep = $row[5];
//                 $existingData = $this->db->get_where('db_rekammedis.tb_eklaim', ['sep' => $sep])->row_array();
                
//                 if ($existingData) {
//                     throw new \Exception('Data dengan SEP ' . $sep . ' sudah ada, tidak dapat diupload ulang.');
//                 }

//                 $data = [
//                     'tgl_masuk' => $row[1], 
//                     'tgl_pulang' => $row[2], 
//                     'no_rm' => $row[3], 
//                     'nama_pasien' => $row[4], 
//                     'sep' => $sep, 
//                     'inacbg' => $row[6], 
//                     'top_up' => $row[7], 
//                     'total_tarif' => $row[8], 
//                     'tarif_rs' => $row[9],
//                     'jenis' => $row[10], 
//                 ];

//                 if (!$this->db->insert('db_rekammedis.tb_eklaim', $data)) {
//                     throw new \Exception('Gagal menyimpan data ke database.');
//                 }
//             }
//             $this->db->trans_commit();
//             return redirect()->back()->with('success', 'Data berhasil diupload dan disimpan!');
//         } else {
//             return redirect()->back()->with('error', 'Gagal mengunggah file: ' . $this->upload->display_errors());
//         }
//     } else {
//         return redirect()->back()->with('error', 'Tidak ada file yang dipilih.');
//     }
// }




    

}

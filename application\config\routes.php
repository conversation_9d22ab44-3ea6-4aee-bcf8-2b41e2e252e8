<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/*
| -------------------------------------------------------------------------
| URI ROUTING
| -------------------------------------------------------------------------
| This file lets you re-map URI requests to specific controller functions.
|
| Typically there is a one-to-one relationship between a URL string
| and its corresponding controller class/method. The segments in a
| URL normally follow this pattern:
|
|	example.com/class/method/id/
|
| In some instances, however, you may want to remap this relationship
| so that a different class/function is called than the one
| corresponding to the URL.
|
| Please see the user guide for complete details:
|
|	https://codeigniter.com/userguide3/general/routing.html
|
| -------------------------------------------------------------------------
| RESERVED ROUTES
| -------------------------------------------------------------------------
|
| There are three reserved routes:
|
|	$route['default_controller'] = 'welcome';
|
| This route indicates which controller class should be loaded if the
| URI contains no data. In the above example, the "welcome" class
| would be loaded.
|
|	$route['404_override'] = 'errors/page_missing';
|
| This route will tell the Router which controller/method to use if those
| provided in the URL cannot be matched to a valid route.
|
|	$route['translate_uri_dashes'] = FALSE;
|
| This is not exactly a route, but allows you to automatically route
| controller and method names that contain dashes. '-' isn't a valid
| class or method name character, so it requires translation.
| When you set this option to TRUE, it will replace ALL dashes in the
| controller and method URI segments.
|
| Examples:	my-controller/index	-> my_controller/index
|		my-controller/my-method	-> my_controller/my_method
*/
$route['default_controller'] = 'Login';
$route['404_override'] = '';
$route['translate_uri_dashes'] = FALSE;

$route['end'] = 'Logout';
$route['dashboard'] = 'Dashboard/index';
$route['master'] = 'Perusahaan/index';
$route['input'] = 'Input/index';
$route['kso'] = 'Kso/index';

$route['coder_admin'] = 'CoderAdmin/index';
$route['coder'] = 'Coder/index';
$route['coder_radiologi'] = 'CoderRadiologi/index';
$route['coder_history'] = 'Coder/index_history';
$route['coder_tugas'] = 'Coder/index_tugas';
$route['admin'] = 'CoderAdmin/index_grouping';
$route['admin_cek'] = 'CoderAdmin/index_cekEklaim';
$route['admin_jkn'] = 'Admin/index_jkn';

$route['assri'] = 'Assembling/index_assri';
$route['assrj'] = 'Assembling/index_assrj';

$route['scan'] = 'Digital/index_scan';
$route['arsip'] = 'Digital/index_arsip';
$route['scan_rj'] = 'Digital/tab_scan_rj';
$route['scan_ri'] = 'Digital/tab_scan_ri';
$route['scan_ll'] = 'Digital/tab_scan_ll';

$route['pantau_rj'] = 'Pemantauan/index_pantau_rj';
$route['pantau_rjT'] = 'Pemantauan/index_pantau_rjT';
$route['pantau_rjH'] = 'Pemantauan/index_pantau_rjH';


$route['pantau_ri'] = 'Pemantauan/index_pantau_ri';
$route['pantau_riT'] = 'Pemantauan/index_pantau_riT';
$route['pantau_riH'] = 'Pemantauan/index_pantau_riH';

// $route['pemi'] = 'Peminjaman/index';
// $route['peng'] = 'Pengembalian/index';

$route['nilaiguna'] = 'Analisis/indexAwalNilaiGuna';
$route['nilaigunaAnalisis'] = 'Analisis/indexNG';
$route['nilaigunaUploadf'] = 'Analisis/indexUploadf';
$route['nilaigunaEdit/(:num)'] = 'Analisis/indexEditf/$1';
// $route['nilaigunaEdit'] = 'Analisis/indexEditf';
// $route['nilaiguna/update_file'] = 'Analisis/updateSatuFile';

$route['kuantitatif'] = 'Analisis/indexAwal';
$route['kuantitatifAnalisis'] = 'Analisis/index';
$route['kuantitatifHistory'] = 'Analisis/indexHistory';

$route['kualitatif'] = 'Analisis/index_kualitatifAwal';
$route['kualitatifAnalisis'] = 'Analisis/index_kualitatif';
$route['kuanlitatifHistory'] = 'Analisis/index_kualitatifHistory';

// $route['rawat_inap'] = 'Checklist/index_inap';
// $route['rawat_jalan'] = 'Checklist/index_jalan';


$route['review'] = 'Review/indexAwal';
$route['reviewT'] = 'Review/index';
$route['reviewH'] = 'Review/indexHistory';


$route['pemi'] = 'Peminjaman/indexAwal';
$route['pemiTransaksi'] = 'Peminjaman/index';
$route['pemiHistory'] = 'Pengembalian/index';
$route['peminjaman'] = 'peminjaman/indexPinjamAwal';
$route['peminjamanT'] = 'Peminjaman/indexpinjam';
$route['peminjamanH'] = 'Peminjaman/indexpinjamh';
$route['peng'] = 'Pengembalian/index';
$route['inaktif'] = 'Peminjaman/index_inaktif';
$route['peminjamanTF'] = 'Peminjaman/indexpinjamf';
$route['peminjamanHF'] = 'Peminjaman/indexpinjamhf';
$route['peminjamanTP'] = 'Peminjaman/indexpinjamp';
$route['peminjamanHP'] = 'Peminjaman/indexpinjamhp';
$route['peminjamanHome'] = 'Peminjaman/indexPeneliti';
$route['peminjamanHomeT'] = 'Peminjaman/indexPenelitiTrans';
$route['peminjamanHomeH'] = 'Peminjaman/indexPenelitiHis';
$route['peneliti'] = 'Penelitian/index';

$route['penelitian'] = 'Penelitian/indexPenelitianHome';
$route['penelitianT'] = 'Penelitian/indexPenelitian';
$route['penelitianH'] = 'Penelitian/indexPenelitianHistory';

$route['lap_rekap'] = 'Laporan/index';
$route['lap_ana'] = 'Laporan/index_lap_analisis';
$route['perjanjianH'] = 'Perjanjian/indexHistory';

$route['admision'] = 'Admision/index';
$route['reservasi/list'] = 'Admision/indexReservasi';
$route['admisionUser'] = 'Admision/indexUser';
$route['reservasi/diterima'] = 'Admision/indexReservasiTerima';
$route['reservasi/ditolak'] = 'Admision/indexReservasiTolak';
$route['admisionKamar'] = 'Admision/indexReservasiKamar';
$route['admisionLaporan'] = 'Laporan/indexAdmision';
$route['multiguna'] = 'Analisis/index_multiguna';

// Medical Records Routes
$route['medicalrecords'] = 'MedicalRecords/index';
$route['medicalrecords/get_coder_transaksi'] = 'MedicalRecords/get_coder_transaksi';
$route['medicalrecords/get_coder_history'] = 'MedicalRecords/get_coder_history';
$route['medicalrecords/get_dokter_data'] = 'MedicalRecords/get_dokter_data';
$route['medicalrecords/proses_komen'] = 'MedicalRecords/proses_komen';
$route['medicalrecords/simpan_komentar_icd'] = 'MedicalRecords/simpan_komentar_icd';
$route['medicalrecords/update_komentar_icd'] = 'MedicalRecords/update_komentar_icd';
$route['medicalrecords/get_existing_comment'] = 'MedicalRecords/get_existing_comment';
$route['medicalrecords/finalisasi_resume'] = 'MedicalRecords/finalisasi_resume';
$route['medicalrecords/get_revisi_data'] = 'MedicalRecords/get_revisi_data';
$route['medicalrecords/update_status_dokter'] = 'MedicalRecords/update_status_dokter';
$route['medicalrecords/approve_resume'] = 'MedicalRecords/approve_resume';

// Penjadwalan Operasi Routes
$route['penjadwalan_operasi'] = 'PenjadwalanOperasi/index';

<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PengkajianDafOpe extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Jakarta');
    $this->load->model(
      [
        'masterModel',
        'pengkajianAwalModel',
        'operasi/AsistenBedahModel',
        'operasi/PengkajianDafOpeModel',
        'operasi/PengkajianPraOperasiModel',
        'operasi/WaitingListModel'
      ]
    );
  }

  public function index()
  {
    $nokun = $this->uri->segment(5);
    $norm = $this->uri->segment(3);

    $data = [
      'norm' => $norm,
      'getNomr' => $this->pengkajianAwalModel->getNomr($nokun),
      'listDr' => $this->masterModel->listDr(),
      'tujuanOperasi' => $this->masterModel->referensi(1456),
      'sifatOperasi' => $this->masterModel->referensi(621),
      'rencanaJenisPembiusan' => $this->masterModel->referensi(622),
      'historydaftaroperasi' => $this->pengkajianAwalModel->historydaftaroperasi(),
      'historydaftaroperasiReservasi' => $this->PengkajianDafOpeModel->historyWithReservasi($norm),
      'potongBeku' => $this->masterModel->referensi(1802),
      'joinOperasi' => $this->masterModel->referensi(1851),
      'PA' => $this->masterModel->referensi(1863),
      'VC' => $this->masterModel->referensi(1864),
      'dataSebelumnya' => $this->PengkajianDafOpeModel->ambil($nokun),
      'dataPengkajianPraOperasi' => $this->PengkajianPraOperasiModel->ambil($nokun),
      'caraBayarOptions' => $this->PengkajianPraOperasiModel->ambilReverensi(10, null), // Cara bayar
      'kelasRawatOptions' => $this->PengkajianPraOperasiModel->ambilReverensi(19, null) // Kelas rawat
    ];

    // echo'<pre>';print_r($data);exit();
    $this->load->view('Pengkajian/operasi/pendaftaranPasienOperasi', $data);
  }

  public function alasan()
  {
    $post = $this->input->post();
    // echo'<pre>';print_r($post);exit();
    $result = $this->PengkajianDafOpeModel->alasanSifatOperasi($post['sifatOperasi'], $post['smf']);
    $data = [];
    foreach ($result as $row) {
      $sub_array = [];
      $sub_array['id'] = $row['id'];
      $sub_array['text'] = $row['deskripsi'];
      $data[] = $sub_array;
    }
    // echo '<pre>';print_r($data);exit();
    echo json_encode($data);
  }

  public function action_dafoperasi($param)
  {
    // echo'<pre>';print_r($param);exit();
    $this->db->trans_begin();
    $post = $this->input->post();
    // echo'<pre>';print_r($post);exit();
    $idWl = $post['id_waiting_list'] ?? 0;
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'tambah' || $param == 'ubah') {
        // Mulai rules
        $this->form_validation->set_rules($this->AsistenBedahModel->rules());
        $this->form_validation->set_rules($this->PengkajianDafOpeModel->rules());

        // Mulai sifat operasi
        if ($param == 'tambah') {
          if (isset($post['sifat_operasi'])) {
            if ($post['sifat_operasi'] == 2131) { // CITO
              $this->form_validation->set_rules($this->PengkajianDafOpeModel->rulesCITO());
            } elseif ($post['sifat_operasi'] == 6080) { // Urgent
              $this->form_validation->set_rules($this->PengkajianDafOpeModel->rulesUrgent());
            } elseif ($post['sifat_operasi'] == 6125) { // Prioritas
              $this->form_validation->set_rules($this->PengkajianDafOpeModel->rulesPrioritas());
            }
          }
        }
        // Akhir sifat operasi

        if (isset($post['rencana_jenis_pembiusan']) && $post['rencana_jenis_pembiusan'] == 2138) { // Rencana jenis pembiusan
          $this->form_validation->set_rules($this->PengkajianDafOpeModel->rulesRencanaJenisPembiusan());
        }
        // Akhir rules

        if ($this->form_validation->run() == true) {
          // Mulai data
          $nokun = $post['nokun'];
          $dokterBedah = $post['dokter_bedah'];
          $diagnosis = $post['diagnosa_medis'];
          $tindakan = $post['rencana_tindakan_operasi'];
          $tanggalOperasi = $post['tanggal_operasi'];
          $tujuanOperasi = $post['tujuan_operasi'];
          $sifatOperasi = $post['sifat_operasi'];
          $created_at = date('Y-m-d H:i:s');
          $oleh = $this->session->userdata('id');
          // Akhir data

          // Mulai data daftar operasi
          $dataDaftarOperasi = [
            'diagnosa_medis' => $diagnosis,
            'rencana_tindakan_operasi' => $tindakan[0],
            'rencana_tindakan_operasi2' => $post['rencana_tindakan_operasi1'] ?? null, 
            'rencana_tindakan_operasi3' => $post['rencana_tindakan_operasi2'] ?? null, 
            'ruang_tujuan' => $post['ruang_tujuan'],
            'ruang_operasi' => $post['ruang_operasi'],
            'perkiraan_lama_operasi' => $post['perkiraan_lama_operasi'],
            'tanggal_operasi' => $tanggalOperasi,
            'jam_operasi' => $post['jam_operasi'],
            'tujuan_operasi' => $tujuanOperasi,
            'sifat_operasi' => $sifatOperasi,
            'rencana_jenis_pembiusan' => $post['rencana_jenis_pembiusan'],
            'rencana_jenis_pembiusan_lain' => $post['rencana_jenis_pembiusan_lain'] ?? null,
            'dokter_bedah' => $dokterBedah[0],
            'potong_beku' => $post['potong_beku'],
            'join_operasi' => $post['join_operasi'],
            'pilihan_op' => $post['pilihanOperasi'] ?? null,
            'ket_pilihan_op' => $post['VC'] ?? null,
            'tindakan_operasi' => $post['tindakan_operasi'] ?? null,
            'tindakan_operasi2' => $post['tindakan_operasi1'] ?? null, 
            'tindakan_operasi3' => $post['tindakan_operasi2'] ?? null, 
            'catatan_khusus' => $post['catatan_khusus'] ?? null,
            'kelas' => $post['kelas'] ?? null,
            'slot_operasi' => $post['slot_operasi'] ?? null,
            'kamar' => $post['id_kamar'] ?? null,
          ];
          // echo'<pre>';print_r($dataDaftarOperasi);exit();
          // Akhir data daftar operasi

          // Mulai data waiting list
          $dataWl = [
            'id_dokter' => $dokterBedah[0],
            'diagnosis' => $diagnosis,
            'tindakan' => $tindakan[0],
            'tujuan_operasi' => $tujuanOperasi,
            'sifat_operasi' => $sifatOperasi,
            'tanggal' => date('Y-m-d'),
          ];
          // Akhir data waiting list

          // Mulai aksi
          if (isset($param)) {
            if ($param == 'ubah') {
              $id = $post['id'];

              // Mulai periksa alasan
              if (isset($post['sifat_operasi_lain'])) {
                $dataDaftarOperasi['sifat_operasi_lain'] = $post['sifat_operasi_lain'];
              }
              if (isset($post['alasan_urgent'])) {
                $dataDaftarOperasi['alasan_urgent'] = $post['alasan_urgent'];
              }
              if (isset($post['alasan_prioritas'])) {
                $dataDaftarOperasi['alasan_prioritas'] = $post['alasan_prioritas'];
              }
              if (isset($post['ruang_operasi'])) {
                $dataDaftarOperasi['ruang_operasi'] = $post['ruang_operasi'];
              }
              // Akhir periksa alasan
              // echo'<pre>';print_r($dataDaftarOperasi);exit();

              $this->PengkajianDafOpeModel->ubah($id, $dataDaftarOperasi);
              $this->WaitingListModel->ubah($idWl, $dataWl);
              $this->WaitingListModel->ubahRencana($idWl, ['status' => 0]);
              $this->AsistenBedahModel->ubah($id, ['status' => 0]);
            } elseif ($param == 'tambah') {
              // Mulai simpan daftar operasi
              $dataDaftarOperasi['nokun'] = $nokun;
              $dataDaftarOperasi['sifat_operasi_lain'] = $post['sifat_operasi_lain'] ?? 0;
              $dataDaftarOperasi['alasan_urgent'] = $post['alasan_urgent'] ?? 0;
              $dataDaftarOperasi['alasan_prioritas'] = $post['alasan_prioritas'] ?? 0;
              $dataDaftarOperasi['oleh'] = $oleh;
              $dataDaftarOperasi['created_at'] = $created_at;
              $dataDaftarOperasi['status'] = 1;
              // echo'<pre>';print_r($dataDaftarOperasi);exit();
              $id = $this->PengkajianDafOpeModel->simpan($dataDaftarOperasi);
              // Akhir simpan daftar operasi

              // Mulai simpan waiting list
              $dataWl['norm'] = $post['norm'];
              $dataWl['nokun'] = $nokun;
              $dataWl['id_pendaftaran_operasi'] = $id;
              $dataWl['created_at'] = $created_at;
              $dataWl['status'] = 1;
              $dataWl['oleh'] = $oleh;
              $idWl = $this->WaitingListModel->simpan($dataWl);
              // Akhir simpan waiting list
               // Mulai simpan ke remun_medis.perjanjian jika ruang operasi ID=16
              if ($post['ruang_operasi'] == '16') {
                $namaPasien = $this->PengkajianDafOpeModel->getNamaLengkapPasien($post['norm']);
                $nomorKontak = $this->PengkajianDafOpeModel->getKontakPasien($post['norm']);

                // Generate nomor kontrol dengan method yang aman
                $noKontrol = $this->PengkajianDafOpeModel->generateNoKontrol($post['tanggal_operasi']);

                // Siapkan data untuk insert ke remun_medis.perjanjian
                $dataPerjanjian = [
                  'ID_PENDAFTARAN_OPERASI' => $id,
                  'ID_WAITING_LIST_OPERASI' => $idWl,
                  'NOMR' => $post['norm'],
                  'NAMAPASIEN' => $namaPasien,
                  'ID_RUANGAN' => '105090104',
                  'ID_DOKTER' => $post['dokter_bedah'][0] ?? 0,
                  'TANGGAL' => $post['tanggal_operasi'],
                  'KETERANGAN' => '-',
                  'RENCANA' => 11,
                  'TUJUANOPERASI' => $post['tujuan_operasi'],
                  'NOMOR' => $nomorKontak,
                  'TANGGALRAWATINAP' => $post['tanggal_operasi'],
                  'DIAGNOSA' => $post['diagnosa_medis'],
                  'TINDAKAN' => implode(',', $post['rencana_tindakan_operasi']),
                  'OLEH' => $this->session->userdata('id'),
                  'STATUS' => 1,
                  'NOKONTROL' => $noKontrol,
                  'NOKONTROLDOKTER' => null,
                  'PERSETUJUAN' => 0,
                  'STATUS_SORE' => 0
                ];

                // Insert ke tabel remun_medis.perjanjian
                $idPerjanjian = $this->PengkajianDafOpeModel->insertPerjanjianOperasi($dataPerjanjian);

                // Insert ke log.log_pendaftaran_operasi setelah berhasil insert perjanjian
                if ($idPerjanjian && !empty($post['slot_operasi'])) {
                  $this->insertLogPendaftaranOperasi($idPerjanjian, $post['tanggal_operasi'], $post['id_kamar'] ?? null, $post['slot_operasi']);
                }

                // Cek status rawat inap berdasarkan NOPEN
                $isRawatInap = $this->PengkajianDafOpeModel->cekStatusRawatInap($post['nopen']);

                if ($isRawatInap) {
                  log_message('info', 'Pasien berstatus rawat inap - NOPEN: ' . $post['nopen']);
                } else {
                  // Jika bukan rawat inap, insert ke db_reservasi.tb_reservasi
                  $dataReservasi = [
                    'id_perjanjian' => $idPerjanjian,
                    'norm' => $post['norm'],
                    'id_waiting_list_operasi' => $idWl,
                    'id_pendaftaran_operasi' => $id,
                    'nama'=> $namaPasien,
                    'no_telp' => $nomorKontak,
                    'tgl_rencanaMasuk' => $post['tanggal_operasi'],
                    'tgl_pcr' => '',
                    'id_cara_bayar' => 1,
                    'id_kelas' => null,
                    'kode_booking' => $this->PengkajianDafOpeModel->ambil_kode_booking()->kode_booking,
                    'id_dokter' => $post['dokter_bedah'][0],
                    'id_tujuan_dirawat' => $post['ruang_operasi'],
                    'pengguna' => $this->session->userdata('id')
                  ];
                  
                  $this->PengkajianDafOpeModel->insertReservasi($dataReservasi);
                }
              }
              // Akhir simpan ke remun_medis.perjanjian
            }          

              // Mulai simpan waiting list rencana
              $dataWlr = [
                'id_wlo' => $idWl,
                'tanggal_rencana' => $tanggalOperasi,
                'keterangan' => $post['keterangan'] ?? null,
                'created_at' => $created_at,
                'status' => 1,
                'oleh' => $oleh
              ];
              $this->WaitingListModel->simpanRencana($dataWlr);
              // Akhir simpan waiting list rencana

             

            // Mulai simpan dokter bedah lain
            if (count($dokterBedah) > 1) { // Periksa apakah dokter bedah lebih dari 1
              $i = 0;
              $dataDokter = [];
              foreach ($dokterBedah as $db) {
                if ($i > 0) {
                  $dataDokter[$i] = [
                    'id_pendaftaran' => $id,
                    'asisten_bedah' => $dokterBedah[$i],
                    'rencana_tindakan' => $tindakan[$i],
                    'oleh' => $oleh,
                    'status' => 1
                  ];
                }
                $i++;
              }
              // echo'<pre>';print_r($dataDokter);exit();
              $this->AsistenBedahModel->simpan($dataDokter);
            }
            // Akhir simpan dokter bedah lain
          }
          // Akhir aksi

          if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = ['status' => 'failed'];
          } else {
            $this->db->trans_commit();
            $result = ['status' => 'success'];
          }
        } else {
          $result = [
            'status' => 'failed',
            'errors' => $this->form_validation->error_array()
          ];
        }

        echo json_encode($result);
      }
    }
  }

  public function viewDaftarPraOperasi()
  {
    $id = $this->input->post('id');
    $getDaftarOperasi = $this->PengkajianDafOpeModel->detail($id);
    $nokun = $getDaftarOperasi['nokun'] ?? null;

    $data = [
      'id' => $id,
      'getNomr' => $this->pengkajianAwalModel->getNomr($nokun),
      'listDr' => $this->masterModel->listDr(),
      'tujuanOperasi' => $this->masterModel->referensi(1456),
      'sifatOperasi' => $this->masterModel->referensi(621),
      'rencanaJenisPembiusan' => $this->masterModel->referensi(622),
      'potongBeku' => $this->masterModel->referensi(1802),
      'joinOperasi' => $this->masterModel->referensi(1851),
      'kelasOperasi' => $this->PengkajianDafOpeModel->getKelasOperasi(),
      'getDaftarOperasi' => $getDaftarOperasi,
      'PA' => $this->masterModel->referensi(1863),
      'VC' => $this->masterModel->referensi(1864),
      'tindakanDokterLain' => $this->AsistenBedahModel->ambilTindakan($id)
    ];

    // echo'<pre>';print_r($data);exit();
    $this->load->view('Pengkajian/operasi/editDaftarPraOperasi', $data);
  }


  /**
   * Fungsi untuk memeriksa ruangan pasien dan menentukan penjamin/pembiayaan operasi yang sesuai
   * Digunakan untuk auto-select penjamin/pembiayaan operasi berdasarkan GEDUNG
   * Jika GEDUNG IS NULL, pilih Operasi Reguler (ID=2)
   * Jika GEDUNG=1, pilih Operasi Swasta (Gedung C) (ID=16)
   */
  public function cekRuangan()
  {
    $id_ruangan = $this->input->post('id_ruangan');

    if ($id_ruangan) {
      // Ambil data ruangan dari masterModel
      $ruangan = $this->masterModel->getDeskRuangan($id_ruangan);

      // Kembalikan data dalam format JSON
      echo json_encode($ruangan);
    } else {
      echo json_encode(null);
    }
  }

  /**
   * Fungsi untuk mengecek jumlah operasi berdasarkan tanggal
   * Menggunakan model untuk business logic
   */
  public function cekJumlahOperasi()
  {
    $tanggal = $this->input->post('tanggal');

    if ($tanggal) {
      $jumlah = $this->PengkajianPraOperasiModel->getJumlahOperasi($tanggal);
      echo json_encode(['jumlah' => $jumlah]);
    } else {
      echo json_encode(['jumlah' => 0]);
    }
  }

  /**
   * Fungsi untuk mengecek apakah pasien sudah memiliki pendaftaran operasi pada tanggal tertentu
   * Menggunakan model untuk business logic
   * Hanya berlaku untuk ruang operasi ID=16
   */
  public function cekPendaftaranOperasiTanggal()
  {
    $norm = $this->input->post('norm');
    $tanggal = $this->input->post('tanggal');
    $ruang_operasi = $this->input->post('ruang_operasi');

    // cek
    // log_message('debug', 'cekPendaftaranOperasiTanggal - norm: ' . $norm . ', tanggal: ' . $tanggal . ', ruang_operasi: ' . $ruang_operasi);

    // Validasi parameter dengan trim untuk menghindari whitespace
    $norm = trim($norm);
    $tanggal = trim($tanggal);
    $ruang_operasi = trim($ruang_operasi);

    // Jika bukan ruang operasi ID=16, langsung return aman
    if ($ruang_operasi != '16') {
      echo json_encode([
        'status' => 'aman',
        'message' => 'Tanggal tersedia untuk pendaftaran operasi.'
      ]);
      return;
    }

    if (!empty($norm) && !empty($tanggal)) {
      $pendaftaran = $this->PengkajianPraOperasiModel->cekPendaftaranOperasiByTanggal($norm, $tanggal);
      
      if ($pendaftaran) {
        // Format tanggal untuk ditampilkan
        $tanggal_format = date('d/m/Y', strtotime($pendaftaran['TANGGAL']));
        
        echo json_encode([
          'status' => 'sudah_ada',
          'nama_pasien' => $pendaftaran['nama_pasien'],
          'tanggal' => $tanggal_format,
          'message' => 'Pasien ' . $pendaftaran['nama_pasien'] . ' dengan tanggal ' . $tanggal_format . ' sudah memiliki pendaftaran operasi.'
        ]);
      } else {
        echo json_encode([
          'status' => 'aman',
          'message' => 'Tanggal tersedia untuk pendaftaran operasi.'
        ]);
      }
    } else {
      echo json_encode([
        'status' => 'error',
        'message' => 'Parameter tidak lengkap. NORM: ' . ($norm ? 'ada' : 'kosong') . ', Tanggal: ' . ($tanggal ? 'ada' : 'kosong') . ', Ruang Operasi: ' . ($ruang_operasi ? 'ada' : 'kosong')
      ]);
    }
  }

  /**
   * Fungsi untuk mengambil detail operasi dengan server-side processing
   * Mendukung mode single date dan date range (30 hari ke depan)
   */
  public function getDetailOperasi()
  {
    // Ambil parameter dari DataTables server-side processing
    $draw = $this->input->post('draw') ?? 1;
    $start = $this->input->post('start') ?? 0;
    $length = $this->input->post('length') ?? 10;
    $search = $this->input->post('search')['value'] ?? '';
    
    // Ambil parameter tanggal (bisa single atau range)
    $tanggal = $this->input->post('tanggal');
    $tanggal_awal = $this->input->post('tanggal_awal');
    $tanggal_akhir = $this->input->post('tanggal_akhir');

    // Panggil method model untuk mendapatkan data
    $result = $this->PengkajianPraOperasiModel->getDetailOperasi($tanggal_awal, $tanggal_akhir, $tanggal, $start, $length, $search);
    
    // Format response untuk DataTables server-side
    $response = [
      'draw' => intval($draw),
      'recordsTotal' => $result['recordsTotal'],
      'recordsFiltered' => $result['recordsFiltered'],
      'data' => $result['data']
    ];

    echo json_encode($response);
  }

  /**
   * Fungsi untuk mengambil data cara bayar (master referensi jenis 10)
   * Untuk select2 pada modal lengkapi reservasi
   */
  public function cara_bayar()
  {
    $result = $this->PengkajianPraOperasiModel->ambilReverensi(10, null);
    $data = [];
    foreach ($result as $row) {
      $sub_array = [];
      $sub_array['id'] = $row['ID'];
      $sub_array['text'] = $row['DESKRIPSI'];
      $data[] = $sub_array;
    }
    echo json_encode($data);
  }

  /**
   * Fungsi untuk mengambil data kelas rawat (master referensi jenis 19)
   * Untuk select2 pada modal lengkapi reservasi
   */
  public function kelas_rawat()
  {
    $result = $this->PengkajianPraOperasiModel->ambilReverensi(19, null);
    $data = [];
    foreach ($result as $row) {
      $sub_array = [];
      $sub_array['id'] = $row['ID'];
      $sub_array['text'] = $row['DESKRIPSI'];
      $data[] = $sub_array;
    }
    echo json_encode($data);
  }

  /**
   * Fungsi untuk mengambil data reservasi berdasarkan ID pendaftaran operasi
   * Join antara medis.tb_pendaftaran_operasi, remun_medis.perjanjian, dan db_reservasi.tb_reservasi
   */
  public function getDataReservasi()
  {
    $id = $this->input->post('id');

    if ($id) {
      $data = $this->PengkajianDafOpeModel->getDataReservasi($id);

      if ($data) {
        echo json_encode([
          'status' => 'success',
          'data' => $data
        ]);
      } else {
        echo json_encode([
          'status' => 'error',
          'message' => 'Data reservasi tidak ditemukan'
        ]);
      }
    } else {
      echo json_encode([
        'status' => 'error',
        'message' => 'ID pendaftaran tidak valid'
      ]);
    }
  }

  /**
   * Fungsi untuk mengambil data perjanjian operasi untuk modal List Perjanjian Operasi
   * Menggunakan referensi dari getDetailOperasi dengan format khusus untuk slot operasi
   */
  public function getListPerjanjianOperasi()
  {
    try {
      $tanggal_mulai = $this->input->post('tanggal_mulai') ?? date('Y-m-d');
      $hari_kerja = $this->input->post('hari_kerja') ?? [];

      // Debug log
      log_message('debug', "Controller getListPerjanjianOperasi - tanggal_mulai received: $tanggal_mulai");
      log_message('debug', "Controller getListPerjanjianOperasi - hari_kerja received: " . json_encode($hari_kerja));
      log_message('debug', "Controller getListPerjanjianOperasi - server date(): " . date('Y-m-d H:i:s'));

      // PERBAIKAN: Gunakan hari_kerja dari frontend jika tersedia, atau hitung tanggal akhir
      if (!empty($hari_kerja) && is_array($hari_kerja)) {
        // Gunakan tanggal terakhir dari hari_kerja sebagai tanggal_akhir
        $tanggal_akhir = end($hari_kerja);
      } else {
        // Fallback: Hitung tanggal akhir dengan logika 3 hari kerja (maksimal 7 hari)
        $tanggal_akhir = date('Y-m-d', strtotime($tanggal_mulai . ' +7 days'));
      }

      $result = $this->PengkajianPraOperasiModel->getListPerjanjianOperasi($tanggal_mulai, $tanggal_akhir, $hari_kerja);

      echo json_encode([
        'status' => 'success',
        'data' => $result,
        'tanggal_mulai' => $tanggal_mulai,
        'tanggal_akhir' => $tanggal_akhir,
        'hari_kerja_received' => $hari_kerja
      ]);
    } catch (Exception $e) {
      echo json_encode([
        'status' => 'error',
        'message' => 'Terjadi kesalahan saat mengambil data perjanjian: ' . $e->getMessage()
      ]);
    }
  }

  /**
   * Fungsi untuk update data reservasi (id_cara_bayar atau id_kelas)
   * Update ke tabel db_reservasi.tb_reservasi
   */
  public function updateReservasi()
  {
    $id_reservasi = $this->input->post('id_reservasi');
    $field = $this->input->post('field');
    $value = $this->input->post('value');

    // Validasi parameter - izinkan value kosong/null
    if (!$id_reservasi || !$field) {
      echo json_encode([
        'status' => 'error',
        'message' => 'ID reservasi dan field harus diisi'
      ]);
      return;
    }

    // Validasi field yang diizinkan untuk di-update
    $allowed_fields = ['id_cara_bayar', 'id_kelas'];
    if (!in_array($field, $allowed_fields)) {
      echo json_encode([
        'status' => 'error',
        'message' => 'Field tidak diizinkan untuk di-update'
      ]);
      return;
    }

    // Jika value kosong, set ke null untuk database
    if ($value === '' || $value === null) {
      $value = null;
    }

    // Mulai transaksi
    $this->db->trans_begin();

    try {
      // Update data reservasi
      $result = $this->PengkajianDafOpeModel->updateReservasi($id_reservasi, $field, $value);
      
      if ($result !== false) { // Gunakan !== false karena affected_rows bisa return 0 jika data sama
        $this->db->trans_commit();
        
        // Pesan yang lebih spesifik
        $field_label = ($field == 'id_cara_bayar') ? 'Cara Bayar' : 'Kelas Rawat';
        $message = $value ? "$field_label berhasil diupdate" : "$field_label berhasil dikosongkan";
        
        echo json_encode([
          'status' => 'success',
          'message' => $message
        ]);
      } else {
        $this->db->trans_rollback();
        echo json_encode([
          'status' => 'error',
          'message' => 'Gagal update data reservasi'
        ]);
      }
    } catch (Exception $e) {
      $this->db->trans_rollback();
      echo json_encode([
        'status' => 'error',
        'message' => 'Terjadi kesalahan: ' . $e->getMessage()
      ]);
    }
  }

  /**
   * Fungsi untuk update tanggal rencana masuk
   * Update ke tabel remun_medis.perjanjian kolom TANGGALRAWATINAP
   */
  public function updateReservasiTanggal()
  {
    $id_reservasi = $this->input->post('id_reservasi');
    $field = $this->input->post('field');
    $value = $this->input->post('value');
    $id_pendaftaran = $this->input->post('id_pendaftaran');

    // Validasi parameter
    if (!$id_reservasi || !$field) {
      echo json_encode([
        'status' => 'error',
        'message' => 'ID reservasi dan field harus diisi'
      ]);
      return;
    }

    // Validasi field yang diizinkan untuk di-update
    if ($field !== 'tgl_rencanaMasuk') {
      echo json_encode([
        'status' => 'error',
        'message' => 'Field tidak diizinkan untuk di-update'
      ]);
      return;
    }

    // Jika value kosong, set ke null untuk database
    if ($value === '' || $value === null) {
      $value = null;
    }

    // Mulai transaksi
    $this->db->trans_begin();

    try {
      // Update tgl_rencanaMasuk di db_reservasi.tb_reservasi
      $result1 = $this->PengkajianDafOpeModel->updateReservasi($id_reservasi, $field, $value);
      
      // Update TANGGALRAWATINAP di remun_medis.perjanjian
      $result2 = $this->PengkajianDafOpeModel->updateTanggalRawatInap($id_pendaftaran, $value);
      
      if ($result1 !== false && $result2 !== false) {
        $this->db->trans_commit();
        
        $message = $value ? "Tanggal rencana masuk berhasil diupdate" : "Tanggal rencana masuk berhasil dikosongkan";
        
        echo json_encode([
          'status' => 'success',
          'message' => $message
        ]);
      } else {
        $this->db->trans_rollback();
        echo json_encode([
          'status' => 'error',
          'message' => 'Gagal update tanggal rencana masuk'
        ]);
      }
    } catch (Exception $e) {
      $this->db->trans_rollback();
      echo json_encode([
        'status' => 'error',
        'message' => 'Terjadi kesalahan: ' . $e->getMessage()
      ]);
    }
  }

  /**
   * Fungsi untuk update tanggal operasi ke 2 tabel
   * Update ke medis.tb_pendaftaran_operasi kolom tanggal_operasi
   * Update ke remun_medis.perjanjian kolom TANGGAL
   */
  public function updateTanggalOperasi()
  {
    $id_pendaftaran = $this->input->post('id_pendaftaran');
    $tanggal_operasi = $this->input->post('tanggal_operasi');

    // Validasi parameter
    if (!$id_pendaftaran || !$tanggal_operasi) {
      echo json_encode([
        'status' => 'error',
        'message' => 'ID pendaftaran dan tanggal operasi harus diisi'
      ]);
      return;
    }

    // Mulai transaksi
    $this->db->trans_begin();

    try {
      // Update tanggal_operasi di medis.tb_pendaftaran_operasi
      $result1 = $this->PengkajianDafOpeModel->updateTanggalOperasiPendaftaran($id_pendaftaran, $tanggal_operasi);
      
      // Update TANGGAL di remun_medis.perjanjian
      $result2 = $this->PengkajianDafOpeModel->updateTanggalOperasiPerjanjian($id_pendaftaran, $tanggal_operasi);
      
      if ($result1 !== false && $result2 !== false) {
        $this->db->trans_commit();
        
        echo json_encode([
          'status' => 'success',
          'message' => 'Tanggal operasi berhasil diupdate di perjanjian & pendaftaran operasi'
        ]);
      } else {
        $this->db->trans_rollback();
        echo json_encode([
          'status' => 'error',
          'message' => 'Gagal update tanggal operasi'
        ]);
      }
    } catch (Exception $e) {
      $this->db->trans_rollback();
      echo json_encode([
        'status' => 'error',
        'message' => 'Terjadi kesalahan: ' . $e->getMessage()
      ]);
    }
  }



  /**
   * Method untuk mengambil data perjanjian operasi dengan server-side processing
   * Mendukung filter tanggal, status (Me/Perjanjian/Penjadwalan), dan deteksi konflik
   */
  // public function getDaftarPerjanjianOperasi()
  // {
  //   // Ambil parameter dari DataTables server-side processing
  //   $draw = $this->input->post('draw') ?? 1;
  //   $start = $this->input->post('start') ?? 0;
  //   $length = $this->input->post('length') ?? 10;
  //   $search = $this->input->post('search')['value'] ?? '';

  //   // Ambil parameter filter
  //   $tanggal_dari = $this->input->post('tanggal_dari');
  //   $tanggal_sampai = $this->input->post('tanggal_sampai');
  //   $status_filter = $this->input->post('status_filter') ?? 'penjadwalan,perjanjian'; // Default semua aktif
  //   $me_filter = $this->input->post('me_filter') ?? false; // Default tidak aktif

  //   // Ambil ID dokter dari session untuk filter Me
  //   $id_dokter_session = $this->session->userdata('id_dokter') ?? $this->session->userdata('id') ?? 0;

  //   try {
  //     // Panggil method model untuk mendapatkan data
  //     $result = $this->PengkajianPraOperasiModel->getDaftarPerjanjianOperasi(
  //       $tanggal_dari,
  //       $tanggal_sampai,
  //       $status_filter,
  //       $me_filter,
  //       $id_dokter_session,
  //       $start,
  //       $length,
  //       $search,
  //       $draw
  //     );

  //     // Format response untuk DataTables server-side
  //     $response = [
  //       'draw' => intval($draw),
  //       'recordsTotal' => $result['recordsTotal'],
  //       'recordsFiltered' => $result['recordsFiltered'],
  //       'data' => $result['data']
  //     ];

  //     echo json_encode($response);
  //   } catch (Exception $e) {
  //     echo json_encode([
  //       'draw' => intval($draw),
  //       'recordsTotal' => 0,
  //       'recordsFiltered' => 0,
  //       'data' => [],
  //       'error' => 'Terjadi kesalahan: ' . $e->getMessage()
  //     ]);
  //   }
  // }
  public function getDaftarPerjanjianOperasi()
{
    // Set content type untuk JSON response
    $this->output->set_content_type('application/json');
    
    // Ambil parameter dari DataTables server-side processing
    $draw = $this->input->post('draw') ?? 1;
    $start = $this->input->post('start') ?? 0;
    $length = $this->input->post('length') ?? 10;
    $search = $this->input->post('search')['value'] ?? '';

    // Ambil parameter filter
    $tanggal_dari = $this->input->post('tanggal_dari');
    $tanggal_sampai = $this->input->post('tanggal_sampai');
    $status_filter = $this->input->post('status_filter') ?? 'penjadwalan,perjanjian';
    $me_filter = $this->input->post('me_filter') ?? false;

    // Konversi me_filter ke boolean
    if ($me_filter === 'true' || $me_filter === '1' || $me_filter === 1) {
        $me_filter = true;
    } else {
        $me_filter = false;
    }

    // Ambil ID dokter dari session untuk filter Me
    $id_dokter_session = $this->session->userdata('id_dokter') ?? $this->session->userdata('id') ?? 0;

    // Debug log (opsional, bisa dihapus di production)
    log_message('debug', 'Filter parameters: ' . json_encode([
        'tanggal_dari' => $tanggal_dari,
        'tanggal_sampai' => $tanggal_sampai,
        'status_filter' => $status_filter,
        'me_filter' => $me_filter,
        'id_dokter_session' => $id_dokter_session,
        'search' => $search
    ]));

    try {
        // Panggil method model untuk mendapatkan data
        $result = $this->PengkajianPraOperasiModel->getDaftarPerjanjianOperasi(
            $tanggal_dari,
            $tanggal_sampai,
            $status_filter,
            $me_filter,
            $id_dokter_session,
            $start,
            $length,
            $search,
            $draw
        );

        // Pastikan response sesuai format DataTables
        $response = [
            'draw' => intval($draw),
            'recordsTotal' => intval($result['recordsTotal']),
            'recordsFiltered' => intval($result['recordsFiltered']),
            'data' => $result['data']
        ];

        // Output JSON response
        echo json_encode($response);

    } catch (Exception $e) {
        // Log error untuk debugging
        log_message('error', 'getDaftarPerjanjianOperasi error: ' . $e->getMessage());
        
        // Return error response
        echo json_encode([
            'draw' => intval($draw),
            'recordsTotal' => 0,
            'recordsFiltered' => 0,
            'data' => [],
            'error' => 'Terjadi kesalahan: ' . $e->getMessage()
        ]);
    }
}

  /**
   * Method untuk mengambil  filtere tanggal operasi 
   */
  public function getTanggalOperasiDistinct()
  {
    try {
      $result = $this->PengkajianPraOperasiModel->getTanggalOperasiDistinct();
      $data = [];
      foreach ($result as $row) {
        $data[] = [
          'id' => $row['tgl_operasi'],
          'text' => date('d/m/Y', strtotime($row['tgl_operasi']))
        ];
      }
      echo json_encode($data);
    } catch (Exception $e) {
      echo json_encode([
        'error' => 'Terjadi kesalahan: ' . $e->getMessage()
      ]);
    }
  }

  /**
   * Helper method untuk insert log pendaftaran operasi
   * Parse slot_operasi (format: "16:00-17:00,17:00-18:00") dan insert per slot
   * 
   * @param int $idPerjanjian ID perjanjian dari remun_medis.perjanjian
   * @param string $tanggal Tanggal operasi
   * @param int|null $idKamar ID kamar operasi
   * @param string $slotOperasi Slot operasi dalam format "16:00-17:00,17:00-18:00"
   */
  private function insertLogPendaftaranOperasi($idPerjanjian, $tanggal, $idKamar, $slotOperasi)
  {
    try {
      // Parse slot operasi - pisahkan berdasarkan koma
      $slots = explode(',', $slotOperasi);
      
      foreach ($slots as $slot) {
        $slot = trim($slot);
        
        // Parse jam mulai dan jam akhir dari format "16:00-17:00"
        if (strpos($slot, '-') !== false) {
          list($jamMulai, $jamAkhir) = explode('-', $slot);
          $jamMulai = trim($jamMulai);
          $jamAkhir = trim($jamAkhir);
          
          // Data untuk insert ke log.log_pendaftaran_operasi
          $dataLog = [
            'ID_PERJANJIAN' => $idPerjanjian,
            'TANGGAL' => $tanggal,
            'ID_TB_KAMAR' => $idKamar,
            'JAM_MULAI' => $jamMulai,
            'JAM_AKHIR' => $jamAkhir
          ];
          
          // Insert ke database melalui model
          $this->PengkajianDafOpeModel->insertLogPendaftaranOperasi($dataLog);
          
          // Log untuk debugging
          log_message('info', 'Insert log pendaftaran operasi: ID_PERJANJIAN=' . $idPerjanjian . 
                             ', TANGGAL=' . $tanggal . ', JAM_MULAI=' . $jamMulai . ', JAM_AKHIR=' . $jamAkhir);
        }
      }
    } catch (Exception $e) {
      // Log error tapi jangan gagalkan transaksi utama
      log_message('error', 'Error inserting log pendaftaran operasi: ' . $e->getMessage());
    }
  }
}

/* End of file PengkajianDafOpe.php */
/* Location: ./application/controllers/operasi/PengkajianDafOpe.php */

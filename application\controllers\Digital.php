<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Digital extends CI_Controller {

  public function __construct()
  {
    parent::__construct();
    if($this->session->userdata('logged_in') == FALSE ){
      redirect('login');  
    }
    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('ModelDigital'));
  }

  public function index_arsip()
  {
    $data=array(      
      // 'listkat'        => $this->ModelInput->tampilKategori(),
      // 'listperusahaan' => $this->ModelInput->listPerusahaan(),
      // 'listno'         => $this->ModelInput->listnoPks(),
      // 'namapasien'  => $this->ModelDigital->cek_nomr($nomr),
      'isi'            => 'Digital/Index_arsip'
    );
    $this->load->view('Layout/Wrapper',$data);
  }

  public function index_scan()
  {
    $data=array(      
      // 'listkat'        => $this->ModelInput->tampilKategori(),
      // 'listperusahaan' => $this->ModelInput->listPerusahaan(),
      // 'listno'         => $this->ModelInput->listnoPks(),
      'isi'            => 'Digital/Index_scan'
    );
    $this->load->view('Layout/Wrapper',$data);
  }

  public function tab_scan_rj() {
    $this->load->view('Digital/tab_scan_rj');
  }

  public function tab_scan_ri() {
    $id_petugas = $this->session->userdata('id'); 

    // $data = array(      
    //     'statusPetugas' => $this->ModelDigital->getStatusPetugas($id_petugas) 
    // );
    $this->load->view('Digital/tab_scan_ri');
  }

  public function tab_scan_ll() {
    $this->load->view('Digital/tab_scan_ll');
  }
  public function getPetugas() {
    $id_petugas = $this->session->userdata('id'); 
    $statusPetugas = $this->ModelDigital->getStatusPetugas($id_petugas); 

    echo json_encode($statusPetugas); 
  }


  public function CekNomrX()
{
    $nomr = $this->input->post('NOMR');

    $cekmr = $this->ModelDigital->cek_nomr($nomr);
    
    if ($cekmr->num_rows() > 0) {
        $result = $cekmr->row_array();
        $row = array(
            'nomr' => $result['NORM'],
            'nama' => $result['NAMA'],
            'tanggal' => $result['TANGGAL']
        );
    } else {
        $row = array(
            'nomr' => '',
            'nama' => 'Data tidak ditemukan'
        );
    }

    echo json_encode($row);
}

public function CekNomrRJ()
{
  $nomr = $this->input->post('NOMR');
  $cekmr = $this->ModelDigital->cek_nomr_rj($nomr);

  if ($cekmr->num_rows() > 0) {
    $result = $cekmr->result_array();
    $data = array(
            'nomr' => $result[0]['NORM'], // Asumsi NORM sama untuk semua kunjungan
            'nama' => $result[0]['NAMA'], // Asumsi NAMA sama untuk semua kunjungan
            // 'kunjungan' => array()
          );

    // foreach ($result as $row) {
    //   $data['kunjungan'][] = $row['TANGGAL'];
    // }
  } else {
    $data = array(
      'nomr' => '',
      'nama' => 'Data tidak ditemukan',
      // 'kunjungan' => array()
    );
  }

  echo json_encode($data);
}

public function CekNomrRI()
{
  $nomr = $this->input->post('NOMR');
  $cekmr = $this->ModelDigital->cek_nomr_ri($nomr);

  if ($cekmr->num_rows() > 0) {
    $result = $cekmr->result_array();
    $data = array(
            'nomr' => $result[0]['NORM'], // Asumsi NORM sama untuk semua kunjungan
            'nama' => $result[0]['NAMA'], // Asumsi NAMA sama untuk semua kunjungan
            // 'kunjungan' => array()
          );

    // foreach ($result as $row) {
    //   $data['kunjungan'][] = $row['TANGGAL'];
    // }
  } else {
    $data = array(
      'nomr' => '',
      'nama' => 'Data tidak ditemukan di rawat inap',
      'kunjungan' => array()
    );
  }

  echo json_encode($data);
}
public function getTglDatangOptions() {
  $nomr = $this->input->get('NOMR'); 
  $query = $this->input->get('q'); 
  $result = $this->ModelDigital->getTglDatangOptions($nomr, $query);

  $data = array();
  foreach ($result as $row) {
      $data[] = [
          'id' => $row['TANGGAL'],
          'text' => date('d-m-Y H:i:s', strtotime($row['TANGGAL']))
      ];
  }

  echo json_encode($data);
}
public function getTglDatangOptionsJalan() {
  $nomr = $this->input->get('NOMR'); 
  $query = $this->input->get('q'); 
  $result = $this->ModelDigital->getTglJalan($nomr, $query);

  $data = array();
  foreach ($result as $row) {
      $data[] = [
          'id' => $row['TANGGAL'],
          'text' => date('d-m-Y H:i:s', strtotime($row['TANGGAL']))
      ];
  }

  echo json_encode($data);
}


  public function CekNomrLL()
  {
    $nomr = $this->input->post('NOMR');
    $cekmr = $this->ModelDigital->cek_nomr_ll($nomr);

    if ($cekmr->num_rows() > 0) {
      $result = $cekmr->result_array();
      $data = array(
              'nomr' => $result[0]['NORM'], // Asumsi NORM sama untuk semua kunjungan
              'nama' => $result[0]['NAMA'], // Asumsi NAMA sama untuk semua kunjungan
              // 'kunjungan' => array(),
              // 'ruangan' => array(),
              // 'id_ruangan' => array()
            );

      // foreach ($result as $row) {
      //   $data['kunjungan'][] = $row['TANGGAL'];
      //   $data['ruangan'][] = $row['DESKRIPSI'];
      //   $data['id_ruangan'][] = $row['ID_RUANGAN'];
      // }
    } else {
      $data = array(
        'nomr' => '',
        'nama' => 'Data tidak ditemukan',
        // 'kunjungan' => array(),
        // 'ruangan' => array(),
        // 'id_ruangan' => array()
      );
    }

    echo json_encode($data);
  }

  public function getTglDatangLL() {
    $nomr = $this->input->get('NOMR'); 
    $query = $this->input->get('q'); 
    $limit = 10; 
    $result = $this->ModelDigital->getTglDatangLL($nomr, $query, $limit);

    $data = array();
    foreach ($result as $row) {
        $data[] = [
            'id' => $row['TANGGAL'], 
            'text' => date('d-m-Y H:i:s', strtotime($row['TANGGAL'])), 
            'ruangan' => $row['DESKRIPSI'],
            'idRuangan'=> $row['ID_RUANGAN'] 
        ];
    }

    echo json_encode($data);
  }

  public function list_ruangan()
    {
        $result = $this->ModelDigital->list_Ruangan();
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row['ID_SIMPEL'];
            $sub_array['text'] = $row['DESKRIPSI'];
            $data[] = $sub_array;
        }
        echo json_encode($data);
    }

  public function list_jumlah()
    {
        $result = $this->ModelAssembling->list_Jumlah();
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row['ID'];
            $sub_array['text'] = $row['DESKRIPSI'];
            $data[] = $sub_array;
        }
        echo json_encode($data);
    }

  public function list_jumlah1()
    {
        $result = $this->ModelAssembling->list_Jumlah();
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row['ID'];
            $sub_array['text'] = $row['DESKRIPSI'];
            $data[] = $sub_array;
        }
        echo json_encode($data);
    }

    public function list_volume()
    {
        $result = $this->ModelDigital->list_Volume();
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row['ID'];
            $sub_array['text'] = $row['DESKRIPSI'];
            $data[] = $sub_array;
        }
        echo json_encode($data);
    }

    public function list_petugas()
    {
        $result = $this->ModelDigital->list_Petugas();
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row['id'];
            $sub_array['text'] = $row['nama_lengkap'];
            $data[] = $sub_array;
        }
        echo json_encode($data);
    }

    public function list_lokasi()
    {
        $result = $this->ModelDigital->list_Lokasi();
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row['ID'];
            $sub_array['text'] = $row['DESKRIPSI'];
            $data[] = $sub_array;
        }
        echo json_encode($data);
    }

    public function list_jenis()
    {
        $result = $this->ModelDigital->list_Jenis();
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row['ID'];
            $sub_array['text'] = $row['DESKRIPSI'];
            $data[] = $sub_array;
        }
        echo json_encode($data);
    }

    public function list_box()
    {
        $result = $this->ModelDigital->list_Box();
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row['ID'];
            $sub_array['text'] = $row['DESKRIPSI'];
            $data[] = $sub_array;
        }
        echo json_encode($data);
    }

    public function list_form()
    {
        $result = $this->ModelDigital->list_Form();
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row['ID'];
            $sub_array['text'] = $row['DESKRIPSI'];
            $data[] = $sub_array;
        }
        echo json_encode($data);
    }

  public function hapusArsip()
  {
    $this->db->trans_begin();
    $post = $this->input->post();
    $id =$post['ID'];

    
    $dataUpdate = array (
      'STATUS'                           => 0,
    );
    
    // echo "<pre>";print_r($id);exit();
    $this->db->where('db_rekammedis.tb_arsip.ID', $id);
    $this->db->update('db_rekammedis.tb_arsip', $dataUpdate);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

  public function hapusScanll()
  {
    $this->db->trans_begin();
    $post = $this->input->post();
    $id =$post['ID'];

    
    $dataUpdate = array (
      'STATUS'                           => 0,
    );
    
    // echo "<pre>";print_r($id);exit();
    $this->db->where('db_rekammedis.tb_digital.ID', $id);
    $this->db->update('db_rekammedis.tb_digital', $dataUpdate);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

  public function hapusScanrj()
  {
    $this->db->trans_begin();
    $post = $this->input->post();
    $id =$post['ID'];


    $dataUpdate = array (
      'STATUS'                           => 0,
    );

  // echo "<pre>";print_r($id);exit();
    $this->db->where('db_rekammedis.tb_digital.ID', $id);
    $this->db->update('db_rekammedis.tb_digital', $dataUpdate);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

  public function hapusScanri()
  {
    $this->db->trans_begin();
    $post = $this->input->post();
    $id =$post['ID'];

    
    $dataUpdate = array (
      'STATUS'                           => 0,
    );
    
  // echo "<pre>";print_r($id);exit();
    $this->db->where('db_rekammedis.tb_digital.ID', $id);
    $this->db->update('db_rekammedis.tb_digital', $dataUpdate);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
  }

  

  public function simpanScanRj()
  { 
    $this->db->trans_begin();

    $post = $this->input->post();
    // echo "<pre>";print_r($post);exit();
    $tglcreate = date('Y-m-d H:i:s');
    $tanggal = $this->input->post('TANGGAL_SELECT_SCAN_RJ');
    if(empty($tanggal)) {
        $tanggal = $this->input->post('TANGGAL_INPUT_SCAN_RJ');
    }
                 
    $data_insert_rj = array (
      'NOMR'       => $this->input->post('NOMR_SCAN_RJ'),
      'NAMA_PASIEN'         => $this->input->post('NAMA_SCAN_RJ'),
      // 'TANGGAL_KUNJUNGAN'       => $this->input->post('TANGGAL_KUNJUNGAN_SCAN_RJ'),
      'TANGGAL_KUNJUNGAN'       => $tanggal,
      // 'ID_PETUGAS'       => $this->input->post('PETUGAS_SCAN_RJ'),
      'ID_PETUGAS'       => $this->session->userdata('id'),
      'STATUS'       => 1,
      // 'TANGGAL_INPUT'            => $tglcreate,
      // 'OLEH'          => $this->session->userdata('id'),
    );
   // echo "<pre>";print_r($data_insert);exit();
    $this->db->insert('db_rekammedis.tb_digital', $data_insert_rj);

    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
      } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
      }
 
    echo json_encode($result);
    
  }

  public function simpanScanRi()
  { 
    $this->db->trans_begin();

    $post = $this->input->post();
    // echo "<pre>";print_r($post);exit();
    // $tglcreate = date('Y-m-d H:i:s');
    $tanggal = $this->input->post('TANGGAL_SELECT_SCAN_RI');
    if(empty($tanggal)) {
        $tanggal = $this->input->post('TANGGAL_INPUT_SCAN_RI');
    }
                 
    $data_insert_ri = array (
      'NOMR'       => $this->input->post('NOMR_SCAN_RI'),
      'NAMA_PASIEN'         => $this->input->post('NAMA_SCAN_RI'),
      // 'TANGGAL_KUNJUNGAN'       => $this->input->post(''),
      'TANGGAL_KUNJUNGAN'       => $tanggal,
      'ID_PETUGAS'       =>  $this->session->userdata('id'),
      'STATUS'       => 2,
      'TANGGAL_INPUT'            => date('Y-m-d H:i:s'),
      // 'OLEH'          => $this->session->userdata('id'),
    );
   // echo "<pre>";print_r($data_insert);exit();
    $this->db->insert('db_rekammedis.tb_digital', $data_insert_ri);

    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
      } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
      }
 
    echo json_encode($result);
    
  }

  public function simpanScanLL()
  { 
    $this->db->trans_begin();

    $post = $this->input->post();
    // echo "<pre>";print_r($post);exit();
    $tglcreate = date('Y-m-d H:i:s');
    
    $tanggal = $this->input->post('TANGGAL_SELECT_SCAN_LL');
    if(empty($tanggal)) {
        $tanggal = $this->input->post('TANGGAL_INPUT_SCAN_LL');
    }

    $id_ruangan = $this->input->post('ID_RUANGAN_SCAN_LL');
    // if(empty($id_ruangan)) {
    //     $result = array('status' => 'failed', 'errors' => array('Ruangan harus dipilih'));
    //     echo json_encode($result);
    //     return;
    // }
              
    $data_insert_ll = array (
      'NOMR'                => $this->input->post('NOMR_SCAN_LL'),
      'NAMA_PASIEN'         => $this->input->post('NAMA_SCAN_LL'),
      'TANGGAL_KUNJUNGAN'   => $tanggal,
      'TANGGAL_INPUT'       => $this->input->post('TANGGAL_INPUT_LL'),
      'ID_RUANGAN'          => $id_ruangan,
      'JENIS_FORM'          => $this->input->post('JENIS_FORM_LL'),
      // 'ID_PETUGAS'          => $this->input->post('PETUGAS_SCAN_LL'),
      'ID_PETUGAS'          =>  $this->session->userdata('id'),
      'STATUS'              => 3,
      // 'TANGGAL_INPUT'            => $tglcreate,
      // 'OLEH'          => $this->session->userdata('id'),
    );
   // echo "<pre>";print_r($data_insert);exit();
    $this->db->insert('db_rekammedis.tb_digital', $data_insert_ll);

    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
      } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
      }
 
    echo json_encode($result);
    
  }

  public function simpanArsip()
  { 
    $this->db->trans_begin();

    $post = $this->input->post();
    // echo "<pre>";print_r($post);exit();
    $lembar_form = $this->input->post('LEMBAR_FORMULIR_ARSIP');
    $lembar_form_id = implode(',', $lembar_form);
    $tglcreate = date('Y-m-d H:i:s');
                 
    $data_insert_arsip = array (
      'NOMR'       => $this->input->post('NOMR_ARSIP'),
      'NAMA_PASIEN'         => $this->input->post('NAMA_ARSIP'),
      'TANGGAL_KUNJUNGAN'       => $this->input->post('TANGGAL_KUNJUNGAN_ARSIP'),
      'TANGGAL_INPUT'       => $this->input->post('TANGGAL_INPUT_ARSIP'),
      'ID_JENIS_FORM'       => $this->input->post('JENIS_FORMULIR_ARSIP'),
      'ID_LEMBAR_FORM'       => $lembar_form_id,
      'ID_BOX'         => $this->input->post('NAMA_BOX_ARSIP'),
      'ID_PETUGAS'       => $this->input->post('PETUGAS_ARSIP'),
      // 'TANGGAL_INPUT'            => $tglcreate,
      // 'OLEH'          => $this->session->userdata('id'),
    );
   // echo "<pre>";print_r($data_insert);exit();
    $this->db->insert('db_rekammedis.tb_arsip', $data_insert_arsip);

    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
      } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
      }
 
    echo json_encode($result);
    
  }

public function getListLembar() {
    $jenisFormulirId = $this->input->post('id');

    switch ($jenisFormulirId) {
      case 6:
        $jenisform = 11;
      break;
      case 7:
        $jenisform = 10;
      break;
      case 8:
        $jenisform = 12;
      break;
      case 21:
        $jenisform = 13;
      break;
      case 28:
        $jenisform = 9;
      break;      
    }

    $listLembar = $this->ModelDigital->getLembarByJenisFormulir($jenisform);
    echo json_encode($listLembar);
}


  public function modalEditArsip()
  {
    if($this->input->is_ajax_request()==true){
      $id = $this->input->post('id');
      $ambildata = $this->ModelDigital->editArsip($id);

      

      if($ambildata->num_rows()>0){
        $row=$ambildata->row_array();

        $lembar_form_id = explode(',', $row['ID_LEMBAR_FORM']);
        $lembar_form = $this->ModelDigital->get_form_id($lembar_form_id);
        $lembar_lepas = array_column($lembar_form, 'DESKRIPSI');
        // $form_lembar_lepas = implode(', ', $lembar_lepas);

        $data=[
          
          // 'listkat'           => $this->ModelPerusahaan->tampilKategori(),
          'ID'                  => $row['ID'],
          'NOMR'                => $row['NOMR'],
          'NAMA_PASIEN'         => $row['NAMA_PASIEN'],
          'TANGGAL_KUNJUNGAN'                => $row['TANGGAL_KUNJUNGAN'],
          'TANGGAL_INPUT'                => $row['TANGGAL_INPUT'],
          'ID_JENIS_FORM'                => $row['ID_JENIS_FORM'],
          'JENIS_FORM'                => $row['JENIS_FORM'],
          'ID_BOX'                => $row['ID_BOX'],
          'NAMA_BOX'                => $row['NAMA_BOX'],
          'ID_PETUGAS'                => $row['ID_PETUGAS'],
          'PETUGAS'                => $row['PETUGAS'],
          'id_form_lembar_lepas'                => $row['ID_LEMBAR_FORM'],
          'lembar_lepas'                => $lembar_lepas,
        ];
      }
      $msg=[
        'sukses'=>$this->load->view('Digital/modal_edit_arsip', $data, true)
      ];
      echo json_encode($msg);
    }
  }

  function modalSearchArsip()
  {
    $this->load->view('Digital/modal_search_arsip');
  }

  

  public function modalEditScanRJ()
  {
    if($this->input->is_ajax_request()==true){
      $id = $this->input->post('id');
      $ambildata = $this->ModelDigital->editScanRJ($id);
      if($ambildata->num_rows()>0){
        $row=$ambildata->row_array();
        $data=[
          
          // 'listkat'           => $this->ModelPerusahaan->tampilKategori(),
          'ID'                  => $row['ID'],
          'NOMR'                => $row['NOMR'],
          'NAMA_PASIEN'         => $row['NAMA_PASIEN'],
          'TANGGAL_KUNJUNGAN'                => $row['TANGGAL_KUNJUNGAN'],
          'ID_PETUGAS'                => $row['ID_PETUGAS'],
          'PETUGAS'                => $row['PETUGAS'],
        ];
      }
      $msg=[
        'sukses'=>$this->load->view('Digital/modal_edit_scanrj', $data, true)
      ];
      echo json_encode($msg);
    }
  }

  public function modalEditScanRI()
  {
    if($this->input->is_ajax_request()==true){
      $id = $this->input->post('id');
      $ambildata = $this->ModelDigital->editScanRJ($id);
      if($ambildata->num_rows()>0){
        $row=$ambildata->row_array();
        $data=[
          
          // 'listkat'           => $this->ModelPerusahaan->tampilKategori(),
          'ID'                  => $row['ID'],
          'NOMR'                => $row['NOMR'],
          'NAMA_PASIEN'         => $row['NAMA_PASIEN'],
          'TANGGAL_KUNJUNGAN'                => $row['TANGGAL_KUNJUNGAN'],
          'ID_PETUGAS'                => $row['ID_PETUGAS'],
          'PETUGAS'                => $row['PETUGAS'],
        ];
      }
      $msg=[
        'sukses'=>$this->load->view('Digital/modal_edit_scanri', $data, true)
      ];
      echo json_encode($msg);
    }
  }

  public function modalEditScanLL()
  {
    if($this->input->is_ajax_request()==true){
      $id = $this->input->post('id');
      $ambildata = $this->ModelDigital->editScanLL($id);
      if($ambildata->num_rows()>0){
        $row=$ambildata->row_array();
        $data=[
          
          // 'listkat'           => $this->ModelPerusahaan->tampilKategori(),
          'ID'                  => $row['ID'],
          'NOMR'                => $row['NOMR'],
          'NAMA_PASIEN'         => $row['NAMA_PASIEN'],
          'TANGGAL_KUNJUNGAN'                => $row['TANGGAL_KUNJUNGAN'],
          'TANGGAL_INPUT'                => $row['TANGGAL_INPUT'],
          'ID_RUANGAN'                => $row['ID_RUANGAN'],
          'RUANGAN'                => $row['RUANGAN'],
          'ID_JENIS'                => $row['ID_JENIS'],
          'JENIS_FORM'                => $row['JENIS_FORM'],
          'ID_PETUGAS'                => $row['ID_PETUGAS'],
          'PETUGAS'                => $row['PETUGAS'],
        ];
      }
      $msg=[
        'sukses'=>$this->load->view('Digital/modal_edit_scanll', $data, true)
      ];
      echo json_encode($msg);
    }
  }

public function simpanEdit()
  {
    $post = $this->input->post();
    
    $this->db->trans_begin();

    $id = $this->input->post('ID');

    $tgledit = date('Y-m-d H:i:s');
                 
    $data_insert = array (
      // 'NOMR'       => $this->input->post('NOMR'),
      // 'NAMA_PASIEN'         => $this->input->post('NAMA'),
      'JUMLAH_RM'       => $this->input->post('JUMLAH_RM_EDIT'),
      'VOLUME_RM'       => $this->input->post('VOLUME_RM_EDIT'),
      // 'TANGGAL_KUNJUNGAN'       => $this->input->post('TANGGAL_KUNJUNGAN'),
      // 'ID_PETUGAS'       => $this->input->post('PETUGAS'),
      // 'TANGGAL_ASSEMBLING'       => $this->input->post('TANGGAL_ASS'),
      // 'TANGGAL_INPUT'            => $tglcreate,
      // 'OLEH'          => $this->session->userdata('id'),
    );
   // echo "<pre>";print_r($data_insert);exit();
    $this->db->where('ID', $id);
    $this->db->update('db_rekammedis.tb_assembling', $data_insert);

    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
      } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
      }
 
    echo json_encode($result);
    
  }

  public function simpanEditArsip()
  {
    $post = $this->input->post();
    
    $this->db->trans_begin();

    $id = $this->input->post('ID');

    $tgledit = date('Y-m-d H:i:s');

    $lembar_form = $this->input->post('LEMBAR_EDIT_ARSIP');
    $lembar_form_id = implode(',', $lembar_form);
                 
    $data_insert = array (
      // 'NOMR'       => $this->input->post('NOMR'),
      // 'NAMA_PASIEN'         => $this->input->post('NAMA'),
      'ID_JENIS_FORM'       => $this->input->post('JENIS_FORM_EDIT_ARSIP'),
      'ID_BOX'       => $this->input->post('NAMA_BOX_EDIT_ARSIP'),
      'ID_PETUGAS'       => $this->input->post('PETUGAS_EDIT_ARSIP'),
      'ID_LEMBAR_FORM'       => $lembar_form_id,
      // 'TANGGAL_ASSEMBLING'       => $this->input->post('TANGGAL_ASS'),
      // 'TANGGAL_INPUT'            => $tglcreate,
      // 'OLEH'          => $this->session->userdata('id'),
    );
   // echo "<pre>";print_r($data_insert);exit();
    $this->db->where('ID', $id);
    $this->db->update('db_rekammedis.tb_arsip', $data_insert);

    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
      } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
      }
 
    echo json_encode($result);
    
  }

  public function simpanEditScanrj()
  {
    $post = $this->input->post();
    
    $this->db->trans_begin();

    $id = $this->input->post('ID');

    $tgledit = date('Y-m-d H:i:s');
                 
    $data_insert_scanrj = array (
      // 'NOMR'       => $this->input->post('NOMR'),
      // 'NAMA_PASIEN'         => $this->input->post('NAMA'),
      'ID_PETUGAS'       => $this->input->post('PETUGAS_EDIT_SCANRJ'),
      // 'TANGGAL_KUNJUNGAN'       => $this->input->post('TANGGAL_KUNJUNGAN'),
      // 'ID_PETUGAS'       => $this->input->post('PETUGAS'),
      // 'TANGGAL_ASSEMBLING'       => $this->input->post('TANGGAL_ASS'),
      // 'TANGGAL_INPUT'            => $tglcreate,
      // 'OLEH'          => $this->session->userdata('id'),
    );
   // echo "<pre>";print_r($data_insert);exit();
    $this->db->where('ID', $id);
    $this->db->update('db_rekammedis.tb_digital', $data_insert_scanrj);

    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
      } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
      }
 
    echo json_encode($result);
    
  }

  public function simpanEditScanll()
  {
    $post = $this->input->post();
    
    $this->db->trans_begin();

    $id = $this->input->post('ID');

    $tgledit = date('Y-m-d H:i:s');
                 
    $data_insert_scanll = array (
      // 'NOMR'       => $this->input->post('NOMR'),
      // 'NAMA_PASIEN'         => $this->input->post('NAMA'),
      'ID_PETUGAS'       => $this->input->post('PETUGAS_EDIT_SCANLL'),
      // 'ID_RUANGAN'       => $this->input->post('UNIT_EDIT_SCANLL'),
      'JENIS_FORM'       => $this->input->post('JENIS_FORM_EDIT_SCANLL'),
      // 'TANGGAL_KUNJUNGAN'       => $this->input->post('TANGGAL_KUNJUNGAN'),
      // 'ID_PETUGAS'       => $this->input->post('PETUGAS'),
      // 'TANGGAL_ASSEMBLING'       => $this->input->post('TANGGAL_ASS'),
      // 'TANGGAL_INPUT'            => $tglcreate,
      // 'OLEH'          => $this->session->userdata('id'),
    );
   // echo "<pre>";print_r($data_insert);exit();
    $this->db->where('ID', $id);
    $this->db->update('db_rekammedis.tb_digital', $data_insert_scanll);

    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
      } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
      }
 
    echo json_encode($result);
    
  }

  public function simpanEditScanri()
  {
    $post = $this->input->post();
    
    $this->db->trans_begin();

    $id = $this->input->post('ID');

    $tgledit = date('Y-m-d H:i:s');
                 
    $data_insert_scanri = array (
      // 'NOMR'       => $this->input->post('NOMR'),
      // 'NAMA_PASIEN'         => $this->input->post('NAMA'),
      'ID_PETUGAS'       => $this->input->post('PETUGAS_EDIT_SCANRI'),
      // 'TANGGAL_KUNJUNGAN'       => $this->input->post('TANGGAL_KUNJUNGAN'),
      // 'ID_PETUGAS'       => $this->input->post('PETUGAS'),
      // 'TANGGAL_ASSEMBLING'       => $this->input->post('TANGGAL_ASS'),
      // 'TANGGAL_INPUT'            => $tglcreate,
      // 'OLEH'          => $this->session->userdata('id'),
    );
   // echo "<pre>";print_r($data_insert);exit();
    $this->db->where('ID', $id);
    $this->db->update('db_rekammedis.tb_digital', $data_insert_scanri);

    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
      } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
      }
 
    echo json_encode($result);
    
  }

  public function getdataArsip()
  {
    // $draw   = intval($this->input->POST("draw"));
    // $start  = intval($this->input->POST("start"));
    // $length = intval($this->input->POST("length"));

    $listdata = $this->ModelDigital->dataListArsip();
    
    $data=array();
    $no =1;
    foreach ($listdata->result() as $field) {

    // $cek = '<input type="checkbox" value="'.$field->ID.'"  class="cek_done">';
      // $edit = '<button type="button" class="btn btn-primary btn-rounded btn-sm" title="edit data" onclick="ceklis('.$field->ID.')" data-id="'.$field->JENIS_FORM.'" style="width:72px"><i class="fa fa-edit"></i> </button>';
      $edit = '<button type="button" class="btn btn-primary btn-rounded btn-sm" title="edit data" onclick="editArsip('.$field->ID.')" style="width:72px"><i class="fa fa-edit"></i></button>';      
      $hapus = '<button type="button" class="btn btn-danger btn-rounded btn-sm hapus_arsip" href="javaScript:;" id="hapus_arsip" title="hapus_arsip" data="'.$field->ID.'" style="width:72px"><i class="fa fa-trash"></i></button>';      
      $edit2 = '<a href="#modalcek" class="btn btn-sm btn-primary" data-toggle="modal" data-id="'.$field->ID.'"><i class="fas fa-search"></i> Lihat</a>';

      $data[] = array(
        $no,

        $field->NOMR,
        $field->NAMA_PASIEN,
         date('d/m/Y H:i:s', strtotime($field->TANGGAL_KUNJUNGAN)),
        $field->TANGGAL_INPUT,
        $field->JENIS_FORM,
        $field->NAMA_BOX,
        $field->PETUGAS,    

        $edit.' '.$hapus
        );
        
      $no++;

    }

    $output = array(
      // "draw"            => $draw,
      // "recordsTotal"    => $listdata->num_rows(),
      // "recordsFiltered" => $listdata->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }

  public function getdataDigitalRj()
  {
    $id_petugas = $this->session->userdata('id');
    $tugas = $this->session->userdata('tugas');
    $draw   = intval($this->input->post("draw"));
    $start  = intval($this->input->post("start"));
    $length = intval($this->input->post("length"));
    $search = $this->input->post("search")['value'];

    $listdata = $this->ModelDigital->dataListDigitalRj($start, $length, $search, $tugas, $id_petugas);
    
    $data = [];
    $no = $start + 1;

    foreach ($listdata['data'] as $field) {
        $edit = '<button type="button" class="btn btn-primary btn-rounded btn-sm" title="Edit Data" onclick="editScanrj(' . $field->ID . ')" style="width:72px"><i class="fa fa-edit"></i></button>';
        $hapus = '<button type="button" class="btn btn-danger btn-rounded btn-sm hapus_scanrj" title="Hapus" data="' . $field->ID . '" style="width:72px"><i class="fa fa-trash"></i></button>';

        $data[] = [
            $no,
            $field->NOMR,
            $field->NAMA_PASIEN,
            date('d/m/Y H:i:s', strtotime($field->TANGGAL_KUNJUNGAN)),
            $field->PETUGAS,
            $edit . ' ' . $hapus
        ];
        $no++;
    }

    $output = [
        "draw" => $draw,
        "recordsTotal" => $listdata['recordsTotal'],
        "recordsFiltered" => $listdata['recordsFiltered'],
        "data" => $data
    ];

    echo json_encode($output);
  }
  

//   public function getdataDigitalRi() {
//     $id_petugas = $this->session->userdata('id');

//     // Tombol dropdown (tidak diganti)
//     $button = '<div class="btn-group">
//                   <button type="button" class="btn btn-light dropdown-toggle waves-effect" data-toggle="dropdown" aria-expanded="false">
//                       <i class="mdi mdi-folder font-18 vertical-middle"></i>
//                       <i class="mdi mdi-chevron-down"></i>
//                   </button>
//                   <div class="dropdown-menu">
//                       <a class="dropdown-item" href="javascript: void(0);">Detail</a>
//                       <a class="dropdown-item" href="javascript: void(0);">Edit</a>
//                       <a class="dropdown-item" href="javascript: void(0);">Hapus</a>
//                   </div>
//               </div>';

//     if ($this->session->userdata('tugas') == '9') {
//         $listdata = $this->ModelDigital->getPetugasScan(); 
//     } else {
//         $listdata = $this->ModelDigital->dataListDigitalRi($id_petugas); 
//     }

//     $data = [];
//     $no = 1;

    
//     foreach ($listdata as $field) {
      
//         // $edit2 = '<a href="#modalcek" class="btn btn-sm btn-primary" data-toggle="modal" data-id="' . $field->ID . '"><i class="fas fa-search"></i> Lihat</a>';
//         if ($this->session->userdata('tugas') == '9') {
//           $lihat='<button type="button" class="btn btn-primary btn-rounded btn-sm" title="Detail Scan" onclick="lihatPetugasScan(' . $field->id . ')" style="width:72px"><i class="fa fa-eye"></i></button>';
//             $data[] = array(
//               $no,
//               $field->petugas, 
//               $field->persentase_pekerjaan. '%',  
//               $field->keterangan,
//               $lihat
//             );
//         } else {
//           $edit = '<button type="button" class="btn btn-primary btn-rounded btn-sm" title="edit data" onclick="editScanri(' . $field->ID . ')" style="width:72px"><i class="fa fa-edit"></i></button>';
//           $hapus = '<button type="button" class="btn btn-danger btn-rounded btn-sm hapus_scanri" href="javaScript:;" id="hapus_scanri" title="hapus_scanri" data="' . $field->ID . '" style="width:72px"><i class="fa fa-trash"></i></button>';
//             $data[] = array(
//                 $no,
//                 $field->NOMR,
//                 $field->NAMA_PASIEN,
//                  date('d/m/Y H:i:s', strtotime($field->TANGGAL_KUNJUNGAN)),
//                 $edit . ' ' . $hapus
//             );
//         }
//         $no++;
//     }

//     $output = array(
//         "data" => $data
//     );

//     echo json_encode($output);
// }

public function getdataDigitalRi() {
  if ($this->session->userdata('tugas') == '9')  {
      $listdata = $this->ModelDigital->getPetugasScan();
      $data = [];
      $no = 1;

      foreach ($listdata as $field) {
          $lihat = '<button type="button" class="btn btn-primary btn-rounded btn-sm" title="Detail Scan" onclick="lihatPetugasScan(' . $field->id . ')" style="width:72px"><i class="fa fa-eye"></i></button>';
          $data[] = [
              $no,
              $field->petugas,
              $field->persentase_pekerjaan . '%',
              $field->keterangan,
              $lihat
          ];
          $no++;
      }

      echo json_encode(['data' => $data]);
  } else {
    $id_petugas = $this->session->userdata('id');
    $start  = intval($this->input->post("start"));
    $length = intval($this->input->post("length"));
    $search = $this->input->post("search")['value'];
    $listdata = $this->ModelDigital->dataListDigitalRi($start, $length, $search,$id_petugas);
    $data = [];
    $no = 1;

    foreach ($listdata['data'] as $field) {
        $edit = '<button type="button" class="btn btn-primary btn-rounded btn-sm" title="Edit Data" onclick="editScanri(' . $field->ID . ')" style="width:72px"><i class="fa fa-edit"></i></button>';
        $hapus = '<button type="button" class="btn btn-danger btn-rounded btn-sm hapus_scanri" title="Hapus" data="' . $field->ID . '" style="width:72px"><i class="fa fa-trash"></i></button>';
        $data[] = [
            $no,
            $field->NOMR,
            $field->NAMA_PASIEN,
            date('d/m/Y H:i:s', strtotime($field->TANGGAL_KUNJUNGAN)),
            $edit . ' ' . $hapus
        ];
        $no++;
    }

    $output = array(
      "draw" => $this->input->post("draw"),
      "recordsTotal" => $listdata['recordsTotal'],
      "recordsFiltered" => $listdata['recordsFiltered'],
      "data" => $data
  );
  echo json_encode($output);
  }
}


public function modallihatPetugasScan()
  {
      $id_petugas = $this->input->post('id');
      $ambildata = $this->ModelDigital->judul($id_petugas);
      $data = [];
      if($ambildata->num_rows()>0){
        $row=$ambildata->row_array();
        $data=[
          'id'    => $row['id'],
          'NIP'   => $row['NIP'],
          'NAMA'  => $row['NAMA'],
        ];
      }
      $msg=[
        'sukses'=>$this->load->view('Digital/modal_PetugasScan', $data, true)
      ];
      echo json_encode($msg);
  }

  public function getDetDigitalRi() {
    $id_petugas = $this->input->post('id');   
    $start = $this->input->post('start');  
    $length = $this->input->post('length'); 
    $search = $this->input->post('search')['value']; 

    $listdata = $this->ModelDigital->dataListDigitalRi($start, $length, $search, $id_petugas); 
   
    $data = [];
    $no = 1;    
    foreach ($listdata['data']  as $field) {
      $Tanggal = date('d/m/Y', strtotime($field->Tanggal));
      $TANGGAL_KUNJUNGAN = date('d/m/Y H:i:s', strtotime($field->TANGGAL_KUNJUNGAN));
      $data[] = array(
          $no,
          $Tanggal,
          $field->NOMR,
          $field->NAMA_PASIEN,
          $TANGGAL_KUNJUNGAN,
      );    
      $no++;
    }

   $output = array(
        "draw" => intval($this->input->post('draw')),
        "recordsTotal" => $listdata['recordsTotal'],
        "recordsFiltered" => $listdata['recordsFiltered'],
        "data" => $data
    );

    echo json_encode($output);
}

//rj semprawut
public function getTanggal() {
  $jenisForm = $this->input->get('jenisForm');
  $search = $this->input->get('search');

  $data = $this->ModelDigital->get_tanggal_list($jenisForm, $search);
  echo json_encode($data);
}

// public function ajax_list() {
//   $start = $this->input->post('start');
//   $length = $this->input->post('length');
//   $search = $this->input->post('search')['value'];
//   $tanggal = $this->input->post('tanggalPelayanan');

//   $data = $this->ModelDigital->get_datatables($length, $start, $search, $tanggal);
//   $recordsTotal = $this->ModelDigital->count_all($tanggal);
//   $recordsFiltered = $this->ModelDigital->count_filtered($search, $tanggal);

//   $output = [
//       "draw" => intval($this->input->post("draw")),
//       "recordsTotal" => $recordsTotal,
//       "recordsFiltered" => $recordsFiltered,
//       "data" => []
//   ];

//   $no = $start + 1;
//   foreach ($data as $row) {
//       $output['data'][] = [
//           "no" => $no++,
//           "nomr" => $row->nomr,
//           "nama_pasien" => $row->nama_pasien,
//           "tanggal_pelayanan" => $row->tanggal_pelayanan,
//           "nama_pegawai" => $row->nama_pegawai,
//           "berkas" => '<button class="btn btn-sm btn-primary" onclick="showBerkasDetail(\''. $row->nopen .'\')" data-nopen="'. $row->nopen .'">Lihat Berkas</button>',
//           "action" => '<button class="btn btn-sm btn-secondary" disabled><i class="fas fa-edit"></i> </button>'
//       ];
//   }

//   echo json_encode($output);
// }

public function get_berkas_detail($nopen) {
  $berkas = $this->ModelDigital->get_berkas_detail_by_nopen($nopen);
  echo json_encode($berkas);
}


// public function ajax_list() {
//   $start = $this->input->post('start');
//   $length = $this->input->post('length');
//   $search = $this->input->post('search')['value'];
//   $tanggal = $this->input->post('tanggalPelayanan');

//   $data = $this->ModelDigital->get_datatables($length, $start, $search, $tanggal);
//   $recordsTotal = $this->ModelDigital->count_all($tanggal);
//   $recordsFiltered = $this->ModelDigital->count_filtered($search, $tanggal);

//   $output = [
//       "draw" => intval($this->input->post("draw")),
//       "recordsTotal" => $recordsTotal,
//       "recordsFiltered" => $recordsFiltered,
//       "data" => []
//   ];

//   $no = $start + 1;
//   foreach ($data as $row) {
//       $output['data'][] = [
//           "no" => $no++,
//           "nomr" => $row->nomr,
//           "nama_pasien" => $row->nama_pasien,
//           "tanggal_pelayanan" => $row->tanggal_pelayanan,
//           "nopen" => $row->nopen
//       ];
//   }

//   echo json_encode($output);
// }

// public function ajax_detail($nopen) {
//   $data = $this->ModelDigital->get_detail_berkas_petugas($nopen);
//   echo json_encode($data);
// }


public function rj_pendaftaran() {
  $start = $this->input->post('start');
  $length = $this->input->post('length');
  $search = $this->input->post('search')['value'];
  $tanggal = $this->input->post('tanggalPelayanan');

  $data = $this->ModelDigital->get_datatablesRj1($length, $start, $search, $tanggal);
  $recordsTotal = $this->ModelDigital->count_all($tanggal);
  $recordsFiltered = $this->ModelDigital->count_filtered($search, $tanggal);

  $output = [
      "draw" => intval($this->input->post("draw")),
      "recordsTotal" => $recordsTotal,
      "recordsFiltered" => $recordsFiltered,
      "data" => []
  ];

  $no = $start + 1;
  foreach ($data as $row) {
      $output['data'][] = [
          "no" => $no++,
          "nomr" => $row->nomr,
          "nama_pasien" => $row->nama_pasien,
          "tanggal_pelayanan" => $row->tanggal_pelayanan,
          "nama_berkas" => $row->nama_berkas,
          "nama_pegawai" => $row->nama_pegawai,
          "nopen" => $row->nopen,
          "berkas" => '<button class="btn btn-sm btn-primary" onclick="showBerkasDetail(\''. $row->nopen .'\')" data-nopen="'. $row->nopen .'">Lihat Berkas</button>'
      ];
  }

  echo json_encode($output);
}
public function rj_jenisBerkas() {
  $tanggal = $this->input->post('tanggalPelayanan');
  $data = $this->ModelDigital->get_berkas($tanggal);
  
  $response = array();
  $no = 1;
  foreach ($data as $row) {
      $response[] = array(
          'no' => $no++,
          'nomr' => $row->nomr,
          'nama_pasien' => $row->nama_pasien,
          'tanggal_pelayanan' => date('d/m/Y', strtotime($row->tanggal_pelayanan)),
          'nama_berkas' => $row->namafile,
          'nama_pegawai' => $row->nama_pegawai,
          'lokasi' => $row->lokasi,
          'namafile' => $row->namafile
      );
  }
  
  echo json_encode(array('data' => $response));
}




//   public function getdataDigitalRi()
// {
//   $id_petugas = $this->session->userdata('id');
//     $button = '<div class="btn-group">
//                   <button type="button" class="btn btn-light dropdown-toggle waves-effect" data-toggle="dropdown" aria-expanded="false">
//                       <i class="mdi mdi-folder font-18 vertical-middle"></i>
//                       <i class="mdi mdi-chevron-down"></i>
//                   </button>
//                   <div class="dropdown-menu">
//                       <a class="dropdown-item" href="javascript: void(0);">Detail</a>
//                       <a class="dropdown-item" href="javascript: void(0);">Edit</a>
//                       <a class="dropdown-item" href="javascript: void(0);">Hapus</a>
//                   </div>
//               </div>';

//     if ($this->session->userdata('tugas') == '9') {
//       $listdata = $this->ModelDigital->getPetugasScan(); 
//     } else {
//         $listdata = $this->ModelDigital->dataListDigitalRi($id_petugas); 
//     }

//     $data = array();
//     $no = 1;

//     foreach ($listdata->result() as $field) {
//         $edit = '<button type="button" class="btn btn-primary btn-rounded btn-sm" title="edit data" onclick="editScanri(' . $field->ID . ')" style="width:72px"><i class="fa fa-edit"></i></button>';
//         $hapus = '<button type="button" class="btn btn-danger btn-rounded btn-sm hapus_scanri" href="javaScript:;" id="hapus_scanri" title="hapus_scanri" data="' . $field->ID . '" style="width:72px"><i class="fa fa-trash"></i></button>';
//         $edit2 = '<a href="#modalcek" class="btn btn-sm btn-primary" data-toggle="modal" data-id="' . $field->ID . '"><i class="fas fa-search"></i> Lihat</a>';
//         $lihat ='';
//         if ($this->session->userdata('tugas') == '9') {
//             $data[] = array(
//                 $no,
               
//                 $field->petugas,
//                 $field->persentase, 
//                 $field->keterangan,
//                 $lihat
//             );
//         } else {
//             $data[] = array(
//                 $no,
//                 $field->NOMR,
//                 $field->NAMA_PASIEN,
//                  date('d/m/Y H:i:s', strtotime($field->TANGGAL_KUNJUNGAN)),
//                 $edit . ' ' . $hapus
//             );
//         }

//         $no++;
//     }

//     $output = array(
//         "data" => $data
//     );

//     echo json_encode($output);
// }

public function getDataDigitalLL()
{
    $id_petugas = $this->session->userdata('id');  
    $tugas = $this->session->userdata('tugas');   
    $draw = intval($this->input->post("draw"));
    $start = intval($this->input->post("start"));
    $length = intval($this->input->post("length"));
    $search = $this->input->post("search")['value'];  

    $listdata = $this->ModelDigital->dataListDigitalLL($id_petugas, $tugas, $start, $length, $search);

    $data = array();
    $no = $start + 1;

    foreach ($listdata['data'] as $field) {
        $edit = '<button type="button" class="btn btn-primary btn-rounded btn-sm" title="Edit data" onclick="editScanll('.$field->ID.')" style="width:72px"><i class="fa fa-edit"></i></button>';
        $hapus = '<button type="button" class="btn btn-danger btn-rounded btn-sm hapus_scanll" href="javaScript:;" id="hapus_scanll" title="Hapus Scan LL" data="'.$field->ID.'" style="width:72px"><i class="fa fa-trash"></i></button>';

        $data[] = array(
            $no,
            $field->NOMR,
            $field->NAMA_PASIEN,
            date('d/m/Y H:i:s', strtotime($field->TANGGAL_KUNJUNGAN)),
            $field->TANGGAL_INPUT,
            $field->RUANGAN,
            $field->JENIS_FORM,
            $field->PETUGAS,
            $edit.' '.$hapus
        );
        $no++;
    }

    $output = array(
        "draw" => $draw,
        "recordsTotal" => $listdata['recordsTotal'],
        "recordsFiltered" => $listdata['recordsFiltered'],
        "data" => $data
    );

    echo json_encode($output);
}



  public function modalCeklisX()
  {
    $id       = $this->input->post('id');
    // $did = $this->input->post('did');
    $head =  $this->ModelAssembling->inputCeklis($id)->result_array();
    // $body = $this->ModelAssembling->dataCeklis($did)->result_array();
    $data     = array(

      'head' => $head,
      // 'body' => $body,
    );
    $this->load->view('Assembling_rj/modal_ceklis', $data);
  }

  public function getdataCeklis()
  {
    // $draw   = intval($this->input->POST("draw"));
    // $start  = intval($this->input->POST("start"));
    // $length = intval($this->input->POST("length"));

    // $id = '6';
    
       // echo "<pre>";print_r($id);exit();

    $id       = $this->input->post('ID');

    $listdata = $this->ModelAssembling->dataCeklis($id);
    
    $data=array();
    $no =1;
    foreach ($listdata->result() as $field) {

      $ceklis=$field->CEKLIS;

      if($ceklis == '2'){
        $cek = '<input type="checkbox" data-id="'.$field->ID_DET.'"  class="cek_done" checked>';
      } else {
        $cek = '<input type="checkbox" data-id="'.$field->ID_DET.'"  class="cek_done">';
      }
    
      $edit = '<button type="button" class="btn btn-primary btn-rounded btn-sm" title="edit data" onclick="ceklis('.$field->ID.')" style="width:72px"><i class="fa fa-edit"></i> </button>';

      $data[] = array(
        $no,
        $field->NAMA_FORM,
        $cek
        );
        
      $no++;

    }

    $output = array(
      // "draw"            => $draw,
      // "recordsTotal"    => $listdata->num_rows(),
      // "recordsFiltered" => $listdata->num_rows(),
      "data"            => $data
    );
    echo json_encode($output);
  }

public function simpanCeklis()
    {
    $this->db->trans_begin();
    $post = $this->input->post();
    $id =$post['id'];
    $value =$post['value'];

    if($value==1){
      $dataUpdate = array (
          'CEKLIS'                           => 2,
        );
    } else if($value==2){
      $dataUpdate = array (
          'CEKLIS'                           => 1,
        );
    }
      
    // echo "<pre>";print_r($id);exit();
        $this->db->where('db_rekammedis.tb_assembling_detail.ID', $id);
        $this->db->update('db_rekammedis.tb_assembling_detail', $dataUpdate);

    if ($this->db->trans_status() === false) {
      $this->db->trans_rollback();
      $result = array('status' => 'failed');
    } else {
      $this->db->trans_commit();
      $result = array('status' => 'success');
    }

    echo json_encode($result);
    }

    public function getTanggalScanEMr()
    {
        $result = $this->ModelDigital->listTanggalScanEMr();
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row['ID'];
            $sub_array['text'] = $row['text'];
            $data[] = $sub_array;
        }
        echo json_encode($data);
    }
    public function jenisForm()
    {
        $result = $this->ModelDigital->listJenisForm();
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row['ID'];
            $sub_array['text'] = $row['DESKRIPSI'];
            $data[] = $sub_array;
        }
        echo json_encode($data);
    }

  //   public function generate_autologin_url() {
  //     $username = $this->input->post('username');
  //     $password = $this->input->post('password');
  //     $nomr = $this->input->post('nomr');
  //     $nopen = $this->input->post('nopen');
  //     $nmf = $this->input->post('nmf');
      
  //     $keyUsername = '2129264869259147';
  //     $keyPass = '9359886494143302';
  //     $key1 = '6992825730419199';
      
  //     $enc_username = $this->encryptAES($username, $keyUsername);
  //     $enc_password = $this->encryptAES($password, $keyPass);
  //     $enc_fp = $this->encryptAES('0', $key1); // fingerprint atau tipe akses
  
  //     $url = "http://************/simrskd/rekam_medis/uploadRM/showfile/{$nomr}/{$nopen}/{$nmf}" .
  //            "?username={$enc_username}&password={$enc_password}&fp={$enc_fp}";
      
  //     echo json_encode(['url' => $url]);
  // }
  // private function encryptAES($str, $key) {
  //   $cipher = "AES-128-ECB";
  //   $encrypted = openssl_encrypt($str, $cipher, $key, OPENSSL_RAW_DATA);
  //   return rawurlencode(base64_encode($encrypted)); 
  //   // Gunakan base64 agar sesuai jika SIMRS butuh format ini
  // }

  // RI
  public function getTanggalBerkas() {
    $jenisForm = $this->input->get('jenisForm');
    $search = $this->input->get('search');
  
    $data = $this->ModelDigital->getTanggalBerkas2($jenisForm, $search);
    echo json_encode($data);
  }
  public function ri_pendaftaran() {
    $tanggalPelayanan = $this->input->post('tanggalPelayanan');
    $data = $this->ModelDigital->getScanBerkasData($tanggalPelayanan);
    $response = array();
    $no = 1;
    foreach ($data as $row) {
        $response[] = array(
            'no' => $no++,
            'nomr' => $row->nomr,
            'nama_pasien' => $row->nama_pasien,
            'tanggal_pelayanan' => date('d/m/Y', strtotime($row->tanggal_pelayanan)),
            'nama_berkas' => $row->nama_berkas,
            'nama_pegawai' => $row->nama_pegawai,
            "berkas" => '<button class="btn btn-sm btn-primary" onclick="showBerkasDetail(\''. $row->nopen .'\')" data-nopen="'. $row->nopen .'">Lihat Berkas</button>'
        );
    }
    
    echo json_encode(array('data' => $response));
  }
  public function get_berkas_detailRi($nopen) {
    $berkas = $this->ModelDigital->get_berkas_detail_by_nopenRi($nopen);
    echo json_encode($berkas);
  }
  public function ri_jenisBerkas() {
    $tanggal = $this->input->post('tanggalPelayanan');
    $data = $this->ModelDigital->get_berkas2($tanggal);
    
    $response = array();
    $no = 1;
    foreach ($data as $row) {
        $response[] = array(
            'no' => $no++,
            'nomr' => $row->nomr,
            'nama_pasien' => $row->nama_pasien,
            'tanggal_pelayanan' => date('d/m/Y', strtotime($row->tanggal_pelayanan)),
            'nama_berkas' => $row->namafile,
            'nama_pegawai' => $row->nama_pegawai,
            'lokasi' => $row->lokasi,
            'namafile' => $row->namafile
        );
    }
    
    echo json_encode(array('data' => $response));
  }

}
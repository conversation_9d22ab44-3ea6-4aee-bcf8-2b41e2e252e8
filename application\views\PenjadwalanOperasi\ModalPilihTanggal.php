<div class="calendar-container">
    <!-- Calendar Navigation -->
    <div class="calendar-header d-flex justify-content-between align-items-center mb-4">
        <button type="button" class="btn btn-outline-primary" id="prevMonth">
            <i class="fa fa-chevron-left"></i>
        </button>
        <h4 id="currentMonth" class="mb-0"></h4>
        <button type="button" class="btn btn-outline-primary" id="nextMonth">
            <i class="fa fa-chevron-right"></i>
        </button>
    </div>

    <!-- Calendar Days -->
    <div id="calendarDays" class="calendar-days">
        <!-- Days will be loaded here -->
    </div>

    <!-- Close Button -->
    <div class="text-right mt-3">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">
            <i class="fa fa-times"></i> Tutup
        </button>
    </div>
</div>

<style>
.calendar-container {
    padding: 20px;
}

.calendar-days {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    margin-bottom: 20px;
}

.day-card {
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    min-height: 400px;
}

.day-header {
    background: #007bff;
    color: white;
    padding: 10px;
    border-radius: 6px;
    margin-bottom: 15px;
    font-weight: 600;
}

.day-date {
    font-size: 1.2rem;
    margin-bottom: 5px;
}

.day-name {
    font-size: 0.9rem;
    opacity: 0.9;
}

.slots-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    margin-top: 15px;
}

.slot-box {
    border: 2px dashed #ccc;
    border-radius: 8px; /* Rounded corners */
    padding: 0; /* Remove padding to use flexbox alignment */
    min-height: 140px; /* Adjust height */
    display: flex;
    flex-direction: column;
    justify-content: center; /* Center empty slot content */
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden; /* Hide overflowing content */
}

.slot-box.empty {
    background: #f8f9fa;
    border-color: #28a745;
}

.slot-box.empty:hover {
    background: #e8f5e9;
    border-color: #1e7e34;
}

.slot-box.scheduled {
    background-color: #e9f5e9; /* Latar belakang hijau muda untuk jadwal final */
    border: 1px solid #28a745; /* Border hijau */
    color: #212529; /* Teks gelap agar kontras */
    justify-content: flex-start;
    align-items: stretch;
    cursor: not-allowed;
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.4); /* Shadow hijau */
}

.slot-box.scheduled:hover {
    background-color: #d8f0d8; /* Sedikit lebih gelap saat hover */
}

.slot-box.appointment {
    background-color: #fff3cd; /* Latar belakang kuning muda untuk perjanjian */
    border: 2px solid #ffc107; /* Border kuning */
    color: #212529; /* Teks gelap agar kontras */
    justify-content: flex-start;
    align-items: stretch;
    cursor: pointer; /* Ubah menjadi pointer karena bisa diklik */
    box-shadow: 0 2px 4px rgba(255, 193, 7, 0.4); /* Shadow kuning */
}

.slot-box.appointment:hover {
    background-color: #ffeaa7; /* Sedikit lebih gelap saat hover */
    border-color: #e0a800;
}

/* Legacy support for old 'occupied' class */
.slot-box.occupied {
    background-color: #e9f5e9;
    border: 1px solid #28a745;
    color: #212529;
    justify-content: flex-start;
    align-items: stretch;
    cursor: not-allowed;
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.4);
}

.slot-box.occupied:hover {
    background-color: #d8f0d8;
}

.slot-number {
    position: absolute;
    top: 5px;
    left: 8px;
    font-size: 0.8rem;
    font-weight: 600;
    color: #666;
}

.slot-content {
    text-align: left;
    font-size: 0.75rem;
    line-height: 1.4;
    padding: 8px;
    width: 100%;
}

.slot-time-header {
    background-color: #fff; /* Latar belakang putih */
    color: #212529; /* Teks gelap */
    padding: 5px 8px;
    font-weight: 600;
    text-align: center;
    border-bottom: 1px solid #dee2e6; /* Border abu-abu */
    font-size: 0.8rem;
    border-radius: 6px 6px 0 0;
}

.slot-content-detail {
    margin-bottom: 4px;
    font-size: 0.6rem;
}

.slot-content-detail strong {
    color: #495057; /* Warna label lebih gelap */
}

.slot-content .patient-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 3px;
}

.slot-content .doctor-name {
    color: #007bff;
    margin-bottom: 3px;
}

.slot-content .tindakan {
    color: #212529; /* Teks gelap */
    font-size: 0.75rem;
    margin-bottom: 0;
    background-color: transparent;
    padding: 0;
    border-radius: 0;
    font-weight: normal;
    display: block;
}

.slot-content .time {
    color: #28a745;
    font-weight: 600;
    font-size: 0.8rem;
}

.slot-add-icon {
    font-size: 2rem;
    color: #28a745;
    margin-bottom: 5px;
}

.slot-label {
    font-size: 0.9rem;
    color: #666;
    font-weight: 500;
}

.kamar-section {
    margin-bottom: 20px;
}

.kamar-title {
    background: #f8f9fa;
    padding: 10px 15px;
    border-radius: 6px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 15px;
    border-left: 4px solid #007bff;
    border-right: 4px solid #007bff;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.btn-add-slot {
    background: none;
    border: none;
    color: #007bff;
    font-size: 16px;
    cursor: pointer;
    padding: 2px 6px;
    border-radius: 3px;
    transition: all 0.2s;
}

.btn-add-slot:hover {
    background-color: #007bff;
    color: white;
    transform: scale(1.1);
}

/* Custom tooltip style for larger font */
.tooltip-custom .tooltip-inner {
    font-size: 0.9rem; /* Adjust font size as needed */
    max-width: 350px; /* Adjust max-width for better layout */
    padding: 10px;
}

@media (max-width: 768px) {
    .calendar-days {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .slots-container {
        grid-template-columns: 1fr;
        gap: 8px;
    }
    
    .slot-box {
        min-height: 100px;
        padding: 8px;
    }
    
    .slot-content {
        font-size: 0.8rem;
    }
}
</style>

<script>
$(document).ready(function() {
    var today = new Date();
    today.setHours(0, 0, 0, 0); // Set to start of day for comparison
    var currentStartDate = new Date(today); // Start from today
    
    // Indonesian month names
    var monthNames = [
        'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
        'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
    ];
    
    // Indonesian day names
    var dayNames = ['Minggu', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu'];
    
    // Function to get next working day (Monday-Friday)
    function getNextWorkingDay(date) {
        var nextDay = new Date(date);
        while (nextDay.getDay() === 0 || nextDay.getDay() === 6) { // Skip Sunday (0) and Saturday (6)
            nextDay.setDate(nextDay.getDate() + 1);
        }
        return nextDay;
    }
    
    // Function to generate 7 days starting from current date
    function getSevenDaysFromToday(startDate) {
        var weekDays = [];
        var current = new Date(startDate);

        // Add 7 days starting from startDate
        for (var i = 0; i < 7; i++) {
            weekDays.push(new Date(current));
            current.setDate(current.getDate() + 1);
        }

        return weekDays;
    }
    
    // Function to generate week title based on first and last date
    function generateWeekTitle(workingDays) {
        if (workingDays.length === 0) return '';
        
        var firstDate = workingDays[0];
        var lastDate = workingDays[workingDays.length - 1];
        
        var firstMonth = firstDate.getMonth();
        var lastMonth = lastDate.getMonth();
        var year = firstDate.getFullYear();
        
        if (firstMonth === lastMonth) {
            return monthNames[firstMonth] + ' ' + year;
        } else {
            return monthNames[firstMonth] + ' - ' + monthNames[lastMonth] + ' ' + year;
        }
    }
    
    function loadCalendar() {
        var weekDays = getSevenDaysFromToday(currentStartDate);
        var startDate = weekDays[0];
        var endDate = weekDays[weekDays.length - 1];

        // Set week title
        var weekTitle = generateWeekTitle(weekDays);
        $('#currentMonth').text(weekTitle);

        loadCalendarData(startDate, endDate);
    }
    
    function loadCalendarData(startDate, endDate) {
        $.ajax({
            url: '<?php echo base_url('PenjadwalanOperasi/get_week_calendar'); ?>',
            type: 'POST',
            data: {
                start_date: startDate.toISOString().split('T')[0],
                end_date: endDate.toISOString().split('T')[0]
            },
            dataType: 'json',
            success: function(response) {
                if (response.error) {
                    Swal.fire('Error', response.error, 'error');
                } else {
                    renderNewCalendar(response);
                }
            },
            error: function() {
                Swal.fire('Error', 'Gagal memuat data kalender', 'error');
            }
        });
    }
    
    function renderCalendar(startDate, endDate, data) {
        var weekDays = getSevenDaysFromToday(currentStartDate);
        var calendarHtml = '';

        weekDays.forEach(function(date) {
            var dateStr = date.toISOString().split('T')[0];
            var dayData = data[dateStr] || {};
            calendarHtml += renderDayCard(date, dayData);
        });

        $('#calendarDays').html(calendarHtml);
    }

    // New render function for optimized data structure
    function renderNewCalendar(response) {
        var calendarHtml = '';

        response.days.forEach(function(dayData) {
            calendarHtml += renderNewDayCard(dayData, response.kamar);
        });

        $('#calendarDays').html(calendarHtml);
    }

    // New day card renderer
    function renderNewDayCard(dayData, kamarList) {
        var date = new Date(dayData.date);
        var dayName = dayNames[date.getDay()];
        var dayDate = date.getDate();
        
        var html = '<div class="day-card">';
        html += '<div class="day-header">';
        html += '<div class="day-name">' + dayName + '</div>';
        html += '<div class="day-date">' + dayDate + '</div>';
        html += '</div>';
        
        // Group slots by kamar with priority for scheduled over appointment
        var slotsByKamar = {};
        dayData.slots.forEach(function(slot) {
            if (!slotsByKamar[slot.kamar_id]) {
                slotsByKamar[slot.kamar_id] = {};
            }

            var slotKey = slot.slot;
            var existingSlot = slotsByKamar[slot.kamar_id][slotKey];

            // Prioritize scheduled over appointment (penjadwalan over perjanjian)
            if (!existingSlot ||
                (slot.jenis === 'scheduled' && existingSlot.jenis === 'appointment')) {
                slotsByKamar[slot.kamar_id][slotKey] = slot;
            }
        });

        // Render kamar sections
        kamarList.forEach(function(kamar) {
            html += '<div class="kamar-section">';
            html += '<div class="kamar-title">';
            html += kamar.nama;
            html += '<button type="button" class="btn-add-slot" data-kamar-id="' + kamar.id + '" data-kamar-nama="' + kamar.nama + '" data-date="' + dayData.date + '" title="Tambah Slot">';
            html += '<i class="fa fa-plus-circle"></i>';
            html += '</button>';
            html += '</div>';
            html += '<div class="slots-container">';

            // Get existing slots for this kamar
            var existingSlots = slotsByKamar[kamar.id] || {};
            var maxSlot = 0;

            // Find maximum slot number
            Object.keys(existingSlots).forEach(function(slotNum) {
                maxSlot = Math.max(maxSlot, parseInt(slotNum));
            });

            // Ensure at least 2 slots (1 empty slot after existing ones)
            var totalSlots = Math.max(maxSlot + 1, 2);

            // Render slots in 2-column layout
            for (var i = 1; i <= totalSlots; i++) {
                var slot = existingSlots[i] || null;
                html += renderNewSlot(dayData.date, i, slot, kamar.nama, kamar.id);
            }

            html += '</div></div>';
        });
        
        html += '</div>';
        return html;
    }

    // New slot renderer
    function renderNewSlot(date, slotNumber, slotData, kamarNama, kamarId) {
        var isOccupied = slotData !== null;
        var slotType = slotData ? slotData.jenis : 'empty';

        var html = '<div class="slot-box ' + slotType + '" ';
        html += 'data-date="' + date + '" data-slot="' + slotNumber + '" data-kamar-id="' + kamarId + '" data-kamar-nama="' + kamarNama + '"';

        if (isOccupied) {
            // Prepare content for Bootstrap Tooltip
            var tooltipContent = `
                <div class='text-left'>
                    <strong>Pasien:</strong> ${slotData.pasien_info || '-'}<br>
                    <strong>Dokter:</strong> ${slotData.nama_dokter || '-'}<br>
                    <strong>Tindakan:</strong> ${slotData.rencana_tindakan_operasi || '-'}
                </div>
            `;
            html += ' data-toggle="tooltip" data-html="true" title="' + tooltipContent.replace(/"/g, '"') + '"';
        }

        html += '>';

        if (isOccupied) {
            // Layout untuk slot yang terisi (baik scheduled maupun appointment)
            html += '<div class="slot-time-header">' + (slotData.waktu_selesai || slotData.waktu_operasi || 'Waktu') + '</div>';
            html += '<div class="slot-content">';
            html += '<div class="slot-content-detail"><strong>Pasien:</strong><br>' + (slotData.pasien_info || '-') + '</div>';
            html += '<div class="slot-content-detail"><strong>Dokter Bedah:</strong><br>' + (slotData.nama_dokter || '-') + '</div>';
            html += '<div class="slot-content-detail"><strong>Rencana Tindakan:</strong><br><span class="tindakan">' + (slotData.rencana_tindakan_operasi || '-') + '</span></div>';
            if (slotType === 'appointment') {
                html += '<div class="slot-content-detail"><span class="badge badge-warning">Menunggu Konfirmasi</span></div>';
            } else if (slotType === 'scheduled') {
                html += '<div class="slot-content-detail"><span class="badge  badge-danger">Teronfirmasi</span></div>';
            }
            html += '</div>';
        } else {
            // Layout for empty slot
            html += '<div class="slot-number">Slot ' + slotNumber + '</div>';
            html += '<div class="slot-add-icon"><i class="fa fa-plus-circle"></i></div>';
            html += '<div class="slot-label">Pilih Slot</div>';
        }

        html += '</div>';
        return html;
    }
    
    function renderDayCard(date, dayData) {
        var dayName = dayNames[date.getDay()];
        var dayDate = date.getDate();
        var dateStr = date.toISOString().split('T')[0];
        
        var html = '<div class="day-card">';
        html += '<div class="day-header">';
        html += '<div class="day-name">' + dayName + '</div>';
        html += '<div class="day-date">' + dayDate + '</div>';
        html += '</div>';
        
        // Render slots dinamis berdasarkan kamar yang tersedia dari server
        var kamarList = dayData.kamar_list || [];

        // Jika tidak ada jadwal sama sekali pada hari itu, pastikan tetap menampilkan kamar kosong
        if (Object.keys(dayData).length === 0) {
             $.ajax({
                url: '<?php echo base_url('PenjadwalanOperasi/get_kamar_for_calendar'); ?>',
                type: 'GET',
                dataType: 'json',
                async: false, // Dibuat sinkron agar kamarList terisi sebelum lanjut
                success: function(response) {
                    kamarList = response;
                }
            });
        }
        
        kamarList.forEach(function(kamar, kamarIndex) {
            html += '<div class="kamar-section">';
            html += '<div class="kamar-title">' + kamar.nama + '</div>';
            html += '<div class="slots-container">';

            // Setiap kamar memiliki 4 slot
            for (var i = 1; i <= 4; i++) {
                var slotKey = 'slot_' + kamar.id + '_' + i;
                var slotData = dayData[slotKey] || null;
                html += renderSlot(dateStr, i, slotData, kamar.nama, kamar.id);
            }
            html += '</div></div>';
        });
        
        html += '</div>';
        return html;
    }
    
    function renderSlot(date, slotNumber, slotData, kamarNama, kamarId) {
        var isOccupied = !!slotData;
        var html = '<div class="slot-box ' + (isOccupied ? 'occupied' : 'empty') + '" ';
        html += 'data-date="' + date + '" data-slot="' + slotNumber + '" data-kamar-id="' + kamarId + '"';
        
        if (isOccupied) {
            // Prepare content for Bootstrap Tooltip
            var tooltipContent = `
                <div class='text-left'>
                    
                    <strong>Pasien:</strong> ${slotData.pasien_info || '-'}<br>
                    <strong>Dokter:</strong> ${slotData.nama_dokter || '-'}<br>
                    <strong>Tindakan:</strong> ${slotData.rencana_tindakan_operasi || '-'}
                </div>
            `;
            html += ' data-toggle="tooltip" data-html="true" title="' + tooltipContent.replace(/"/g, '"') + '"';
        }
        
        html += '>';

        if (isOccupied) {
            // Layout for occupied slot
            html += '<div class="slot-time-header">' + (slotData.waktu_selesai || 'Waktu') + '</div>';
            html += '<div class="slot-content">';
            html += '<div class="slot-content-detail"><strong>Pasien:</strong><br>' + (slotData.pasien_info || '-') + '</div>';
            html += '<div class="slot-content-detail"><strong>Dokter Bedah:</strong><br>' + (slotData.nama_dokter || '-') + '</div>';
            html += '<div class="slot-content-detail"><strong>Rencana Tindakan:</strong><br><span class="tindakan">' + (slotData.rencana_tindakan_operasi || '-') + '</span></div>';
            html += '</div>';
        } else {
            // Layout for empty slot
            html += '<div class="slot-number">Slot ' + slotNumber + '</div>';
            html += '<div class="slot-add-icon"><i class="fa fa-plus-circle"></i></div>';
            html += '<div class="slot-label">Pilih Slot</div>';
        }

        html += '</div>';
        return html;
    }
    
    // Event handlers
    $('#prevMonth').click(function() {
        // Check if we can go back (currentStartDate should not be before today)
        if (currentStartDate.toDateString() === today.toDateString()) {
            // Already at today, cannot go back
            $(this).addClass('disabled').css({
                'opacity': '0.5',
                'cursor': 'not-allowed',
                'pointer-events': 'none'
            });
            toastr.warning('Tidak dapat kembali ke tanggal sebelum hari ini', 'Peringatan');
            return;
        }

        // Move back 7 days
        var newStartDate = new Date(currentStartDate);
        newStartDate.setDate(newStartDate.getDate() - 7);

        // Don't allow going before today
        if (newStartDate < today) {
            newStartDate = new Date(today);
        }

        currentStartDate = newStartDate;
        loadCalendar();

        // Update button state
        updateNavigationButtons();
    });

    $('#nextMonth').click(function() {
        // Move forward 7 days
        var newStartDate = new Date(currentStartDate);
        newStartDate.setDate(newStartDate.getDate() + 7);

        currentStartDate = newStartDate;
        loadCalendar();

        // Update button state
        updateNavigationButtons();
    });

    // Function to update navigation button states
    function updateNavigationButtons() {
        var prevBtn = $('#prevMonth');

        if (currentStartDate.toDateString() === today.toDateString()) {
            // Disable prev button if at today
            prevBtn.addClass('disabled').css({
                'opacity': '0.5',
                'cursor': 'not-allowed',
                'pointer-events': 'none'
            });
        } else {
            // Enable prev button
            prevBtn.removeClass('disabled').css({
                'opacity': '1',
                'cursor': 'pointer',
                'pointer-events': 'auto'
            });
        }
    }
    
    // Slot click handler untuk slot kosong
    $(document).on('click', '.slot-box.empty', function() {
        var date = $(this).data('date');
        var slot = $(this).data('slot');
        var kamarId = $(this).data('kamar-id');
        var kamarNama = $(this).data('kamar-nama');

        // Calculate default operation time
        var defaultTime = calculateDefaultOperationTime(date, kamarId, slot);

        // Set selected date and slot in the form
        $('#tanggalOperasiPenjadwalan').val(date);
        $('#slotOperasiPenjadwalan').val(slot);
        $('#kamarOperasiHidden').val(kamarId);
        $('#kamarOperasiPenjadwalan').val(kamarId).trigger('change');
        $('#waktuOperasiPenjadwalan').val(defaultTime);

        Swal.fire({
            title: 'Slot Dipilih',
            text: 'Tanggal: ' + date + ', ' + kamarNama + ', Slot ' + slot,
            type: 'success',
            timer: 1500,
            showConfirmButton: false
        }).then(() => {
            $('#modalPilihTanggalOperasi').modal('hide');
        });
    });

    // Slot click handler untuk slot appointment (perjanjian) - bisa dipilih untuk dijadwalkan
    $(document).on('click', '.slot-box.appointment', function() {
        var date = $(this).data('date');
        var slot = $(this).data('slot');
        var kamarId = $(this).data('kamar-id');
        var kamarNama = $(this).data('kamar-nama');

        // Get existing time from slot and extract only the start time
        var timeHeaderText = $(this).find('.slot-time-header').text().trim();
        var startTime = timeHeaderText.split(' - ')[0]; // Ambil bagian pertama (waktu mulai)

        if (startTime === 'Waktu' || !startTime) {
            startTime = calculateDefaultOperationTime(date, kamarId, slot);
        }

        Swal.fire({
            title: 'Pilih Slot Perjanjian?',
            text: 'Slot ini sudah ada perjanjian. Apakah Anda ingin menggunakan slot ini untuk penjadwalan?',
            type: 'question',
            showCancelButton: true,
            confirmButtonText: 'Ya, Pilih',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.value) {
                // Set selected date and slot in the form
                $('#tanggalOperasiPenjadwalan').val(date);
                $('#slotOperasiPenjadwalan').val(slot);
                $('#kamarOperasiHidden').val(kamarId);
                $('#kamarOperasiPenjadwalan').val(kamarId).trigger('change');
                $('#waktuOperasiPenjadwalan').val(startTime); // Gunakan startTime

                Swal.fire({
                    title: 'Slot Dipilih',
                    text: 'Tanggal: ' + date + ', ' + kamarNama + ', Slot ' + slot,
                    type: 'success',
                    timer: 1500,
                    showConfirmButton: false
                }).then(() => {
                    $('#modalPilihTanggalOperasi').modal('hide');
                });
            }
        });
    });

    // Button tambah slot handler
    $(document).on('click', '.btn-add-slot', function() {
        var kamarId = $(this).data('kamar-id');
        var kamarNama = $(this).data('kamar-nama');
        var date = $(this).data('date');

        // Find the highest slot number for this kamar and date
        var maxSlot = 0;
        $('.slot-box[data-kamar-id="' + kamarId + '"][data-date="' + date + '"]').each(function() {
            var slotNum = parseInt($(this).data('slot'));
            maxSlot = Math.max(maxSlot, slotNum);
        });

        var newSlotNumber = maxSlot + 1;

        // Calculate default operation time for new slot
        var defaultTime = calculateDefaultOperationTime(date, kamarId, newSlotNumber);

        // Set selected date and slot in the form
        $('#tanggalOperasiPenjadwalan').val(date);
        $('#slotOperasiPenjadwalan').val(newSlotNumber);
        $('#kamarOperasiHidden').val(kamarId);
        $('#kamarOperasiPenjadwalan').val(kamarId).trigger('change');
        $('#waktuOperasiPenjadwalan').val(defaultTime);

        Swal.fire({
            title: 'Slot Baru Ditambahkan',
            text: 'Tanggal: ' + date + ', ' + kamarNama + ', Slot ' + newSlotNumber,
            type: 'success',
            timer: 1500,
            showConfirmButton: false
        }).then(() => {
            $('#modalPilihTanggalOperasi').modal('hide');
        });
    });

    // Click handler for occupied slots (legacy support)
    $(document).on('click', '.slot-box.occupied', function() {
        var time = $(this).find('.slot-time-header').text();
        toastr.error('Slot sudah terisi dengan jadwal operasi jam ' + time, 'Slot Terisi', {
            closeButton: true,
            progressBar: true,
            timeOut: 3000
        });
    });

    // Click handler for scheduled slots (final schedule) - prevent multiple toastr
    var scheduledClickTimeout = null;
    $(document).on('click', '.slot-box.scheduled', function(e) {
        e.preventDefault();
        e.stopPropagation();

        // Clear previous timeout
        if (scheduledClickTimeout) {
            clearTimeout(scheduledClickTimeout);
        }

        // Set new timeout to prevent multiple toastr
        scheduledClickTimeout = setTimeout(function() {
            var time = $('.slot-box.scheduled:first').find('.slot-time-header').text();

            // Clear existing toastr
            toastr.clear();

            toastr.error('Slot sudah terisi dengan jadwal operasi jam ' + time, 'Slot Terisi', {
                closeButton: true,
                progressBar: true,
                timeOut: 3000,
                preventDuplicates: true
            });

            scheduledClickTimeout = null;
        }, 100);
    });

    // Click handler for appointment slots (perjanjian only)
    // $(document).on('click', '.slot-box.appointment', function() {
    //     var time = $(this).find('.slot-time-header').text();
    //     toastr.warning('Slot sudah diperjanjikan jam ' + time + '. Silakan pilih slot lain atau hubungi admin untuk konfirmasi.', 'Slot Terperjanjikan', {
    //         closeButton: true,
    //         progressBar: true,
    //         timeOut: 4000
    //     });
    // });

    // Initialize Bootstrap Tooltips after calendar is rendered
    function initializeTooltips() {
        $('[data-toggle="tooltip"]').tooltip({
            container: 'body', // To prevent tooltip from being trapped in the modal
            trigger: 'hover', // Show on hover
            template: '<div class="tooltip tooltip-custom" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>' // Apply custom class
        });
    }

    // Function to calculate default operation time
    function calculateDefaultOperationTime(date, kamarId, currentSlot) {
        var defaultStartTime = '08:30';
        var preparationTime = 60; // 60 minutes for preparation

        var scheduledSlots = [];
        var appointmentSlots = [];

        // Collect all scheduled and appointment slots
        $('.slot-box[data-kamar-id="' + kamarId + '"][data-date="' + date + '"]').each(function() {
            var timeText = $(this).find('.slot-time-header').text().trim();
            var timeParts = timeText.split(' - ');
            
            if (timeParts.length === 2) {
                var slotInfo = {
                    startTime: timeParts[0],
                    endTime: timeParts[1]
                };
                if ($(this).hasClass('scheduled')) {
                    scheduledSlots.push(slotInfo);
                } else if ($(this).hasClass('appointment')) {
                    appointmentSlots.push(slotInfo);
                }
            }
        });

        var lastEndTime = null;

        // Prioritize scheduled slots
        if (scheduledSlots.length > 0) {
            // Sort by end time to find the latest one
            scheduledSlots.sort(function(a, b) {
                return b.endTime.localeCompare(a.endTime);
            });
            lastEndTime = scheduledSlots[0].endTime;
        } 
        // If no scheduled slots, check appointment slots
        else if (appointmentSlots.length > 0) {
            // Sort by end time to find the latest one
            appointmentSlots.sort(function(a, b) {
                return b.endTime.localeCompare(a.endTime);
            });
            lastEndTime = appointmentSlots[0].endTime;
        }

        // If a last end time was found, add preparation time
        if (lastEndTime) {
            var timeParts = lastEndTime.split(':');
            if (timeParts.length === 2) {
                var hours = parseInt(timeParts[0], 10);
                var minutes = parseInt(timeParts[1], 10);

                // Add preparation time
                minutes += preparationTime;
                if (minutes >= 60) {
                    hours += Math.floor(minutes / 60);
                    minutes = minutes % 60;
                }

                var recommendedTime = String(hours).padStart(2, '0') + ':' + String(minutes).padStart(2, '0');

                // Check if the calculated time is within operational hours
                if (hours < 23) {
                    return recommendedTime;
                }
            }
        }

        // Fallback to default if no slots are occupied or if time is out of bounds
        return defaultStartTime;
    }

    // Call this function after rendering the calendar
    $(document).ajaxComplete(function() {
        initializeTooltips();
    });

    // Initialize calendar
    loadCalendar();

    // Initialize navigation button states
    updateNavigationButtons();
});
</script>

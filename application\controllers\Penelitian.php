<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class <PERSON>elitian extends CI_Controller {
    
    public function __construct()
    {
    parent::__construct();
    if($this->session->userdata('logged_in') == FALSE ){
        redirect('login');  
    }
    date_default_timezone_set("Asia/Bangkok");
    $this->load->model(array('ModelPeneliti'));
    }
    public function index(){
      $data=array(      
        'isi'            => 'Penelitian/IndexPeneliti'
      );
      $this->load->view('Layout/Wrapper',$data);
    }
    public function tabelData(){
      $getPeneliti = $this->ModelPeneliti->getTblPeneliti();
      $data = [
          'getPeneliti' => $getPeneliti,
      ];
      $table_html = $this->load->view('Penelitian/IndexPenelitiTabel', $data, true);
      $msg = [
          'data' => $table_html
      ];
      echo json_encode($msg);
    }
    public function modalTambahPeneliti(){
      $add = $this->load->view('Penelitian/IndexPenelitiAdd','',true);
      $msg = [
          'data' => $add
      ];
      echo json_encode($msg);      
    }
    public function username_check(){
      $post = $this->input->post(null, true);
      $id = isset($post['nik']) ? $post['nik'] : ''; 
      $query = $this->db->query("SELECT * FROM db_rekammedis.tb_user_penelitian WHERE user_name = ? AND nik != ?", array($post['username'], $id));
      if ($query->num_rows() > 0) {
          $this->form_validation->set_message('username_check', 'maaf %s ini sudah terdaftar, silakan input username lain. ');
          return FALSE;
      } else {
          return TRUE;
      }
    }
    
    public function simpanData(){
      $this->db->db_select('db_rekammedis');
      $this->form_validation->set_rules(
        'username',
        'Username',
        'required|callback_username_check|max_length[30]');
      $this->form_validation->set_rules('pass', 'Password', 'required|max_length[30]');
      $this->form_validation->set_rules(
          'nik',
          'Nomer Identitas',
          'required|callback_valid_nik|is_unique[tb_peneliti.nik]',
          array('is_unique' => '%s yang dimasukan sudah ada, silahkan ganti datanya')
      );
      $this->form_validation->set_rules(
          'nama',
          'Nama Peneliti',
          'required'
      );
      $this->form_validation->set_rules(
          'notelp',
          'Nomer Telpon',
          'required|max_length[20]'
      );
      $this->form_validation->set_rules(
          'email',
          'Email',
          'required|max_length[40]|valid_email',
          array('valid_email' => 'Masukan %s yang valid')
      );
      $this->form_validation->set_rules(
        'alamat',
        'Alamat Peneliti',
        'required'
      );
      $this->form_validation->set_rules(
        'jk',
        'Jenis Kelamin Peneliti',
        'required'
      );


      // $this->form_validation->set_message('exact_length', '%s yang diinputkan tidak sesuai, silahkan input kembali');
      $this->form_validation->set_message('numeric', '%s yang diinputkan tidak sesuai format, silahkan input kembali dalam bentuk angka');
      $this->form_validation->set_message('required', '%s masih kosong, silakan di isi datanya ya');
      $this->form_validation->set_message('max_length', '%s yang diinputkan terlalu banyak, silahkan input kembali');
      
      if ($this->form_validation->run() == FALSE) {
          $errors = $this->form_validation->error_array();
          echo json_encode(['error' => $errors]);
      } else { 
          $this->db->trans_start();
          $post = $this->input->post(); 
          $stat = 1;
          $role = 2;
          $dataAkun = array(
              'user_name' => $post['username'],
              'pass' => $post['pass'],
              'jk' => $post['jk'],
              'nik' => $post['nik'],
              'level' => $role,
              'status' => $stat,
          );
          $this->db->insert('db_rekammedis.tb_user_penelitian', $dataAkun);
          $dataPeneliti = array(
              'nik' => $post['nik'],
              'nama' => $post['nama'],
              'email' => $post['email'],
              'notelp' => $post['notelp'],
              'alamat' => $post['alamat'],
              'keterangan' => $post['ket'],
              'status' => $stat,
          );
          $this->db->insert('db_rekammedis.tb_peneliti', $dataPeneliti);
          if ($this->db->trans_status() === false) {
              $this->db->trans_rollback();
              $result = array('status' => 'failed');
          } else {
              $this->db->trans_commit();
              $result = array('status' => 'success');
          }
          echo json_encode($result);
      }      
   }
   public function valid_nik($nik) {
    $negara = $this->input->post('negara');

    if (!$negara) {
        $this->form_validation->set_message('valid_nik', 'Negara harus dipilih.');
        return FALSE;
    }

    switch ($negara) {
        case '1': // Indonesia
            // if (!preg_match('/^\d{1,16}$/', $nik)) {
            //     $this->form_validation->set_message('valid_nik', 'No. Identitas untuk Indonesia harus berupa angka dan maksimal 16 digit.');
            //     return FALSE;
            // }
            if (!preg_match('/^\d{16}$/', $nik)) {
                $this->form_validation->set_message('valid_nik', 'No. Identitas untuk Indonesia harus berupa angka dan harus 16 digit.');
                return FALSE;
            }
            break;
        case '2': // Amerika Serikat
            if (!preg_match('/^[A-Z0-9]{10}$/', $nik)) {
                $this->form_validation->set_message('valid_nik', 'No. Identitas untuk Amerika Serikat harus berupa huruf dan angka, harus 10 karakter.');
                return FALSE;
            }
            break;
        case '3': // Malaysia
            if (!preg_match('/^\d{12}$/', $nik)) {
                $this->form_validation->set_message('valid_nik', 'No. Identitas untuk Malaysia harus berupa angka dan harus 12 digit.');
                return FALSE;
            }
            break;
        case '4': // Singapura
            if (!preg_match('/^\d{9}$/', $nik)) {
                $this->form_validation->set_message('valid_nik', 'No. Identitas untuk Singapura harus berupa angka dan harus 9 digit.');
                return FALSE;
            }
            break;
        case '5': // Jepang
            if (!preg_match('/^\d{12}$/', $nik)) {
                $this->form_validation->set_message('valid_nik', 'No. Identitas untuk Jepang harus berupa angka dan harus 12 digit.');
                return FALSE;
            }
            break;
        case '6': // Australia
            if (!preg_match('/^\d{10}$/', $nik)) {
                $this->form_validation->set_message('valid_nik', 'No. Identitas untuk Australia harus berupa angka dan harus 10 digit.');
                return FALSE;
            }
            break;
        case '7': // Jerman
            if (!preg_match('/^[A-Z0-9]{15}$/', $nik)) {
                $this->form_validation->set_message('valid_nik', 'No. Identitas untuk Jerman harus berupa huruf dan angka, harus 15 karakter.');
                return FALSE;
            }
            break;
        case '8': 
            break;
        default:
            $this->form_validation->set_message('valid_nik', 'Negara tidak valid.');
            return FALSE;
    }
    return TRUE;
  }
  public function modalEditPeneliti(){
    $nik       = $this->input->post('nik');
    $peneliti  = $this->ModelPeneliti->getEditPeneliti($nik);
    $data = array(
        'nik'  => $peneliti['nik'],
        'namalengkap' => $peneliti['nama'],
        'email' => $peneliti['email'],
        'notelp' => $peneliti['notelp'],
        'nama' => $peneliti['user_name'],
        'alamat' => $peneliti['alamat'],
        'pass' => $peneliti['pass'],
        'jk' => $peneliti['jk'],
    );

    $msg = array(
        'sukses' => $this->load->view('Penelitian/IndexPenelitiEdit', $data, true)
    );
    echo json_encode($msg); 
  }
  public function editData(){
    $this->db->db_select('db_rekammedis');
    $this->form_validation->set_rules('username', 'Username', 'required|callback_username_check|max_length[30]');
    $this->form_validation->set_rules('pass', 'Password', 'required|max_length[30]');
    $this->form_validation->set_rules(
        'nama',
        'Nama Peneliti',
        'required'
    );
    $this->form_validation->set_rules(
        'notelp',
        'Nomer Telpon',
        'required|max_length[20]'
    );
    $this->form_validation->set_rules(
        'email',
        'Email',
        'required|max_length[40]|valid_email',
        array('valid_email' => 'Masukan %s yang valid')
    );
    $this->form_validation->set_rules(
      'alamat',
      'Alamat Peneliti',
      'required'
    );
    $this->form_validation->set_rules(
      'jk',
      'Jenis Kelamin Peneliti',
      'required'
    );
    $this->form_validation->set_message('exact_length', '%s yang diinputkan jumlahnya tidak sesuai, silahkan input kembali');
    $this->form_validation->set_message('numeric', '%s yang diinputkan tidak sesuai format, silahkan input kembali dalam bentuk angka');
    $this->form_validation->set_message('required', '%s masih kosong, silakan di isi datanya ya');
    $this->form_validation->set_message('max_length', '%s yang diinputkan terlalu banyak, silahkan input kembali');
    
    if ($this->form_validation->run() == FALSE) {
        $errors = $this->form_validation->error_array();
        echo json_encode(['error' => $errors]);
    }else{
        $this->db->trans_start();
        $nik=$this->input->post('nik');
        $nama=$this->input->post('nama'); 
        $email=$this->input->post('email');              
        $notelp=$this->input->post('notelp');
        $username=$this->input->post('username');
        $pass=$this->input->post('pass');
        $jk=$this->input->post('jk');
        $alamat=$this->input->post('alamat');

          // tabel peneliti
        if (!empty($nama)) {
            $this->db->set('nama', $nama);
        }
        if (!empty($email)) {
            $this->db->set('email', $email);
        }
        if (!empty($notelp)) {
            $this->db->set('notelp', $notelp);
        }
        if (!empty($alamat)) {
          $this->db->set('alamat', $alamat);
        }

        $this->db->where('nik', $nik);
        $this->db->update('db_rekammedis.tb_peneliti');

        //tabel user
        if (!empty($username)) {
            $this->db->set('user_name', $username);
        }
        if (!empty($pass)) {
            $this->db->set('pass', $pass);
        }
        if (!empty($jk)) {
            $this->db->set('jk', $jk);
        }
        $this->db->where('nik', $nik);
        $this->db->update('db_rekammedis.tb_user_penelitian');
        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
        } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
        }
        echo json_encode($result);
    }
  }
  public function hapusPeneliti(){
    $this->db->trans_start();
    $stat=0;
    $nik=$this->input->post('nik');  
    
    $this->db->set('status', $stat);         
    $this->db->where('nik', $nik);
    $this->db->update('db_rekammedis.tb_peneliti');
    
    $this->db->set('status', $stat);          
    $this->db->where('nik', $nik);
    $this->db->update('db_rekammedis.tb_user_penelitian');
    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $result = array('status' => 'failed');
    } else {
        $this->db->trans_commit();
        $result = array('status' => 'success');
    }
    echo json_encode($result);
  }

  //penelitian
  public function indexPenelitianHome(){
    $data=array(      
      'isi'            => 'Penelitian/IndexPenelitianHome'
    );
    $this->load->view('Layout/Wrapper',$data);
  }

  public function indexPenelitian(){
    $this->load->view('Penelitian/IndexPenelitian');
  }
  
  public function indexPenelitianHistory(){
    $this->load->view('Penelitian/IndexPenelitianHistory');
  }

  public function simpanPenelitian(){
    $data = array(
        'nik'            => $this->input->post('nik'),
        'oleh'           => $this->input->post('petugas'),
        'tanggal'        => $this->input->post('tgl'),
        'tanggal_akhir'  => $this->input->post('tglak'),
        'status'    => 1,
        
    );
    $this->db->insert('db_rekammedis.tb_datapenelitian', $data);
    $insert_id = $this->db->insert_id();

    $norm = $this->input->post('norm');    
    $dataDetailToInsert = array();
    foreach ($norm as $key) {
        $dataDetailToInsert[] = array(
            'no_data'  => $insert_id, 
            'oleh'     => $this->input->post('petugas'),
            'nomr'     => $key,
            'status'   => 1,
        );
    }
    $this->db->insert_batch('db_rekammedis.tb_datapenelitian_detail', $dataDetailToInsert);
    if ($this->db->trans_status() === FALSE) {
        $this->db->trans_rollback();
        $response = array('status' => 'error', 'message' => 'Database transaction failed.');
    } else {
        $this->db->trans_commit();
        $response = array('status' => 'success');
    }

    
    echo json_encode($response);
  }
  public function getTblDataPenelitian(){
    $listdata = $this->ModelPeneliti->getDataPenelitian();
    $data=array();
    $no =1;    
    foreach ($listdata->result() as $field) { 
        // $today = date_create(date('Y-m-d'));;
        // $tanggalAkhir = date_create($field->tanggal_akhir);
        // $sisaHari1 = date_diff($today, $tanggalAkhir); 
        // $sisaHari =  $sisaHari1->format("%r%a");
        $today = new DateTime();
        $tanggalAkhir = new DateTime($field->tanggal_akhir);
        $sisaHari = $today->diff($tanggalAkhir)->days; 
        if ($sisaHari < 5) {
            $status = '<span class="badge bg-danger text-light">' . $sisaHari . ' hari</span>'; // Merah, teks putih
        } elseif ($sisaHari <= 20) {
            $status = '<span class="badge bg-warning text-light">' . $sisaHari . ' hari</span>'; // Kuning, teks putih
        } else {
            $status = '<span class="badge bg-success text-light">' . $sisaHari . ' hari</span>'; // Hijau, teks putih
        }
      $button = '<button type="button" class="btn btn-primary btn-rounded btn-sm" title="Edit Form" onclick="editData('.$field->no_data.')" style="width:72px"><i class="fas fa-pencil-alt"></i></button>';
      $hapus = '<button type="button" class="btn btn-danger btn-rounded btn-sm hapusAkses" onclick="hapusAkses(\'' . $field->no_data . '\', \'' . $field->nik . '\', \'' . $field->nama . '\')" id="hapusAkses" title="Hapus Akses" style="width:72px"><i class="fa fa-trash"></i></button>';
      
      $data[] = array(
        $no,
        !empty($field->tanggal) ? date('d/m/Y', strtotime($field->tanggal)) : '', 
        $field->nik,
        $field->nama,
        $field->nomr,        
        !empty($field->tanggal_akhir) ? date('d/m/Y', strtotime($field->tanggal_akhir)) : '',
        $status,
        $button.'.'.$hapus,
      );
      $no++;
    }

    $output = array(
      "data"            => $data
    );
    echo json_encode($output);
  }
  public function modalEdit(){
    $no_data       = $this->input->post('id');
    $penelitian  = $this->ModelPeneliti->getEditPenelitian($no_data);
    $data = array(
        'no_data' => $no_data,
        'nik'  => $penelitian ['nik'],
        'namalengkap' => $penelitian ['nama'],
        'tanggal' => $penelitian ['tanggal'],
        'tanggalAk' => $penelitian ['tanggal_akhir'],
    );

    $msg = array(
        'sukses' => $this->load->view('Penelitian/IndexPenelitianEdit', $data, true)
    );
    echo json_encode($msg); 
  }
  public function checkNormPasien() {
    $normPasien = $this->input->post('normPasien'); 
    $no_data = $this->input->post('no_data'); 
    $this->db->where_in('nomr', $normPasien); 
    $this->db->where('no_data', $no_data); 
    $this->db->where('status', 1);
    $exists = $this->db->get('db_rekammedis.tb_datapenelitian_detail');
    
    $existingNomrs = [];
    if ($exists->num_rows()> 0) {
        $existingNomrs = array_column($exists->result_array(), 'nomr');
        $result = array('status' => 'failed', 'message' => 'Data akses pasien dengan nomr yang dipilih sudah ada.', 'existingNomrs' => $existingNomrs);
        echo json_encode($result);
    } else {
        echo json_encode(array('status' => 'success'));
    }
  } 


  public function editPenelitian(){
    $no_data = $this->input->post('no_data');
    $data = array(
        'tanggal'        => $this->input->post('tgl'),
        'tanggal_akhir'  => $this->input->post('tglak'),  
        'update_oleh'    => $this->session->userdata('id'),
    );
    $this->db->where('no_data', $no_data);
    $this->db->update('db_rekammedis.tb_datapenelitian', $data);

    $norm = $this->input->post('normPasien');   
    $dataDetailToInsert = array();
    if(!empty($norm)){
        foreach ($norm as $key) {
            $dataDetailToInsert[] = array(
                'no_data'  => $no_data, 
                'oleh'    => $this->session->userdata('id'),
                'nomr'     => $key,
                'status'   => 1,
            );
        }
        $this->db->insert_batch('db_rekammedis.tb_datapenelitian_detail', $dataDetailToInsert);
    } 
    if ($this->db->trans_status() === FALSE) {
        $this->db->trans_rollback();
        $response = array('status' => 'error', 'message' => 'Database transaction failed.');
    } else {
        $this->db->trans_commit();
        $response = array('status' => 'success');
    }    
    echo json_encode($response);
  }
  public function tblDataAkses(){
    $no_data       = $this->input->post('no_data');
    $listdata = $this->ModelPeneliti->getDataAkses($no_data);
    
    $data=array();
    $no =1;
    foreach ($listdata->result() as $field) {      
    $hapus = '<button type="button" class="btn btn-danger btn-rounded btn-sm hapusAkses" onclick="hapus(\'' . $field->no_detail . '\', \'' . $field->nomrNama . '\')" id="hapus" title="Hapus Akses" style="width:72px"><i class="fa fa-trash"></i></button>';

      $data[] = array(
        $no,
        $field->nama_lengkap,
        $field->nomrNama,
        $hapus,
        );        
      $no++;
    }

    $output = array(
      "data"            => $data
    );
    echo json_encode($output);
  }
  public function hapusDataAkses(){
    $id = $this->input->post('id');
    $this->db->trans_begin();
    $this->db->set('status', 0);
    $this->db->where('no_detail', $id); 
    $this->db->update('db_rekammedis.tb_datapenelitian_detail');
    if ($this->db->trans_status() === false) {
        $this->db->trans_rollback();
        $response = array('status' => 'failed');
    } else {
        $this->db->trans_commit();
        $response = array('status' => 'success');
    }
    echo json_encode($response);

  }
  public function hapusPenelitiAkses(){
    $id = $this->input->post('id');
    $this->db->set('status', 0);
    $this->db->where('no_data', $id);
    $this->db->update('db_rekammedis.tb_datapenelitian');

    $this->db->set('status', 0);
    $this->db->where('no_data', $id);
    $this->db->update('db_rekammedis.tb_datapenelitian_detail');
    $response = array('status' => 'success');
    echo json_encode($response);
  }
  public function getTblHistoryPenelitian(){
    $listdata = $this->ModelPeneliti->getTblHistory();    
    $data=array();
    $no =1;
    foreach ($listdata->result() as $field) {   
      $data[] = array(
        $no,
        !empty($field->tanggal) ? date('d/m/Y', strtotime($field->tanggal)) : '', 
        !empty($field->tanggal_akhir) ? date('d/m/Y', strtotime($field->tanggal_akhir)) : '',
        $field->namaPetugas1,
        $field->namaPetugas2,
        $field->nik,
        $field->nama,      
        $field->nomrNama,
      );        
      $no++;
    }

    $output = array(
      "data"            => $data
    );
    echo json_encode($output);
  }


    
  

}
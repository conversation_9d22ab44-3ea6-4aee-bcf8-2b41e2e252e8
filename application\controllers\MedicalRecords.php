<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class MedicalRecords extends CI_Controller {

    public function __construct()
    {
        parent::__construct();
        if($this->session->userdata('logged_in') == FALSE ){
            redirect('login');  
        }
        date_default_timezone_set("Asia/Bangkok");
        $this->load->model(array('ModelMedicalRecords'));
    }

    public function index()
    {
        $status_ds = $this->session->userdata('status_ds');
        
        $data = array(
            'status_ds' => $status_ds,
            'isi' => 'MedicalRecords/Index'
        );
        $this->load->view('Layout/Wrapper', $data);
    }

    // Data untuk tab Transaksi Koder
    public function get_coder_transaksi()
    {
        $start = $this->input->post('start');
        $length = $this->input->post('length');
        $searchValue = $this->input->post('search')['value'];
        $tanggal_awal = $this->input->post('tanggal_awal');
        $tanggal_akhir = $this->input->post('tanggal_akhir');

        // Sorting
        $orderColumn = $this->input->post('order')[0]['column'];
        $orderDir = $this->input->post('order')[0]['dir'];
        $columns = ['no', 'nomr', 'nama', 'nosep', 'ruangan', 'tgl_masuk', 'tgl_keluar', '', ''];
        $orderBy = isset($columns[$orderColumn]) && $columns[$orderColumn] != 'no' ? $columns[$orderColumn] : 'tgl_masuk';

        $result = $this->ModelMedicalRecords->getCoderTransaksi($start, $length, $searchValue, $tanggal_awal, $tanggal_akhir, $orderBy, $orderDir);

        $data = array();
        $no = $start + 1;

        foreach ($result['data'] as $field) {
            // Tentukan URL resume berdasarkan ID_RUANGAN
            if (in_array($field->ID_RUANGAN, ['105011201', '105140101'])) {
                $resume_url = 'http://192.168.7.103/reports/resume_igd/resume_igd.php?format=pdf&nopen=' . $field->NOPEN;
            } else {
                $resume_url = 'http://192.168.7.103/reports/resume_medis/V2.0/resume_pulang.php?format=pdf&nopen=' . $field->NOPEN;
            }

            $resume_btn = '<a href="' . $resume_url . '" target="_blank" class="btn btn-sm btn-info btn-block"><i class="fa fa-eye"></i> Preview</a>';

            $komen_select = '<select class="form-control komen-select" data-nopen="' . $field->NOPEN . '" data-nosep="' . $field->NOSEP . '">
                                <option value="" disabled selected>Pilih Status</option>
                                <option value="1">Lengkap</option>
                                <option value="2">Tidak Lengkap</option>
                            </select>';

            $sub_array = array();
            $sub_array[] = $no;
            $sub_array[] = $field->nomr;
            $sub_array[] = $field->nama;
            $sub_array[] = $field->NOSEP;
            $sub_array[] = $field->ruang_rawat_terakhir;
            $sub_array[] = date('d-m-Y', strtotime($field->tgl_masuk));
            $sub_array[] = date('d-m-Y', strtotime($field->tgl_keluar));
            $sub_array[] = $resume_btn;
            $sub_array[] = $komen_select;

            $data[] = $sub_array;
            $no++;
        }

        $output = array(
            "draw" => intval($this->input->post('draw')),
            "recordsTotal" => $result['recordsTotal'],
            "recordsFiltered" => $result['recordsFiltered'],
            "data" => $data
        );

        echo json_encode($output);
    }

    // Data untuk tab History Koder
    public function get_coder_history()
    {
        $start = $this->input->post('start');
        $length = $this->input->post('length');
        $searchValue = $this->input->post('search')['value'];
        $tanggal_awal = $this->input->post('tanggal_awal');
        $tanggal_akhir = $this->input->post('tanggal_akhir');

        // Sorting
        $orderColumn = $this->input->post('order')[0]['column'];
        $orderDir = $this->input->post('order')[0]['dir'];
        $columns = ['no', 'nomr', 'nama', 'sep', 'ruangan', 'tgl_masuk', 'tgl_keluar', '', ''];
        $orderBy = isset($columns[$orderColumn]) && $columns[$orderColumn] != 'no' ? $columns[$orderColumn] : 'tgl_masuk';

        $result = $this->ModelMedicalRecords->getCoderHistory($start, $length, $searchValue, $tanggal_awal, $tanggal_akhir, $orderBy, $orderDir);

        $data = array();
        $no = $start + 1;

        foreach ($result['data'] as $field) {
            // Ambil komentar dokter menggunakan function baru
            $komentar_dokter_detail = $this->ModelMedicalRecords->getKomentarDokterGabungan($field->ID);
            
            // Tentukan URL resume berdasarkan ID_RUANGAN
            if (in_array($field->ID_RUANGAN, ['105011201', '105140101'])) {
                $resume_url = 'http://192.168.7.103/reports/resume_igd/resume_igd.php?format=pdf&nopen=' . $field->NOPEN;
            } else {
                $resume_url = 'http://192.168.7.103/reports/resume_medis/V2.0/resume_pulang.php?format=pdf&nopen=' . $field->NOPEN;
            }

            $resume_btn = '<a href="' . $resume_url . '" target="_blank" class="btn btn-sm btn-info btn-block"><i class="fa fa-eye"></i> Preview</a>';

            // Status badge
            $status_badge = '';
            switch ($field->STATUS) {
                case 1:
                    $status_badge = '<span class="badge badge-success">LENGKAP</span>';
                    break;
                case 2:
                    $status_badge = '<span class="badge badge-success">LENGKAP DOKTER</span>';
                    break;
                case 3:
                    $status_badge = '<span class="badge badge-warning">TIDAK LENGKAP</span>';
                    break;
            }
            // Icon info untuk komentar dokter - hanya untuk coder di history coder
            $info_icon = '';
            if (!empty($komentar_dokter_detail)) {
                // Gunakan function model untuk cek status
                $status_result = $this->ModelMedicalRecords->getMinStatusKomentarDokter($field->ID);
                
                $bg_color = ($status_result && $status_result->min_status == 1) ? '#dc3545' : '#28a745'; // merah jika ada yang belum dibaca, hijau jika semua sudah
                $checkbox_checked = ($status_result && $status_result->min_status == 1) ? '' : 'checked';
                
                $info_icon = ' <i class="fa fa-info-circle komentar-dokter-info-icon" 
                                  style="color: #ffc107; cursor: pointer;" 
                                  data-id-resume="' . $field->ID . '"
                                  data-komentar="' . htmlspecialchars($komentar_dokter_detail, ENT_QUOTES) . '"
                                  data-checkbox="' . $checkbox_checked . '"
                                  data-bg-color="' . $bg_color . '"
                                  title="Klik untuk lihat komentar dokter"></i>';
            }

            // Status 2 = sudah ACC dokter, disable delete button
            $delete_button = '';
            if ($field->STATUS == 2) {
                $delete_button = '<span class="text-muted ml-2" title="Sudah di ACC dokter, tidak dapat dihapus" style="cursor: not-allowed;">✖</span>';
            } else {
                $delete_button = '<a href="javascript:void(0);" class="text-danger btn-delete-history ml-2" data-id="' . htmlspecialchars($field->ID, ENT_QUOTES) . '" title="Hapus">✖</a>';
            }

            $action_buttons = '
                <a href="javascript:void(0);" class="text-primary btn-edit-history ml-2" data-id="' . htmlspecialchars($field->ID, ENT_QUOTES) . '" data-nopen="' . htmlspecialchars($field->NOPEN, ENT_QUOTES) . '" data-sep="' . htmlspecialchars($field->SEP, ENT_QUOTES) . '" title="Edit">✎</a>
                ' . $delete_button . '
            ';

            $sub_array = array();
            $sub_array[] = $no;
            $sub_array[] = $field->nomr;
            $sub_array[] = $field->nama . $info_icon . ' ' . $action_buttons;
            $sub_array[] = $field->SEP;
            $sub_array[] = $field->ruang_rawat_terakhir;
            $sub_array[] = date('d-m-Y', strtotime($field->tgl_masuk));
            $sub_array[] = date('d-m-Y', strtotime($field->tgl_keluar));
            $sub_array[] = $resume_btn;
            $sub_array[] = $status_badge;

            $data[] = $sub_array;
            $no++;
        }

        $output = array(
            "draw" => intval($this->input->post('draw')),
            "recordsTotal" => $result['recordsTotal'],
            "recordsFiltered" => $result['recordsFiltered'],
            "data" => $data
        );

        echo json_encode($output);
    }

    // Data untuk tab Dokter
    public function get_dokter_data()
    {
        $start = $this->input->post('start');
        $length = $this->input->post('length');
        $searchValue = $this->input->post('search')['value'];
        $tanggal_awal = $this->input->post('tanggal_awal');
        $tanggal_akhir = $this->input->post('tanggal_akhir');
        $history = $this->input->post('history'); 
        $ruangan = $this->input->post('ruangan');

        // Sorting
        $orderColumn = $this->input->post('order')[0]['column'];
        $orderDir = $this->input->post('order')[0]['dir'];
        
        if ($history) {
            // Untuk history dokter (tanpa kolom aksi)
            $columns = ['no', 'pasien', 'sep', 'ruangan', 'tgl_masuk', 'tgl_keluar', 'dokter'];
        } else {
            // Untuk review dokter (dengan kolom aksi)
            $columns = ['no', 'pasien', 'sep', 'ruangan', 'tgl_masuk', 'tgl_keluar', 'dokter', '', ''];
        }
        
        $orderBy = isset($columns[$orderColumn]) && $columns[$orderColumn] != 'no' ? $columns[$orderColumn] : 'tgl_masuk';

        $result = $this->ModelMedicalRecords->getDokterData($start, $length, $searchValue, $tanggal_awal, $tanggal_akhir, $orderBy, $orderDir, $history, $ruangan);

        $data = array();
        $no = $start + 1;

        foreach ($result['data'] as $field) {
            // Tentukan URL resume berdasarkan ID_RUANGAN
            if (in_array($field->ID_RUANGAN, ['105011201', '105140101'])) {
                $resume_url = 'http://192.168.7.103/reports/resume_igd/resume_igd.php?format=pdf&nopen=' . $field->NOPEN;
            } else {
                $resume_url = 'http://192.168.7.103/reports/resume_medis/V2.0/resume_pulang.php?format=pdf&nopen=' . $field->NOPEN;
            }

            $sub_array = array();
            $sub_array[] = $no;
            $sub_array[] = $field->nama . ' [' . $field->nomr . ']';
            $sub_array[] = $field->SEP;
            $sub_array[] = $field->ruang_rawat_terakhir;
            $sub_array[] = date('d-m-Y', strtotime($field->tgl_masuk));
            $sub_array[] = date('d-m-Y', strtotime($field->tgl_keluar));

            if (!$history) {
                // Review Dokter: tampilkan kolom dokter
                $sub_array[] = $field->dokter;
                $resume_btn = '<button class="btn btn-sm btn-info btn-modal-revisi" data-id="' . $field->ID . '">Resume</button>';
                $sub_array[] = $resume_btn;
            } else {
                // History Dokter: ambil data resume dan komentar dokter menggunakan function baru
                $data_resume = $this->ModelMedicalRecords->getDataResumeGabungan($field->ID);
                $data_resume = !empty($data_resume) ? $data_resume : '<i class="text-muted">Tidak ada komentar</i>';
                
                $komentar_dokter = $this->ModelMedicalRecords->getKomentarDokterGabungan($field->ID);
                
                // Tambahkan icon komentar dokter jika ada - untuk dokter di history dokter
                $pasien_column = $field->nama . ' [' . $field->nomr . ']';
                if (!empty($komentar_dokter)) {
                    // Gunakan function model untuk mendapatkan min ID
                    $min_id = $this->ModelMedicalRecords->getMinIdKomentarDokter($field->ID);
                    
                    // Dapatkan status komentar dokter individual
                    $status_komentar = $this->ModelMedicalRecords->getStatusKomentarDokterIndividual($min_id);
                    
                    // Tentukan warna berdasarkan status: 1 = merah (belum dibaca), 2 = hijau (sudah dibaca)
                    $bg_color = ($status_komentar == 1) ? '#dc3545' : '#28a745';
                    $escaped_comment = htmlspecialchars($komentar_dokter, ENT_QUOTES, 'UTF-8');
                    
                    $pasien_column .= ' <i class="fa fa-info-circle komentar-dokter-info-icon" 
                                          style="color: #ffc107; cursor: pointer;" 
                                          data-id-resume="' . $field->ID . '"
                                          data-id-detail="' . $min_id . '"
                                          data-komentar="' . $escaped_comment . '"
                                          data-hapus="true"
                                          data-bg-color="' . $bg_color . '"
                                          title="Klik untuk lihat komentar dokter"></i>';
                }
                
                $sub_array[1] = $pasien_column; // Update kolom pasien dengan icon komentar dokter
                $sub_array[] = $data_resume;
                // Tambahkan tombol Resume (preview)
                $resume_btn = '<a href="' . $resume_url . '" target="_blank" class="btn btn-sm btn-info"><i class="fa fa-eye"></i> Resume</a>';
                $sub_array[] = $resume_btn;
            }

            $data[] = $sub_array;
            $no++;
        }

        $output = array(
            "draw" => intval($this->input->post('draw')),
            "recordsTotal" => $result['recordsTotal'],
            "recordsFiltered" => $result['recordsFiltered'],
            "data" => $data
        );

        echo json_encode($output);
    }

    // Proses komentar dari koder
    public function proses_komen()
    {
        $nopen = $this->input->post('nopen');
        $nosep = $this->input->post('nosep');
        $status = $this->input->post('status');
        $id_simpel = $this->session->userdata('id_simpel');

        $data_resume = array(
            'OLEH' => $id_simpel,
            'NOPEN' => $nopen,
            'SEP' => $nosep,
            'TANGGAL' => date('Y-m-d H:i:s'),
            'STATUS' => $status == '1' ? 1 : 3  // 1 = Lengkap, 3 = Tidak Lengkap
        );

        $insert_id = $this->ModelMedicalRecords->insertResumeRi($data_resume);

        if ($insert_id && $status == '2') {
            // Jika tidak lengkap (status = 2), return data untuk modal ICD
            $modal_data = $this->ModelMedicalRecords->getModalData($nopen);
            $diagnosa_utama = $this->ModelMedicalRecords->getDiagnosaUtama($nopen);
            $diagnosa_sekunder = $this->ModelMedicalRecords->getDiagnosaSekunder($nopen);
            $prosedur = $this->ModelMedicalRecords->getProsedur($nopen);

            echo json_encode(array(
                'status' => 'success',
                'show_modal' => true,
                'id_resume' => $insert_id,
                'modal_data' => $modal_data,
                'diagnosa_utama' => $diagnosa_utama,
                'diagnosa_sekunder' => $diagnosa_sekunder,
                'prosedur' => $prosedur
            ));
        } else {
            echo json_encode(array('status' => 'success', 'show_modal' => false));
        }
    }

    // Simpan komentar ICD
    public function simpan_komentar_icd()
    {
        $id_resume = $this->input->post('id_resume');
        $code = $this->input->post('code');
        $comment = $this->input->post('comment');
        $jenis = $this->input->post('jenis');
        $status = $this->input->post('status');

        // Check if comment already exists
        $existing = $this->ModelMedicalRecords->getExistingCommentByCode($id_resume, $code, $jenis);

        if ($existing) {
            // Update existing comment
            $data = array(
                'COMENT' => $comment,
                'TGL_INPUT' => date('Y-m-d H:i:s'),
                'STATUS' => $status
            );

            $where = array(
                'ID_RESUME' => $id_resume,
                'CODE' => $code,
                'JENIS' => $jenis
            );

            $result = $this->ModelMedicalRecords->updateResumeRiDetail($data, $where);
        } else {
            // Insert new comment
            $data = array(
                'ID_RESUME' => $id_resume,
                'CODE' => $code,
                'COMENT' => $comment,
                'TGL_INPUT' => date('Y-m-d H:i:s'),
                'JENIS' => $jenis
            );

            $result = $this->ModelMedicalRecords->insertResumeRiDetail($data);
        }

        if ($result) {
            echo json_encode(array('status' => 'success'));
        } else {
            echo json_encode(array('status' => 'error'));
        }
    }

    // Update status komentar ICD (hapus komentar, set status=0)
    public function update_status_komentar_icd()
    {
        $id_resume = $this->input->post('id_resume');
        $code = $this->input->post('code');
        $jenis = $this->input->post('jenis');
        $status = $this->input->post('status');

        $data = array(
            'STATUS' => $status
        );

        $where = array(
            'ID_RESUME' => $id_resume,
            'CODE' => $code,
            'JENIS' => $jenis
        );

        $result = $this->ModelMedicalRecords->updateResumeRiDetail($data, $where);

        if ($result) {
            echo json_encode(array('status' => 'success'));
        } else {
            echo json_encode(array('status' => 'error'));
        }
    }

    // Get existing comment
    public function get_existing_comment()
    {
        $id_resume = $this->input->post('id_resume');
        $code = $this->input->post('code');
        $jenis = $this->input->post('jenis');

        $result = $this->ModelMedicalRecords->getExistingComment($id_resume, $jenis, $code);

        echo json_encode($result);
    }

    // Finalisasi resume
    public function finalisasi_resume()
    {
        $id_resume = $this->input->post('id_resume');

        $data = array('STATUS' => 3);
        $where = array('ID' => $id_resume);

        $result = $this->ModelMedicalRecords->updateResumeRi($data, $where);

        if ($result) {
            echo json_encode(array('status' => 'success'));
        } else {
            echo json_encode(array('status' => 'error'));
        }
    }

    // Get data untuk modal revisi dokter
    public function get_revisi_data()
    {
        $id = $this->input->post('id');

        $modal_data = $this->ModelMedicalRecords->getRevisiModalData($id);
        $diagnosa_utama = $this->ModelMedicalRecords->getRevisiDiagnosaUtama($id);
        $diagnosa_sekunder = $this->ModelMedicalRecords->getRevisiDiagnosaSekunder($id);
        $prosedur = $this->ModelMedicalRecords->getRevisiProsedur($id);

        echo json_encode(array(
            'modal_data' => $modal_data,
            'diagnosa_utama' => $diagnosa_utama,
            'diagnosa_sekunder' => $diagnosa_sekunder,
            'prosedur' => $prosedur
        ));
    }

    // Get data untuk edit history modal
    public function get_edit_history_data()
    {
        $id_resume = $this->input->post('id_resume');

        // Get basic modal data
        $modal_data = $this->ModelMedicalRecords->getEditHistoryModalData($id_resume);
        
        // Get ICD diagnosa utama dari database asli
        $diagnosa_utama = $this->ModelMedicalRecords->getDiagnosaUtama($modal_data->NOPEN);
        
        // Get ICD diagnosa sekunder dari database asli  
        $diagnosa_sekunder = $this->ModelMedicalRecords->getDiagnosaSekunder($modal_data->NOPEN);
        
        // Get ICD prosedur dari database asli
        $prosedur = $this->ModelMedicalRecords->getProsedur($modal_data->NOPEN);

        // Get existing comments dari resume_ri_detail (untuk koder, tampilkan semua)
        $existing_comments = $this->ModelMedicalRecords->getExistingCommentsForEdit($id_resume);
        
        // Get data yang sudah ada di resume_ri_detail (untuk status button)
        $coder_diagnosa_utama = $this->ModelMedicalRecords->getCoderDiagnosaUtama($id_resume);
        $coder_diagnosa_sekunder = $this->ModelMedicalRecords->getCoderDiagnosaSekunder($id_resume);
        $coder_prosedur = $this->ModelMedicalRecords->getCoderProsedur($id_resume);

        echo json_encode(array(
            'status' => 'success',
            'show_modal' => true,
            'id_resume' => $id_resume,
            'modal_data' => $modal_data,
            'diagnosa_utama' => $diagnosa_utama,
            'diagnosa_sekunder' => $diagnosa_sekunder,
            'prosedur' => $prosedur,
            'existing_comments' => $existing_comments,
            'coder_diagnosa_utama' => $coder_diagnosa_utama,
            'coder_diagnosa_sekunder' => $coder_diagnosa_sekunder,
            'coder_prosedur' => $coder_prosedur
        ));
    }

    // Update status dokter
    public function update_status_dokter()
    {
        $id = $this->input->post('id');
        $komentar_dokter = $this->input->post('komentar_dokter');
        $id_simpel = $this->session->userdata('id_simpel');

        $data = array(
            'UPDATE_OLEH' => $id_simpel,
            'STATUS' => 2,
            'TANGGAL_UPDATE' => date('Y-m-d H:i:s')
        );

        $where = array('ID' => $id);

        $result = $this->ModelMedicalRecords->updateResumeRi($data, $where);

        // Update status resume_ri_detail: STATUS = 2 dan OLEH = $id_simpel
        if ($result) {
            $this->ModelMedicalRecords->updateStatusDokterResumeRiDetail($id, $id_simpel);
        }

        // Jika ada komentar revisi dokter, simpan ke tabel resume_ri_detailDokter
        if ($result && !empty($komentar_dokter)) {
            $komentar_data = array(
                'ID_RESUME' => $id,
                'COMENT' => $komentar_dokter,
                'TGL_INPUT' => date('Y-m-d H:i:s')
            );
            
            $this->ModelMedicalRecords->insertResumeRiDetailDokter($komentar_data);
        }

        if ($result) {
            echo json_encode(array('status' => 'success'));
        } else {
            echo json_encode(array('status' => 'error'));
        }
    }

    // Approve resume oleh dokter (untuk checkbox)
    public function approve_resume()
    {
        $id = $this->input->post('id');
        $id_simpel = $this->session->userdata('id_simpel');

        $data = array(
            'UPDATE_OLEH' => $id_simpel,
            'STATUS' => 2,
            'TANGGAL_UPDATE' => date('Y-m-d H:i:s')
        );

        $where = array('ID' => $id);
        $result = $this->ModelMedicalRecords->updateResumeRi($data, $where);

        if ($result) {
            echo json_encode(array('status' => 'success', 'message' => 'Data berhasil disetujui'));
        } else {
            echo json_encode(array('status' => 'error', 'message' => 'Gagal menyetujui data'));
        }
    }
     public function hapus_resume_ri()
    {
        $id_resume = $this->input->post('id_resume');
        
        // Menggunakan method hapusResumeRi yang sudah ada untuk update kedua tabel sekaligus
        // Update STATUS=0 pada db_rekammedis.resume_ri dan db_rekammedis.resume_ri_detail
        $result = $this->ModelMedicalRecords->hapusResumeRi($id_resume);

        if ($result) {
            echo json_encode(['status' => 'success']);
        } else {
            echo json_encode(['status' => 'error']);
        }
    }
//    public function cek_komentar_resume_detail() {
//         $id_resume = $this->input->post('id_resume');
//         $ada = $this->ModelMedicalRecords->cekKomentarResumeDetail($id_resume);
//         if ($ada) {
//             echo json_encode(['status' => 'ok']);
//         } else {
//             echo json_encode(['status' => 'error', 'message' => 'Belum ada data komentar lengkap!']);
//         }
//     }
    public function cek_komentar_resume_detail() {
        $id_resume = $this->input->post('id_resume');
        $status = $this->ModelMedicalRecords->cekKomentarResumeDetail($id_resume);
        echo json_encode(['status' => $status]);
    }
    
    public function get_ruangan_list()
    {
        $history = $this->input->get('history') == '1' ? true : false;
        $ruangan_list = $this->ModelMedicalRecords->getRuanganList($history);

        $data = array();
        foreach ($ruangan_list as $ruangan) {
            $data[] = array(
                'id' => $ruangan->ruangan,
                'text' => $ruangan->ruangan
            );
        }

        echo json_encode($data);
    }

    // Get total komentar dokter untuk badge
    public function get_total_komentar_dokter()
    {
        $tanggal_awal = $this->input->post('tanggal_awal');
        $tanggal_akhir = $this->input->post('tanggal_akhir');
        
        $total = $this->ModelMedicalRecords->getTotalKomentarDokter($tanggal_awal, $tanggal_akhir);
        
        echo json_encode(array('total' => $total));
    }

    // Get total komentar coder untuk badge
    public function get_total_komentar_coder()
    {
        $tanggal_awal = $this->input->post('tanggal_awal');
        $tanggal_akhir = $this->input->post('tanggal_akhir');
        
        $total = $this->ModelMedicalRecords->getTotalKomentarCoder($tanggal_awal, $tanggal_akhir);
        
        echo json_encode(array('total' => $total));
    }

    // Update status detail dokter (untuk checkbox coder dan hapus dokter)
    public function update_status_detail_dokter()
    {
        $id_detail = $this->input->post('id_detail');
        $status = $this->input->post('status'); // 0 = hapus, 2 = sudah dibaca coder
        
        $data = array('STATUS' => $status);
        $where = array('ID' => $id_detail);
        
        $result = $this->ModelMedicalRecords->updateResumeRiDetailDokter($data, $where);
        
        if ($result) {
            echo json_encode(array('status' => 'success'));
        } else {
            echo json_encode(array('status' => 'error'));
        }
    }
     public function hapus_status_detail_dokter()
    {
        $idResume = $this->input->post('idResume');
        $status = $this->input->post('status'); 
        
        $data = array('STATUS' => $status, 'OLEH' => $this->session->userdata('id_simpel'));
        $where = array('ID_RESUME' => $idResume);
        
        $result = $this->ModelMedicalRecords->hapusResumeRiDetailDokter($data, $where);
        
        if ($result) {
            echo json_encode(array('status' => 'success'));
        } else {
            echo json_encode(array('status' => 'error'));
        }
    }

    // Update status komentar dokter berdasarkan ID_RESUME (untuk checkbox sudah dibaca)
    public function update_status_komentar_by_resume()
    {
        $id_resume = $this->input->post('id_resume');
        $checked = $this->input->post('checked'); // true/false
        $status = $checked == 'true' ? 2 : 1; // 2 = sudah dibaca, 1 = belum dibaca
        
        $result = $this->ModelMedicalRecords->updateStatusKomentarDokterByResume($id_resume, $status);
        
        if ($result) {
            echo json_encode(array('status' => 'success'));
        } else {
            echo json_encode(array('status' => 'error'));
        }
    }


}

/* End of file MedicalRecords.php */
/* Location: ./application/controllers/MedicalRecords.php */
